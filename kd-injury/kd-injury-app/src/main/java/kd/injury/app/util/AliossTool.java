package kd.injury.app.util;

import kd.common.tool.AliOssToolV3;
import kd.common.tool.JsonBizTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

public class AliossTool {

    private static final Logger logger = LoggerFactory.getLogger(AliossTool.class.getName());
    /**
     * URL上传文件 返回objectId
     * @param path 链接地址（url）
     * @param objectId oss文件地址
     * @return objectId
     */
    public static String uploadOss(String path, String objectId) {
        InputStream is = null;
        ByteArrayOutputStream os = null;
        HttpURLConnection con = null;
        byte[] buff = null;
        try {
            URL imgUrl = new URL(path);
            con = (HttpURLConnection) imgUrl.openConnection();
            if (302 == con.getResponseCode()) {
                String location = con.getHeaderField("location");
                URL url = new URL(location);
                con = (HttpURLConnection) url.openConnection();
            }
            is = con.getInputStream();
            os = new ByteArrayOutputStream();
            buff = new byte[4096];
            int len;
            while ((len = is.read(buff)) != -1) {
                os.write(buff, 0, len);
            }

            // 上传oss
            AliOssToolV3.uploadByByteArrayOuter(AliOssToolV3.PRIVATE_BUCKET_ID, objectId, os.toByteArray(), con.getContentType());
        } catch (IOException e) {
            e.printStackTrace();
            return JsonBizTool.genJson("-1", "影像下载出错,url:" + path);
        } finally {
            if (is != null) {
                try {
                    // 关闭inputStream流
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (os != null) {
                try {
                    //关闭outputStream流
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (con != null) {
                try {
                    con.disconnect();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return objectId;
    }

    /**
     * 上传本地文件到oss
     * @param path 文件路径
     * @param objectId oss地址
     * @return objectId
     */
    public static String uploadLocal(String path, String objectId) {
        File file = new File(path);
        byte[] bytes = getFileByteArray(file);
        AliOssToolV3.uploadByByteArrayOuter(AliOssToolV3.PRIVATE_BUCKET_ID, objectId, bytes, "application/pdf");
        return objectId;
    }

    /**
     * 获取文件的 bytes
     * @param file
     * @return
     */
    public static byte[] getFileByteArray(File file) {
        if (file.length() > Integer.MAX_VALUE) {
            logger.info("===>> file too big... ");
            return null;
        }
        byte[] buf = null;
        try (FileInputStream in = new FileInputStream(file);){
            buf = new byte[(int) file.length()];
            int offset = 0;
            int numRead = 0;
            while (offset < buf.length && (numRead = in.read(buf,offset, buf.length - offset))>=0 ) {
                offset += numRead;
            }

            if (offset != buf.length) {
                throw new IOException("文件未全部读取" + file.getName());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return buf;
    }
}
