package kd.injury.app.controller;

import kd.common.pager.PageParam;
import kd.injury.app.model.dto.InjuryCaseDTO;
import kd.injury.app.model.dto.PageDTO;
import kd.injury.app.model.dto.ResponseResult;
import kd.injury.app.model.req.CaseRequest;
import kd.injury.app.model.vo.CaseInfoVo;
import kd.injury.app.model.vo.InjuryCaseVO;
import kd.injury.app.service.CaseAttachmentService;
import kd.injury.app.service.CaseInfoService;
import kd.injury.app.util.Tool;
import kd.injury.common.constants.AccidentTag;
import kd.injury.common.constants.CaseStatus;
import kd.injury.dao.entity.CaseAttachment;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/cases")
public class TestController {


    private final CaseInfoService caseInfoService;
    private final CaseAttachmentService caseAttachmentService;

    @GetMapping("/test")
    public void test() {
        System.out.println("Hello test");
    }

    @PostMapping("/test")
    public ResponseResult<String> testClaimTask(@RequestParam String caseId) {
        System.out.println(caseId);
        return  ResponseResult.success() ;
    }

//    @RequestMapping("/getHumanInjuryList")
//    public String test1(CaseRequest caseRequest, HttpServletRequest request, Model model) {
//
//        InjuryCaseVO injuryCaseVO = new InjuryCaseVO();    // 返回信息
//
//
//        injuryCaseVO.setId("1");
//        injuryCaseVO.setCaseNumber("BA20250620001");
//        injuryCaseVO.setRiderName("张三");
//        injuryCaseVO.setRiderIdCard("110101199001011234");
//        injuryCaseVO.setReporterName("李四");
//        injuryCaseVO.setReporterMobile("13800138000");
//        injuryCaseVO.setMerchantName("美团配送");
//        injuryCaseVO.setResponsiblePerson("王五");
//        injuryCaseVO.setRemark("案件需要补充医疗证明");
//
//
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//        LocalDateTime reportTime = LocalDateTime.parse("2025-06-20 10:30:00", formatter);
//        LocalDateTime accidentTime = LocalDateTime.parse("2025-06-19 15:45:00", formatter);
//        injuryCaseVO.setReportTime(reportTime.toString());
//        injuryCaseVO.setAccidentTime(accidentTime.toString());
//        injuryCaseVO.setRemainingTime("et");
//        // 设置标签属性（字符串）
//        injuryCaseVO.setAccidentTags("人伤,交通事故,轻微");
//        // 设置操作列表（集合类型）
//
//
//        List<String> operations = new ArrayList<>();
//        operations.add("1");       // 案件详情
//        operations.add("2");       // 首仿
//        operations.add("3");       // 撤案
//        operations.add("4");       // 中止
//        operations.add("5");       // 取消中止 todo
//        operations.add("6");       // 材料上传
//        operations.add("7");       // 案件备注
//        operations.add("8");       // 案件申报
//        operations.add("9");       // 申报确认
//        operations.add("10");      // 退回补材
//        operations.add("11");      // 补材完成 todo
//        operations.add("12");      // 职伤认定
//        operations.add("13");      // 风神状态同步
//        operations.add("14");      // 结案
//
//        injuryCaseVO.setBtnList(operations);
//
//        System.out.println("/api/getHumanInjury执行");
//
//        PageParam pp = Tool.genPageParam(request);
//
//        // 每页最大条数
//        System.out.println(pp.getPageSize());
//        // 第页数
//        System.out.println(pp.getPageNum());
//
//        List<InjuryCaseVO> injuryCaseVOList = new ArrayList<>();
//        injuryCaseVOList.add(injuryCaseVO);
//
//        PageDTO<InjuryCaseVO> page = new PageDTO<>();
//
//        page.setTotal(1);
//        page.setPage(pp.getPageNum());
//        page.setSize(pp.getPageSize());
//        page.setList(injuryCaseVOList);
//        System.out.println(caseRequest.getStatus());
//        System.out.println(caseRequest.getComeFrom());
//
//
//        // 案件状态
//        Map<String, Object> statusMap = CaseStatus.getStatusMap();
//        model.addAttribute("statusMap", statusMap);
//
//        // 事故标签
//        Map<String, Object> tagMap = AccidentTag.getTagMap();
//        model.addAttribute("tagMap", tagMap);
//
//
//        if (caseRequest.getComeFrom() == null) {
//            return "error/404";
//        }
//        caseRequest.setPage(pp.getPageNum());
//        // paramMap.put("userId", ShiroSessionUtil.getLoginSession().getId());   权限鉴定
//        caseRequest.setSize(pp.getPageSize());
//
//        System.out.println(caseRequest.getCaseNumber());
//
//        System.out.println("骑手姓名：" + caseRequest.getRiderName());
//
//        PageDTO<InjuryCaseDTO> page1 = null;
//
//        model.addAttribute("page", page);
//        model.addAttribute("caseRequest", caseRequest);
//        return "/humanInjuryList";
//
//
//    }

    @GetMapping("/search")
    public ResponseResult<PageDTO<CaseInfoVo>> searchCases
            (@RequestBody CaseRequest caseRequest) {
        return ResponseResult.success(caseInfoService.getCasePage(caseRequest));
    }

    /**
     * 测试通过HTTPS链接上传文件到OSS并添加CaseAttachment
     *
     * @param caseId         案件ID
     * @param httpsUrl       HTTPS文件链接
     * @param uploader       上传人（可选，默认为"test_user"）
     * @param attachmentType 附件类型（可选，默认为"TEST"）
     * @param description    附件描述（可选）
     * @param fileName       文件名（可选，如果为空则从URL中提取）
     * @return 上传结果
     */
    @PostMapping("/uploadFromUrl")
    public ResponseResult<CaseAttachment> testUploadFromUrl(
            @RequestParam String caseId,
            @RequestParam String httpsUrl,
            @RequestParam(required = false, defaultValue = "test_user") String uploader,
            @RequestParam(required = false, defaultValue = "TEST") String attachmentType,
            @RequestParam(required = false) String description,
            @RequestParam(required = false) String fileName) {

        try {
            CaseAttachment result = caseAttachmentService.uploadAttachmentFromUrl(
                    caseId, httpsUrl, uploader, attachmentType, description, fileName);

            if (result != null) {
                return ResponseResult.success(result);
            } else {
                return ResponseResult.error(500, "文件上传失败");
            }
        } catch (Exception e) {
            return ResponseResult.error(500, "上传过程中发生异常: " + e.getMessage());
        }
    }

    /**
     * 测试通过HTTPS链接上传文件到OSS并添加CaseAttachment（JSON请求体版本）
     *
     * @param request 请求参数
     * @return 上传结果
     */
    @PostMapping("/uploadFromUrlJson")
    public ResponseResult<CaseAttachment> testUploadFromUrlJson(@RequestBody UploadFromUrlRequest request) {

        try {
            CaseAttachment result = caseAttachmentService.uploadAttachmentFromUrl(
                    request.getCaseId(),
                    request.getHttpsUrl(),
                    request.getUploader() != null ? request.getUploader() : "test_user",
                    request.getAttachmentType() != null ? request.getAttachmentType() : "TEST",
                    request.getDescription(),
                    request.getFileName());

            if (result != null) {
                return ResponseResult.success(result);
            } else {
                return ResponseResult.error(500, "文件上传失败");
            }
        } catch (Exception e) {
            return ResponseResult.error(500, "上传过程中发生异常: " + e.getMessage());
        }
    }

    /**
     * 上传请求参数类
     */
    public static class UploadFromUrlRequest {
        private String caseId;
        private String httpsUrl;
        private String uploader;
        private String attachmentType;
        private String description;
        private String fileName;

        // Getters and Setters
        public String getCaseId() {
            return caseId;
        }

        public void setCaseId(String caseId) {
            this.caseId = caseId;
        }

        public String getHttpsUrl() {
            return httpsUrl;
        }

        public void setHttpsUrl(String httpsUrl) {
            this.httpsUrl = httpsUrl;
        }

        public String getUploader() {
            return uploader;
        }

        public void setUploader(String uploader) {
            this.uploader = uploader;
        }

        public String getAttachmentType() {
            return attachmentType;
        }

        public void setAttachmentType(String attachmentType) {
            this.attachmentType = attachmentType;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }
    }

    /**
     * 测试URL编码处理 - 专门用于测试阿里云OSS签名URL的编码问题
     *
     * @param originalUrl 原始URL（可能包含编码问题）
     * @return 测试结果，包含原始URL和处理后的URL对比
     */
    @PostMapping("/testUrlEncoding")
    public ResponseResult<Map<String, Object>> testUrlEncoding(@RequestParam String originalUrl) {

        Map<String, Object> result = new HashMap<>();
        result.put("originalUrl", originalUrl);

        try {
            // 测试URL处理逻辑
            String processedUrl = processUrlForTest(originalUrl);
            result.put("processedUrl", processedUrl);
            result.put("needsProcessing", !originalUrl.equals(processedUrl));

            // 测试URL是否可访问
            try {
                java.net.URL url = new java.net.URL(processedUrl);
                java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
                connection.setRequestMethod("HEAD");
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(5000);

                int responseCode = connection.getResponseCode();
                result.put("httpStatus", responseCode);
                result.put("accessible", responseCode == 200);

                connection.disconnect();

            } catch (Exception e) {
                result.put("httpStatus", -1);
                result.put("accessible", false);
                result.put("accessError", e.getMessage());
            }

            return ResponseResult.success(result);

        } catch (Exception e) {
            result.put("error", e.getMessage());
            return ResponseResult.error(500, "URL处理测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试用的URL处理方法（复制自CaseAttachmentServiceImpl的逻辑）
     */
    private String processUrlForTest(String originalUrl) {
        try {
            // 如果URL包含阿里云OSS的签名参数，需要特殊处理
            if (originalUrl.contains("aliyuncs.com") && originalUrl.contains("Signature=")) {
                return handleAliyunOssUrlForTest(originalUrl);
            }

            return originalUrl;

        } catch (Exception e) {
            return originalUrl;
        }
    }

    /**
     * 测试用的阿里云OSS URL处理方法
     */
    private String handleAliyunOssUrlForTest(String ossUrl) {
        try {
            // 检查URL是否包含已经被解码的签名（包含+号和=号）
            if (ossUrl.contains("Signature=") && (ossUrl.contains("+") || ossUrl.contains("="))) {
                // 找到Signature参数的位置
                int signatureIndex = ossUrl.indexOf("Signature=");
                if (signatureIndex != -1) {
                    // 提取Signature参数值
                    int valueStart = signatureIndex + "Signature=".length();
                    int valueEnd = ossUrl.indexOf("&", valueStart);
                    if (valueEnd == -1) {
                        valueEnd = ossUrl.length();
                    }

                    String signatureValue = ossUrl.substring(valueStart, valueEnd);

                    // 重新编码Signature值
                    String encodedSignature = java.net.URLEncoder.encode(signatureValue, "UTF-8");

                    // 重构URL
                    return ossUrl.substring(0, valueStart) + encodedSignature + ossUrl.substring(valueEnd);
                }
            }

            return ossUrl;

        } catch (Exception e) {
            return ossUrl;
        }
    }


}
