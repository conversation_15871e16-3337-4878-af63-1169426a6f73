package kd.injury.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import kd.injury.dao.entity.CaseAttachment;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;

/**
 * 案件附件Service接口
 * 继承IService提供通用业务方法
 */
public interface CaseAttachmentService extends IService<CaseAttachment> {

    /**
     * 上传附件并关联案件
     *
     * @param caseId         案件ID
     * @param file           上传的文件
     * @param uploader       上传人
     * @param attachmentType 附件类型
     * @param description    附件描述
     * @return 附件实体
     * @throws IOException 文件处理异常
     */
    CaseAttachment uploadAttachment(String caseId, MultipartFile file, String uploader,
                                    String attachmentType, String description);

    CaseAttachment uploadAttachment(String caseId, String base64File, String uploader,
                                    String attachmentType, String description ,String fileNamePrefix);

    /**
     * 逻辑删除附件
     *
     * @param attachmentId 附件ID
     * @param operator     操作人
     * @return 操作结果
     */
    boolean deleteAttachment(String attachmentId, String operator);

    boolean deleteAttachment(List<String> attachmentIdList);

    List<CaseAttachment> selectByCaseAndAttachmentId(String id, List<String> fileObjectId, String attachmentType);


    Map<String, String> downloadFile(Map<String, CaseAttachment> collect);

    /**
     * 通过HTTPS链接上传文件到OSS并关联案件
     *
     * @param caseId         案件ID
     * @param httpsUrl       HTTPS文件链接
     * @param uploader       上传人
     * @param attachmentType 附件类型
     * @param description    附件描述
     * @param fileName       文件名（可选，如果为空则从URL中提取）
     * @return 附件实体
     */
    CaseAttachment uploadAttachmentFromUrl(String caseId, String httpsUrl, String uploader,
                                           String attachmentType, String description, String fileName);
}