# 通过HTTPS链接上传文件到OSS测试接口文档（优化版）

## 概述

新增了三个测试接口用于测试通过HTTPS链接下载文件并上传到OSS的功能：

1. `/api/cases/uploadFromUrl` - 使用表单参数的接口
2. `/api/cases/uploadFromUrlJson` - 使用JSON请求体的接口
3. `/api/cases/testUrlEncoding` - URL编码问题测试接口

## ⚠️ 重要说明：URL编码问题

**问题描述：** 阿里云OSS签名URL在HTTP传输过程中可能会出现编码问题，导致签名验证失败。

**典型症状：**
- 原始URL：`https://xxx.aliyuncs.com/xxx?Signature=U3BleJbJQ8%2BKUq1RY9TsYPCgGrk%3D`
- 传输后URL：`https://xxx.aliyuncs.com/xxx?Signature=U3BleJbJQ8+KUq1RY9TsYPCgGrk=`
- 结果：HTTP 403 Forbidden 错误

**解决方案：** 系统已自动处理此问题，会检测并重新编码阿里云OSS的Signature参数。

## 接口详情

### 1. 表单参数接口

**接口地址：** `POST /api/cases/uploadFromUrl`

**请求方式：** POST (application/x-www-form-urlencoded)

**请求参数：**

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| caseId | String | 是 | - | 案件ID |
| httpsUrl | String | 是 | - | HTTPS文件链接 |
| uploader | String | 否 | test_user | 上传人 |
| attachmentType | String | 否 | TEST | 附件类型 |
| description | String | 否 | - | 附件描述 |
| fileName | String | 否 | - | 文件名（为空时从URL提取） |

**请求示例：**
```bash
curl -X POST "http://localhost:8080/api/cases/uploadFromUrl" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "caseId=test-case-001" \
  -d "httpsUrl=https://fin-insure-job-zbprod-zb1-oss-1.oss-cn-zhangjiakou.aliyuncs.com/knight/11681694-e2e2-4773-b025-9e1184e47fcf.ext?Expires=1755140804&OSSAccessKeyId=LTAI4FpAqze1RjGsW6Eo5kCB&Signature=U3BleJbJQ8%2BKUq1RY9TsYPCgGrk%3D" \
  -d "attachmentType=EVIDENCE"
```

### 2. JSON请求体接口

**接口地址：** `POST /api/cases/uploadFromUrlJson`

**请求方式：** POST (application/json)

**请求示例：**
```bash
curl -X POST "http://localhost:8080/api/cases/uploadFromUrlJson" \
  -H "Content-Type: application/json" \
  -d '{
    "caseId": "test-case-001",
    "httpsUrl": "https://fin-insure-job-zbprod-zb1-oss-1.oss-cn-zhangjiakou.aliyuncs.com/knight/11681694-e2e2-4773-b025-9e1184e47fcf.ext?Expires=1755140804&OSSAccessKeyId=LTAI4FpAqze1RjGsW6Eo5kCB&Signature=U3BleJbJQ8%2BKUq1RY9TsYPCgGrk%3D",
    "attachmentType": "EVIDENCE",
    "description": "测试阿里云OSS文件上传"
  }'
```

### 3. URL编码测试接口

**接口地址：** `POST /api/cases/testUrlEncoding`

**请求方式：** POST (application/x-www-form-urlencoded)

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| originalUrl | String | 是 | 需要测试的URL |

**请求示例：**
```bash
curl -X POST "http://localhost:8080/api/cases/testUrlEncoding" \
  -d "originalUrl=https://fin-insure-job-zbprod-zb1-oss-1.oss-cn-zhangjiakou.aliyuncs.com/knight/11681694-e2e2-4773-b025-9e1184e47fcf.ext?Expires=1755140804&OSSAccessKeyId=LTAI4FpAqze1RjGsW6Eo5kCB&Signature=U3BleJbJQ8%2BKUq1RY9TsYPCgGrk%3D"
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "originalUrl": "https://xxx.aliyuncs.com/xxx?Signature=U3BleJbJQ8+KUq1RY9TsYPCgGrk=",
    "processedUrl": "https://xxx.aliyuncs.com/xxx?Signature=U3BleJbJQ8%2BKUq1RY9TsYPCgGrk%3D",
    "needsProcessing": true,
    "httpStatus": 200,
    "accessible": true
  }
}
```

## 响应格式

**成功响应：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "附件ID",
    "caseId": "案件ID",
    "fileName": "文件名",
    "fileSize": 文件大小,
    "fileType": "文件MIME类型",
    "uploadTime": "上传时间",
    "uploader": "上传人",
    "attachmentType": "附件类型",
    "description": "附件描述",
    "isDeleted": false,
    "fileObjectId": "OSS对象ID",
    "filePath": "文件路径",
    "createTime": "创建时间",
    "updateTime": "更新时间"
  }
}
```

## 测试流程建议

### 步骤1：测试URL编码
```bash
curl -X POST "http://localhost:8080/api/cases/testUrlEncoding" \
  -d "originalUrl=你的阿里云OSS_URL"
```

### 步骤2：确认URL可访问后进行文件上传
```bash
curl -X POST "http://localhost:8080/api/cases/uploadFromUrl" \
  -d "caseId=test-001" \
  -d "httpsUrl=你的阿里云OSS_URL"
```

## 优化功能

### 1. 自动URL编码处理
- ✅ 自动检测阿里云OSS签名URL
- ✅ 智能重新编码Signature参数
- ✅ 保持其他参数不变
- ✅ 详细的处理日志

### 2. 增强的错误处理
- ✅ URL格式验证
- ✅ HTTP状态码检查
- ✅ 连接超时处理
- ✅ 详细的错误信息

### 3. 调试支持
- ✅ 原始URL和处理后URL的对比日志
- ✅ URL编码测试接口
- ✅ HTTP可访问性测试

## 故障排除

### 问题1：403 Forbidden错误
**原因：** URL编码问题导致签名验证失败
**解决：** 使用 `/testUrlEncoding` 接口检查URL处理是否正确

### 问题2：连接超时
**原因：** 网络问题或URL无效
**解决：** 检查URL是否可访问，确认网络连接

### 问题3：文件下载失败
**原因：** URL过期或权限不足
**解决：** 重新生成OSS签名URL

## 注意事项

1. **URL有效期：** 阿里云OSS签名URL有时效性，请确保在有效期内使用
2. **网络环境：** 确保服务器可以访问外网和阿里云OSS
3. **文件大小：** 建议测试文件不超过100MB
4. **编码问题：** 系统已自动处理，但建议先用测试接口验证

## 开发说明

- 主要实现：`CaseAttachmentServiceImpl.uploadAttachmentFromUrl()`
- URL处理：`CaseAttachmentServiceImpl.processUrl()` 和 `handleAliyunOssUrl()`
- 测试接口：`TestController.testUploadFromUrl()` 等
- URL编码测试：`TestController.testUrlEncoding()`
