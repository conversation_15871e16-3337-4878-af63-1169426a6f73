# bootstrap-datepicker

This is a fork of <PERSON>'s [original code](http://www.eyecon.ro/bootstrap-datepicker/);
thanks go to him for getting this thing started!

Please note that this fork is not used on <PERSON>'s page, nor is it maintained or contributed to by him.

Versions are incremented according to [semver](http://semver.org/).

## Links

* [Online Demo](http://eternicode.github.io/bootstrap-datepicker/)
* [Online Docs](http://bootstrap-datepicker.readthedocs.org/) (ReadTheDocs.com)
* [Google Group](https://groups.google.com/group/bootstrap-datepicker/)
* [Travis CI ![Build Status](https://travis-ci.org/eternicode/bootstrap-datepicker.svg?branch=master)](https://travis-ci.org/eternicode/bootstrap-datepicker)

## Development

Once you cloned the repo, you'll need to install [grunt](http://gruntjs.com/) and the development dependencies using [npm](https://www.npmjs.com/).

    npm install -g grunt-cli
    npm install
