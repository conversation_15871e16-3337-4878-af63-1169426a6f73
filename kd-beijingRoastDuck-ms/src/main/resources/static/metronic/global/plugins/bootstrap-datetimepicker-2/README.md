# Bootstrap 3 Date/Time Picker
![GitHub version](https://badge.fury.io/gh/Eonasdan%2Fbootstrap-datetimepicker.png)&nbsp;&nbsp;&nbsp;![Travis](https://travis-ci.org/Eonasdan/bootstrap-datetimepicker.svg?branch=development)&nbsp;&nbsp;&nbsp;[![Stories in Ready](https://badge.waffle.io/Eonasdan/bootstrap-datetimepicker.png?label=ready&title=Ready)](https://waffle.io/Eonasdan/bootstrap-datetimepicker)

![DateTimePicker](http://i.imgur.com/nfnvh5g.png)

## [View the manual and demos](http://eonasdan.github.io/bootstrap-datetimepicker/)

# v4.17.37

Is the current stable release. The latest and greatest changes are available from the [development branch](https://github.com/Eonasdan/bootstrap-datetimepicker/tree/development)

# Version 5

IMPORANT! The Nuget packages will be depreciated in this release. Moving forward, Asp.Net/Nuget will **NOT** be delivering content packages like this one and you will need to use bower. See [this issue for more]( https://github.com/Eonasdan/bootstrap-datetimepicker/issues/1128)

v5 is [in planning](https://github.com/Eonasdan/bootstrap-datetimepicker/issues/841). We welcome your suggestions.

A major update is a great time to make be able to make breaking changes.

## Submitting Issues
If you have issues, please check the following first:
* Have you read the docs? 
* Do you have the latest version of momentjs?
* Do you have the latest version of jQuery?
* Please test and/or fork [this jsfiddle](http://jsfiddle.net/Eonasdan/0Ltv25o8/) with an example of your issue before you post an issue here. 
* Please indicate which version of the picker you are using (this can be found at the top of any included file)

## Where do you use this?
I'd love to know if your public site is using this plugin and list your logo on the documentation site. Please email me `me at eonasdan dot com`.

##Priority support is available at an hourly rate. 

If you have an urgent request, bug or need installation help, please contact me at `me at eonasdan dot com` for a quote.

## [Installation instructions](http://eonasdan.github.io/bootstrap-datetimepicker/Installing/)

## [Change Log](http://eonasdan.github.io/bootstrap-datetimepicker/Changelog/)
