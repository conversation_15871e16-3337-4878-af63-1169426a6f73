/**
 * Czech translation for bootstrap-datetimepicker
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 * Fixes by <PERSON><PERSON> <<EMAIL>>
 */
;(function($){
	$.fn.datetimepicker.dates['cs'] = {
		days: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>í", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Čtvrtek", "Pátek", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"],
		daysShort: ["<PERSON>", "<PERSON>n", "<PERSON><PERSON>", "<PERSON><PERSON>", "Čtv", "<PERSON><PERSON><PERSON>", "Sob", "<PERSON>"],
		daysMin: ["Ne", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "So", "Ne"],
		months: ["<PERSON>en", "<PERSON><PERSON>", "B<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>v<PERSON><PERSON>", "Červe<PERSON>", "Červenec", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Říjen", "Listopad", "Prosinec"],
		monthsShort: ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Říj", "<PERSON><PERSON>", "<PERSON>"],
		today: "<PERSON><PERSON>",
		suffix: [],
		meridiem: []
	};
}(j<PERSON><PERSON><PERSON>));
