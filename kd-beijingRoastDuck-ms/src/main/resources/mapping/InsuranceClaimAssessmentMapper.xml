<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kd.beijingRoastDuck.dao.InsuranceClaimAssessmentDao" >

    <insert id="insertSelective" parameterType="kd.entity.InsuranceClaimAssessment">
        <include refid="mapping.InsuranceClaimAssessmentMapper.insertSelective"/>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="kd.entity.InsuranceClaimAssessment">
        <include refid="mapping.InsuranceClaimAssessmentMapper.updateByPrimaryKeySelective" />
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <include refid="mapping.InsuranceClaimAssessmentMapper.deleteByPrimaryKey"/>
    </delete>

    <select id="selectByPrimaryKey" resultMap="mapping.InsuranceClaimAssessmentMapper.BaseResultMap" parameterType="java.lang.String">
        <include refid="mapping.InsuranceClaimAssessmentMapper.selectByPrimaryKey"/>
    </select>

    <select id="selectByLoss" resultMap="mapping.InsuranceClaimAssessmentMapper.BaseResultMap" parameterType="java.lang.String">
        <include refid="mapping.InsuranceClaimAssessmentMapper.selectByPrimaryKey"/>
    </select>

    <select id="selectByLossNoAndAssessmentType" parameterType="java.util.Map" resultMap="mapping.InsuranceClaimAssessmentMapper.BaseResultMap">
        select
        <include refid="mapping.InsuranceClaimAssessmentMapper.Base_Column_List" />
        from t_insurance_claim_assessment a
        where a.loss_no = #{lossNo,jdbcType=VARCHAR} and a.assessment_type = #{assessmentType,jdbcType=VARCHAR}
        FOR UPDATE
    </select>

</mapper>