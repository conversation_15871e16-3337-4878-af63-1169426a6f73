<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.CostInfoDao">

    <select id="selectByPrimaryKey" resultMap="mapping.CostInfoMapper.BaseResultMap"
            parameterType="java.lang.String">
        <include refid="mapping.CostInfoMapper.selectByPrimaryKey" />
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <include refid="mapping.CostInfoMapper.deleteByPrimaryKey" />
    </delete>

    <insert id="insertSelective" parameterType="kd.entity.CostInfo">
        <include refid="mapping.CostInfoMapper.insertSelective" />
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="kd.entity.CostInfo">
        <include refid="mapping.CostInfoMapper.updateByPrimaryKeySelective" />
    </update>

    <select id="selectByLossNo" parameterType="java.util.Map" resultMap="mapping.CostInfoMapper.BaseResultMap">
        select
        <include refid="mapping.CostInfoMapper.Base_Column_List" />
        from t_cost_info a
        where a.loss_no = #{lossNo,jdbcType=VARCHAR}
    </select>
</mapper>