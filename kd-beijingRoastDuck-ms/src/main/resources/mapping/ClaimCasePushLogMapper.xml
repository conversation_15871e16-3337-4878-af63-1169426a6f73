<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.ClaimCasePushLogDao">
	
	<insert id="insertSelective" parameterType="kd.entity.ClaimCasePushLog">
		<include refid="mapping.ClaimCasePushLogMapper.insertSelective"/>
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.ClaimCasePushLog">
		<include refid="mapping.ClaimCasePushLogMapper.updateByPrimaryKeySelective" />
	</update>
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		<include refid="mapping.ClaimCasePushLogMapper.deleteByPrimaryKey"/>
	</delete>

    <select id="selectByPrimaryKey" resultMap="mapping.ClaimCasePushLogMapper.BaseResultMap" parameterType="java.lang.String">
		<include refid="mapping.ClaimCasePushLogMapper.selectByPrimaryKey"/>
	</select>
</mapper>