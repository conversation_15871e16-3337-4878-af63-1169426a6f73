<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.ClaimCaseDao">

	<resultMap id="BaseResultVoMap" type="kd.beijingRoastDuck.support.BusinessDataExport">
		<result column="dateStr" property="dateStr" jdbcType="VARCHAR"/>
		<result column="claimCaseCount" property="claimCaseCount" jdbcType="INTEGER"/>
	</resultMap>
	
	<insert id="insertSelective" parameterType="kd.entity.ClaimCase">
		<include refid="mapping.ClaimCaseMapper.insertSelective" />
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.ClaimCase">
		<include refid="mapping.ClaimCaseMapper.updateByPrimaryKeySelective" />
	</update>

	<select id="selectByPrimaryKey" resultMap="mapping.ClaimCaseMapper.BaseResultMap" parameterType="java.lang.String">
		<include refid="mapping.ClaimCaseMapper.selectByPrimaryKey" />
	</select>

	<update id="setAappraisalAmountIsNull" parameterType="java.lang.String">
		update t_claim_case
		set appraisal_amount = null
		where id = #{claimCaseId,jdbcType=VARCHAR}
	</update>

	<select id="selectGroupByDate" resultMap="BaseResultVoMap">
		select
		DATE_FORMAT(create_time,"%Y-%m-%d") as dateStr,
		COUNT(1) as claimCaseCount
		from t_claim_case
		GROUP BY DATE_FORMAT(create_time,"%Y-%m-%d")
	</select>

	<select id="selectUnsettledClaimCase" resultType="kd.beijingRoastDuck.vo.ClaimCaseUnsettledExportVo">
		SELECT a.claim_case_no AS 'claimCaseNo', SUM( a.estimated_approved_money ) AS 'unSettledAmount'
    , b.case_type AS 'type1',
				GROUP_CONCAT( DISTINCT a.type ) AS 'type2',
				CONCAT( b.province, b.city, b.district, b.address ) AS 'address',
				c.start_date AS 'policyStartDate',
				b.treat_date AS 'treatDate',
				b.start_date AS 'reportDate',
				GROUP_CONCAT(
				DISTINCT
					CASE
						WHEN CONCAT(a.type, "-", a.category) = "1-1" THEN
							"骑手人伤"
						WHEN CONCAT(a.type, "-", a.category) = "2-1" THEN
							"三者人伤"
						WHEN CONCAT(a.type, "-", a.category) = "2-2" THEN
							"三者物损"
						WHEN CONCAT(a.type, "-", a.category) = "2-3" THEN
							"三者车损"
						ELSE "无法预知"
						END
					) as 'type3',
			   b.accident_proportion AS 'accidentProportion'
		FROM t_claim_case_object a
				 LEFT JOIN t_claim_case b ON a.claim_case_id = b.id
				 LEFT JOIN t_policy_person c ON b.policy_person_id = c.id
		WHERE b.claim_case_no NOT IN
			  (SELECT claim_case_no FROM t_insurance_company_case_push_log WHERE `status` = 0 AND direction = 1)
		  AND b.ins_code = 'DD'
		  AND b.`status` NOT LIKE "%-1"
		  AND b.`status` NOT LIKE "aex%"
		  AND b.`status` NOT LIKE "acx%"
		  AND b.case_type != 'CP'
		<if test="endDateStr != null and endDateStr != ''" >
			AND #{endDateStr} > c.start_date
		</if>
		<if test="startDateStr != null and startDateStr != ''" >
			AND c.start_date > #{startDateStr}
		</if>
		GROUP BY
			a.claim_case_no
	</select>


</mapper>