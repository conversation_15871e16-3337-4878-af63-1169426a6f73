<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kd.beijingRoastDuck.dao.AuditPriceInfoDao" >

    <insert id="insertSelective" parameterType="kd.entity.AuditPriceInfo">
        <include refid="mapping.AuditPriceInfoMapper.insertSelective"/>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="kd.entity.AuditPriceInfo">
        <include refid="mapping.AuditPriceInfoMapper.updateByPrimaryKeySelective" />
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <include refid="mapping.AuditPriceInfoMapper.deleteByPrimaryKey"/>
    </delete>

    <select id="selectByPrimaryKey" resultMap="mapping.AuditPriceInfoMapper.BaseResultMap" parameterType="java.lang.String">
        <include refid="mapping.AuditPriceInfoMapper.selectByPrimaryKey"/>
    </select>

    <select id="selectByLossNo" resultMap="mapping.AuditPriceInfoMapper.BaseResultMap" parameterType="java.lang.String">
        <include refid="mapping.EvalLossInfoMapper.selectByLossNo"/>
    </select>
</mapper>