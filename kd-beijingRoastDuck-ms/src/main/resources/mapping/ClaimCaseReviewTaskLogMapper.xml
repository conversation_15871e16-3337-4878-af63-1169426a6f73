<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.ClaimCaseReviewTaskLogDao">
	
	<insert id="insertSelective" parameterType="kd.entity.ClaimCaseReviewTaskLog">
		<include refid="mapping.ClaimCaseReviewTaskLogMapper.insertSelective" />
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.ClaimCaseReviewTaskLog">
		<include refid="mapping.ClaimCaseReviewTaskLogMapper.updateByPrimaryKeySelective" />
	</update>
	
	<insert id="insertParamByTaskId" parameterType="java.util.Map">
		insert into t_claim_case_review_task_log
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				id,
			</if>
			<if test="claimCaseId != null" >
				claim_case_id,
			</if>
			<if test="claimCaseReviewTaskId != null" >
				claim_case_review_task_id,
			</if>
			`type`,
			<if test="status != null" >
				status,
			</if>
			<if test="auditer != null" >
				auditer,
			</if>
			<if test="auditTime != null" >
				audit_time,
			</if>
			<if test="remark != null" >
				remark,
			</if>
			<if test="creator != null" >
				creator,
			</if>
			<if test="createTime != null" >
				create_time,
			</if>
			<if test="description != null" >
				description,
			</if>
			<if test="auditData != null" >
				audit_data,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id,jdbcType=VARCHAR},
			</if>
			<if test="claimCaseId != null" >
				#{claimCaseId,jdbcType=VARCHAR},
			</if>
			<if test="claimCaseReviewTaskId != null" >
				#{claimCaseReviewTaskId,jdbcType=VARCHAR},
			</if>
			(select category from t_claim_case_review_task where id = #{claimCaseReviewTaskId,jdbcType=VARCHAR}),
			<if test="status != null" >
				#{status,jdbcType=INTEGER},
			</if>
			<if test="auditer != null" >
				#{auditer,jdbcType=VARCHAR},
			</if>
			<if test="auditTime != null" >
				#{auditTime,jdbcType=TIMESTAMP},
			</if>
			<if test="remark != null" >
				#{remark,jdbcType=VARCHAR},
			</if>
			<if test="creator != null" >
				#{creator,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null" >
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="description != null" >
				#{description,jdbcType=LONGVARCHAR},
			</if>
			<if test="auditData != null" >
				#{auditData,jdbcType=LONGVARCHAR},
			</if>
		</trim>
	</insert>
</mapper>