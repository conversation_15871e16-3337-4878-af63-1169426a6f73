<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kd.beijingRoastDuck.dao.FactoryBrandDao" >

    <insert id="insertSelective" parameterType="kd.entity.FactoryBrand">
        <include refid="mapping.FactoryBrandMapper.insertSelective"/>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="kd.entity.FactoryBrand">
        <include refid="mapping.FactoryBrandMapper.updateByPrimaryKeySelective" />
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <include refid="mapping.FactoryBrandMapper.deleteByPrimaryKey"/>
    </delete>

    <delete id="deleteByFactoryId" parameterType="java.lang.String">
        DELETE FROM t_factory_brand
        WHERE factory_id = #{factoryId,jdbcType=VARCHAR}
    </delete>

    <select id="selectByPrimaryKey" resultMap="mapping.FactoryBrandMapper.BaseResultMap" parameterType="java.lang.String">
        <include refid="mapping.FactoryBrandMapper.selectByPrimaryKey"/>
    </select>

</mapper>