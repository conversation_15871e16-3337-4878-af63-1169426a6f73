<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kd.beijingRoastDuck.dao.VehicleInfoDao" >

    <insert id="insertSelective" parameterType="kd.entity.ClaimCasePushBatch">
        <include refid="mapping.VehicleInfoMapper.insertSelective"/>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="kd.entity.ClaimCasePushBatch">
        <include refid="mapping.VehicleInfoMapper.updateByPrimaryKeySelective" />
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <include refid="mapping.VehicleInfoMapper.deleteByPrimaryKey"/>
    </delete>

    <select id="selectByPrimaryKey" resultMap="mapping.VehicleInfoMapper.BaseResultMap" parameterType="java.lang.String">
        <include refid="mapping.VehicleInfoMapper.selectByPrimaryKey"/>
    </select>

    <select id="selectByLossNo" resultMap="mapping.VehicleInfoMapper.BaseResultMap" parameterType="java.lang.String">
        <include refid="mapping.VehicleInfoMapper.selectByLossNo"/>
    </select>

</mapper>