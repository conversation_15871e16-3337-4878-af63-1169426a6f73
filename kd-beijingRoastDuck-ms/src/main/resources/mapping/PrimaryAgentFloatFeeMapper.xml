<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.PrimaryAgentFloatFeeDao">
    <insert id="insertPrimaryAgentFloatFeeList" parameterType="java.util.List">
        INSERT INTO t_primary_agent_float_fee (
        id,
        ins_code,
        agent_no,
        agent_name,
        area_group_id,
        area_group_name,
        cycle_seq,
        start_date,
        end_date,
        claim_settlement_start,
        claim_settlement_end,
        timely_reporting_start,
        timely_reporting_end,
        claim_settlement_ratio,
        claim_settlement_factor,
        timely_reporting_ratio,
        timely_reporting_factor,
        status,
        create_time
        )
        VALUES
        <foreach collection="primaryAgentFloatFeeList" item="primaryAgentFloatFee" separator=",">
            <trim prefix="(" suffix =")" suffixOverrides=",">
                #{primaryAgentFloatFee.id,jdbcType=VARCHAR},
                #{primaryAgentFloatFee.insCode,jdbcType=VARCHAR},
                #{primaryAgentFloatFee.agentNo,jdbcType=VARCHAR},
                #{primaryAgentFloatFee.agentName,jdbcType=VARCHAR},
                #{primaryAgentFloatFee.areaGroupId,jdbcType=VARCHAR},
                #{primaryAgentFloatFee.areaGroupName,jdbcType=VARCHAR},
                #{primaryAgentFloatFee.cycleSeq,jdbcType=INTEGER},
                #{primaryAgentFloatFee.startDate,jdbcType=TIMESTAMP},
                #{primaryAgentFloatFee.endDate,jdbcType=TIMESTAMP},
                #{primaryAgentFloatFee.claimSettlementStart,jdbcType=TIMESTAMP},
                #{primaryAgentFloatFee.claimSettlementEnd,jdbcType=TIMESTAMP},
                #{primaryAgentFloatFee.timelyReportingStart,jdbcType=TIMESTAMP},
                #{primaryAgentFloatFee.timelyReportingEnd,jdbcType=TIMESTAMP},
                #{primaryAgentFloatFee.claimSettlementRatio,jdbcType=DECIMAL},
                #{primaryAgentFloatFee.claimSettlementFactor,jdbcType=DECIMAL},
                #{primaryAgentFloatFee.timelyReportingRatio,jdbcType=DECIMAL},
                #{primaryAgentFloatFee.timelyReportingFactor,jdbcType=DECIMAL},
                #{primaryAgentFloatFee.status,jdbcType=INTEGER},
                #{primaryAgentFloatFee.createTime,jdbcType=TIMESTAMP},
            </trim>
        </foreach>
    </insert>

    <select id="checkPrimaryAgentExist" resultType="java.lang.String">
        select
            p.id
        from t_primary_agent_float_fee p
        where p.ins_code=#{insCode} AND p.agent_no=#{agentNo} AND p.cycle_seq=#{cycleSeq} limit 1
    </select>
</mapper>