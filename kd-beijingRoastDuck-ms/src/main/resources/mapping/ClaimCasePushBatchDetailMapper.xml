<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.ClaimCasePushBatchDetailDao">
	
	<insert id="insertBatch" parameterType="kd.entity.ClaimCasePushBatch">
		<include refid="mapping.ClaimCasePushBatchDetailMapper.insertSelective"/>
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.ClaimCasePushBatch">
		<include refid="mapping.ClaimCasePushBatchDetailMapper.updateByPrimaryKeySelective" />
	</update>

	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		<include refid="mapping.ClaimCasePushBatchDetailMapper.deleteByPrimaryKey"/>
	</delete>

    <select id="selectByPrimaryKey" resultMap="mapping.ClaimCasePushBatchDetailMapper.BaseResultMap" parameterType="java.lang.String">
		<include refid="mapping.ClaimCasePushBatchDetailMapper.selectByPrimaryKey"/>
	</select>
</mapper>