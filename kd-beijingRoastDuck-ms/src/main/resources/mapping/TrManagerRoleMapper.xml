<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.TrManagerRoleDao">

	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from tr_manager_role
		where 1 = 1
		<if test="managerId != null">
			and manager_id = #{managerId,jdbcType=VARCHAR}
		</if>
		<if test="roleId != null">
			and role_id = #{roleId,jdbcType=VARCHAR}
		</if>
	</delete>

	<insert id="insertSelective" parameterType="kd.entity.TrManagerRoleKey">
		<include refid="mapping.TrManagerRoleMapper.insertSelective" />
	</insert>

	<select id="findByRoleId" resultType="java.lang.String"
			parameterType="java.lang.String">
		SELECT
			manager_id
		FROM
			tr_manager_role
		WHERE
			role_id = #{roleId,jdbcType=VARCHAR}
	</select>
	
</mapper>