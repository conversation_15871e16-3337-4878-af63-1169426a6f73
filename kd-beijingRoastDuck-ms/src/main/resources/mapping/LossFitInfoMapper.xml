<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kd.beijingRoastDuck.dao.LossFitInfoDao" >

    <insert id="insertSelective" parameterType="kd.entity.LossFitInfo">
        <include refid="mapping.LossFitInfoMapper.insertSelective"/>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="kd.entity.LossFitInfo">
        <include refid="mapping.LossFitInfoMapper.updateByPrimaryKeySelective" />
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <include refid="mapping.LossFitInfoMapper.deleteByPrimaryKey"/>
    </delete>

    <select id="selectByPrimaryKey" resultMap="mapping.LossFitInfoMapper.BaseResultMap" parameterType="java.lang.String">
        <include refid="mapping.LossFitInfoMapper.selectByPrimaryKey"/>
    </select>

    <select id="selectByLossNo" resultMap="mapping.LossFitInfoMapper.BaseResultMap" parameterType="java.lang.String">
        <include refid="mapping.LossFitInfoMapper.selectByLossNo"/>
    </select>

    <select id="selectByLossNoAndPartId" parameterType="java.util.Map" resultMap="mapping.LossFitInfoMapper.BaseResultMap">
        select
        <include refid="mapping.LossFitInfoMapper.Base_Column_List" />
        from t_loss_fit_info a
        where a.loss_no = #{lossNo,jdbcType=VARCHAR} and a.part_id = #{partId,jdbcType=VARCHAR}
    </select>

</mapper>