<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.ClaimCasePushOtherDao">
	
	<insert id="insertSelective" parameterType="kd.entity.ClaimCasePushOther">
		<include refid="mapping.ClaimCasePushOtherMapper.insertSelective"/>
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.ClaimCasePushOther">
		<include refid="mapping.ClaimCasePushOtherMapper.updateByPrimaryKeySelective" />
	</update>
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		<include refid="mapping.ClaimCasePushOtherMapper.deleteByPrimaryKey"/>
	</delete>

    <select id="selectByPrimaryKey" resultMap="mapping.ClaimCasePushOtherMapper.BaseResultMap" parameterType="java.lang.String">
		<include refid="mapping.ClaimCasePushOtherMapper.selectByPrimaryKey"/>
	</select>
</mapper>