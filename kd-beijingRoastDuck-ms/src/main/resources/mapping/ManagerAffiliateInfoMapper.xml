<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kd.beijingRoastDuck.dao.ManagerAffiliateInfoDao" >

  <select id="selectByUserId" resultMap="mapping.ManagerAffiliateInfoMapper.BaseResultMap" parameterType="java.lang.String">
    <include refid="mapping.ManagerAffiliateInfoMapper.selectByUserId"/>
  </select>

  <insert id="insertSelective" parameterType="kd.entity.ManagerAffiliateInfo">
    <include refid="mapping.ManagerAffiliateInfoMapper.insertSelective"/>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="kd.entity.ManagerAffiliateInfo">
    <include refid="mapping.ManagerAffiliateInfoMapper.updateByPrimaryKeySelective" />
  </update>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <include refid="mapping.ManagerAffiliateInfoMapper.deleteByPrimaryKey"/>
  </delete>

  <select id="selectByPrimaryKey" resultMap="mapping.ManagerAffiliateInfoMapper.BaseResultMap" parameterType="java.lang.String">
    <include refid="mapping.ManagerAffiliateInfoMapper.selectByPrimaryKey"/>
  </select>

</mapper>