<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.AssessmentReportDao">
	
	<insert id="insertSelective" parameterType="kd.entity.AssessmentReport">
		<include refid="mapping.AssessmentReportMapper.insertSelective"/>
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.AssessmentReport">
		<include refid="mapping.AssessmentReportMapper.updateByPrimaryKeySelective" />
	</update>
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		<include refid="mapping.AssessmentReportMapper.deleteByPrimaryKey"/>
	</delete>

    <select id="selectByPrimaryKey" resultMap="mapping.AssessmentReportMapper.BaseResultMap" parameterType="java.lang.String">
		<include refid="mapping.AssessmentReportMapper.selectByPrimaryKey"/>
	</select>
</mapper>