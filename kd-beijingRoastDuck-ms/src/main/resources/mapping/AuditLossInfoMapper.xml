<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kd.beijingRoastDuck.dao.AuditLossInfoDao" >

    <insert id="insertSelective" parameterType="kd.entity.AuditLossInfo">
        <include refid="mapping.AuditLossInfoMapper.insertSelective"/>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="kd.entity.AuditLossInfo">
        <include refid="mapping.AuditLossInfoMapper.updateByPrimaryKeySelective" />
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <include refid="mapping.AuditLossInfoMapper.deleteByPrimaryKey"/>
    </delete>

    <select id="selectByPrimaryKey" resultMap="mapping.AuditLossInfoMapper.BaseResultMap" parameterType="java.lang.String">
        <include refid="mapping.AuditLossInfoMapper.selectByPrimaryKey"/>
    </select>
    <select id="selectByLossNo" resultType="kd.entity.AuditLossInfo">
        <include refid="mapping.AuditLossInfoMapper.selectByLossNo"/>
    </select>

</mapper>