<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.ClaimCaseSubjectDetailDao">

	<insert id="insertSelective" parameterType="kd.entity.ClaimCaseSubjectDetail">
		<include refid="mapping.ClaimCaseSubjectDetailMapper.insertSelective" />
	</insert>

	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.ClaimCaseSubjectDetail">
		<include refid="mapping.ClaimCaseSubjectDetailMapper.updateByPrimaryKeySelective" />
	</update>

	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		<include refid="mapping.ClaimCaseSubjectDetailMapper.deleteByPrimaryKey"/>
	</delete>

	<select id="selectByPrimaryKey" resultMap="mapping.ClaimCaseSubjectDetailMapper.BaseResultMap" parameterType="java.lang.String">
		<include refid="mapping.ClaimCaseSubjectDetailMapper.selectByPrimaryKey" />
	</select>

	<update id="updateByPrimaryKeySelectiveByVerify">
		update t_claim_case_subject_detail
		<set >
			<if test="claimCaseSubjectId != null" >
				claim_case_subject_id = #{claimCaseSubjectId,jdbcType=VARCHAR},
			</if>
			<if test="claimCaseAttachId != null" >
				claim_case_attach_id = #{claimCaseAttachId,jdbcType=VARCHAR},
			</if>
			<if test="subjectName != null" >
				subject_name = #{subjectName,jdbcType=VARCHAR},
			</if>
			<if test="hospitalName != null" >
				hospital_name = #{hospitalName,jdbcType=VARCHAR},
			</if>
			<if test="receiptNo != null" >
				receipt_no = #{receiptNo,jdbcType=VARCHAR},
			</if>
			<if test="receiptName != null" >
				receipt_name = #{receiptName,jdbcType=VARCHAR},
			</if>
			<if test="treatmentDate != null" >
				treatment_date = #{treatmentDate,jdbcType=TIMESTAMP},
			</if>
			<if test="leaveHospitalDate != null" >
				leave_hospital_date = #{leaveHospitalDate,jdbcType=TIMESTAMP},
			</if>
			<if test="hospitalizeDay != null" >
				hospitalize_day = #{hospitalizeDay,jdbcType=INTEGER},
			</if>
			<if test="medicareStatus != null" >
				medicare_status = #{medicareStatus,jdbcType=INTEGER},
			</if>
			<if test="receiptAmount != null" >
				receipt_amount = #{receiptAmount,jdbcType=DECIMAL},
			</if>
			<if test="cashPay != null" >
				cash_pay = #{cashPay,jdbcType=DECIMAL},
			</if>
			<if test="accountPay != null" >
				account_pay = #{accountPay,jdbcType=DECIMAL},
			</if>
			<if test="planAsPay != null" >
				plan_as_pay = #{planAsPay,jdbcType=DECIMAL},
			</if>
			<if test="additionalPay != null" >
				additional_pay = #{additionalPay,jdbcType=DECIMAL},
			</if>
			<if test="thirdPartyAmountMoney != null" >
				third_party_amount_money = #{thirdPartyAmountMoney,jdbcType=DECIMAL},
			</if>
			<if test="classifiedConceit != null" >
				classified_conceit = #{classifiedConceit,jdbcType=DECIMAL},
			</if>
			<if test="selfFunded != null" >
				self_funded = #{selfFunded,jdbcType=DECIMAL},
			</if>
			<if test="deductionMoney != null" >
				deduction_money = #{deductionMoney,jdbcType=DECIMAL},
			</if>
			<if test="usedAllowDeductionDays != null" >
				used_allow_deduction_days = #{usedAllowDeductionDays,jdbcType=INTEGER},
			</if>
			<if test="usedAllowMoney != null" >
				used_allow_money = #{usedAllowMoney,jdbcType=DECIMAL},
			</if>
			<if test="usedAllowDays != null" >
				used_allow_days = #{usedAllowDays,jdbcType=INTEGER},
			</if>
			<if test="disabilityLevel != null" >
				disability_level = #{disabilityLevel,jdbcType=VARCHAR},
			</if>
			<if test="disabilityRecogTime != null" >
				disability_recog_time = #{disabilityRecogTime,jdbcType=TIMESTAMP},
			</if>
			<if test="missedWorkAmount != null" >
				missed_work_amount = #{missedWorkAmount,jdbcType=DECIMAL},
			</if>
			<if test="missedWorkDays != null" >
				missed_work_days = #{missedWorkDays,jdbcType=INTEGER},
			</if>
			<if test="nursingAmount != null" >
				nursing_amount = #{nursingAmount,jdbcType=DECIMAL},
			</if>
			<if test="nursingDays != null" >
				nursing_days = #{nursingDays,jdbcType=INTEGER},
			</if>
			<if test="vehiclePayAmount != null" >
				vehicle_pay_amount = #{vehiclePayAmount,jdbcType=DECIMAL},
			</if>
			<if test="vehicleLicenseNumber != null" >
				vehicle_license_number = #{vehicleLicenseNumber,jdbcType=VARCHAR},
			</if>
			<if test="vehicleOwnerName != null" >
				vehicle_owner_name = #{vehicleOwnerName,jdbcType=VARCHAR},
			</if>
			<if test="driverName != null" >
				driver_name = #{driverName,jdbcType=VARCHAR},
			</if>
			<if test="vehicleRepairCompany != null" >
				vehicle_repair_company = #{vehicleRepairCompany,jdbcType=VARCHAR},
			</if>
			<if test="itemName != null" >
				item_name = #{itemName,jdbcType=VARCHAR},
			</if>
			<if test="brandName != null" >
				brand_name = #{brandName,jdbcType=VARCHAR},
			</if>
			<if test="goodsPayAmount != null" >
				goods_pay_amount = #{goodsPayAmount,jdbcType=DECIMAL},
			</if>
			<if test="casualties != null" >
				casualties = #{casualties,jdbcType=INTEGER},
			</if>
			<if test="settlementStatus != null" >
				settlement_status = #{settlementStatus,jdbcType=INTEGER},
			</if>
			<if test="rate != null" >
				rate = #{rate,jdbcType=INTEGER},
			</if>
			<if test="approvedMoney != null" >
				approved_money = #{approvedMoney,jdbcType=DECIMAL},
			</if>
				negotiate_amount = #{negotiateAmount,jdbcType=DECIMAL},
			<if test="reason != null" >
				reason = #{reason,jdbcType=VARCHAR},
			</if>
			<if test="remark != null" >
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="creator != null" >
				creator = #{creator,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="jsgs != null" >
				jsgs = #{jsgs,jdbcType=LONGVARCHAR},
			</if>
		</set>
		where id = #{id,jdbcType=VARCHAR}
	</update>
</mapper>