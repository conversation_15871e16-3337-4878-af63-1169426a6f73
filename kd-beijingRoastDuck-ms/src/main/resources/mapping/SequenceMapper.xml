<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.SequenceDao">

	<select id="genNextVal" resultType="java.lang.Integer" parameterType="java.lang.String">
		SELECT nextval(#{seqName,jdbcType=VARCHAR})
	</select>

	<select id="existSeqName" resultMap="mapping.SequenceMapper.BaseResultMap"
			parameterType="java.lang.String">
		select * from t_sequence
		where seq_name = #{seqName,jdbcType=VARCHAR}
	</select>

</mapper>