<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kd.beijingRoastDuck.dao.EvalLossInfoDao" >

    <insert id="insertSelective" parameterType="kd.entity.ClaimCasePushBatch">
        <include refid="mapping.EvalLossInfoMapper.insertSelective"/>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="kd.entity.ClaimCasePushBatch">
        <include refid="mapping.EvalLossInfoMapper.updateByPrimaryKeySelective" />
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <include refid="mapping.EvalLossInfoMapper.deleteByPrimaryKey"/>
    </delete>

    <select id="selectByPrimaryKey" resultMap="mapping.EvalLossInfoMapper.BaseResultMap" parameterType="java.lang.String">
        <include refid="mapping.EvalLossInfoMapper.selectByPrimaryKey"/>
    </select>

    <select id="selectByLossNo" resultMap="mapping.EvalLossInfoMapper.BaseResultMap" parameterType="java.lang.String">
        <include refid="mapping.EvalLossInfoMapper.selectByLossNo"/>
    </select>

</mapper>