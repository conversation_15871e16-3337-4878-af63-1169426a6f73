<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.PolicyPersonDetailsDao">
	
	<insert id="insertSelective" parameterType="kd.entity.PolicyPerson">
		<include refid="mapping.PolicyPersonDetailsMapper.insertSelective" />
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.PolicyPerson">
		<include refid="mapping.PolicyPersonDetailsMapper.updateByPrimaryKeySelective" />
	</update>

	<select id="selectByPrimaryKey" resultMap="mapping.PolicyPersonDetailsMapper.BaseResultMap" parameterType="java.lang.String">
		<include refid="mapping.PolicyPersonDetailsMapper.selectByPrimaryKey" />
	</select>

</mapper>