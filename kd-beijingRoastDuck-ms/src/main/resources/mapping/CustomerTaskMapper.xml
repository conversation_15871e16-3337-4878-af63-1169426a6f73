<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.CustomerTaskDao">
	
	<insert id="insertSelective" parameterType="kd.entity.ClaimCaseAttach">
		<include refid="mapping.CustomerTaskMapper.insertSelective" />
	</insert>
	
	<update id="closePhoneTaskByClaimCaseId" parameterType="java.lang.String">
		update t_customer_task
		set capture_create_time = NOW(), status = 1
		where claim_case_id = #{claimCaseId,jdbcType=VARCHAR} and status = 0 and hang_type = 2
	</update>

	<update id="closeAllCustomerTask" parameterType="java.lang.String">
		update t_customer_task
		set capture_create_time = NOW(), status = 1
		where claim_case_id = #{claimCaseId,jdbcType=VARCHAR} and status = 0
	</update>

</mapper>