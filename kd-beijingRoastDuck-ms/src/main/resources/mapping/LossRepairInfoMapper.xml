<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kd.beijingRoastDuck.dao.LossRepairInfoDao" >

    <insert id="insertSelective" parameterType="kd.entity.LossRepairInfo">
        <include refid="mapping.LossRepairInfoMapper.insertSelective"/>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="kd.entity.LossRepairInfo">
        <include refid="mapping.LossRepairInfoMapper.updateByPrimaryKeySelective" />
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <include refid="mapping.LossRepairInfoMapper.deleteByPrimaryKey"/>
    </delete>

    <select id="selectByPrimaryKey" resultMap="mapping.LossRepairInfoMapper.BaseResultMap" parameterType="java.lang.String">
        <include refid="mapping.LossRepairInfoMapper.selectByPrimaryKey"/>
    </select>

    <select id="selectByLossNo" resultMap="mapping.LossRepairInfoMapper.BaseResultMap" parameterType="java.lang.String">
        <include refid="mapping.LossRepairInfoMapper.selectByLossNo"/>
    </select>

    <select id="selectByLossNoAndRepairId" parameterType="java.util.Map" resultMap="mapping.LossRepairInfoMapper.BaseResultMap">
        select
        <include refid="mapping.LossRepairInfoMapper.Base_Column_List" />
        from t_loss_repair_info a
        where a.loss_no = #{lossNo,jdbcType=VARCHAR} and a.repair_id = #{repairId,jdbcType=VARCHAR}
    </select>

</mapper>