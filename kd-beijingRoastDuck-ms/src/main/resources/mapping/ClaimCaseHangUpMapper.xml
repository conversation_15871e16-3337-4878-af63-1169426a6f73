<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.ClaimCaseHangUpDao">
	
	<insert id="insertSelective" parameterType="kd.entity.ClaimCaseHangUp">
		<include refid="mapping.ClaimCaseHangUpMapper.insertSelective" />
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.ClaimCaseHangUp">
		<include refid="mapping.ClaimCaseHangUpMapper.updateByPrimaryKeySelective" />
	</update>

	<update id="clearSourceHangUp">
		update t_claim_case_hang_up
		set status = -1
		where claim_case_id = #{claimCaseId,jdbcType=VARCHAR} and hang_up_source = #{hangUpSource,jdbcType=INTEGER}
		and status = 0
	</update>
	
	
</mapper>