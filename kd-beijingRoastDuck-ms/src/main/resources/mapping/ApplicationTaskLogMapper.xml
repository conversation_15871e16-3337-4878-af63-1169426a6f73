<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.ApplicationTaskLogDao" >

  <select id="selectByPrimaryKey" resultMap="mapping.ApplicationTaskLogMapper.BaseResultMap" parameterType="java.lang.String" >
    <include refid="mapping.ApplicationTaskLogMapper.selectByPrimaryKey"/>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    <include refid="mapping.ApplicationTaskLogMapper.deleteByPrimaryKey"/>
  </delete>

  <insert id="insertSelective" parameterType="kd.entity.ApplicationTaskLog" >
    <include refid="mapping.ApplicationTaskLogMapper.insertSelective"/>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="kd.entity.ApplicationTaskLog" >
    <include refid="mapping.ApplicationTaskLogMapper.updateByPrimaryKeySelective"/>
  </update>
</mapper>