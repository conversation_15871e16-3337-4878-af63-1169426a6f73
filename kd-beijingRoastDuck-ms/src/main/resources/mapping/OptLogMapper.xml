<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.OptLogDao">

    <insert id="insertSelective" parameterType="java.util.Map">
        insert into t_opt_log
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="managerId != null" >
                manager_id,
            </if>
            <if test="optUrl != null" >
                opt_url,
            </if>
            <if test="ipAddr != null" >
                ip_addr,
            </if>
            <if test="type != null" >
                type,
            </if>
            <if test="contentType != null" >
                content_type,
            </if>
            <if test="optTime != null" >
                opt_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="managerId != null" >
                #{managerId,jdbcType=VARCHAR},
            </if>
            <if test="optUrl != null" >
                #{optUrl,jdbcType=VARCHAR},
            </if>
            <if test="ipAddr != null" >
                #{ipAddr,jdbcType=VARCHAR},
            </if>
            <if test="type != null" >
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="contentType != null" >
                #{contentType,jdbcType=VARCHAR},
            </if>
            <if test="optTime != null" >
                #{optTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
</mapper>