<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kd.beijingRoastDuck.dao.RepairFactoryDao" >

    <insert id="insertSelective" parameterType="kd.entity.RepairFactory">
        <include refid="mapping.RepairFactoryMapper.insertSelective"/>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="kd.entity.RepairFactory">
        <include refid="mapping.RepairFactoryMapper.updateByPrimaryKeySelective" />
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <include refid="mapping.RepairFactoryMapper.deleteByPrimaryKey"/>
    </delete>

    <select id="selectByPrimaryKey" resultMap="mapping.RepairFactoryMapper.BaseResultMap" parameterType="java.lang.String">
        <include refid="mapping.RepairFactoryMapper.selectByPrimaryKey"/>
    </select>

    <select id="selectByFactoryId" resultMap="mapping.RepairFactoryMapper.BaseResultMap" parameterType="java.lang.String">
        SELECT
            *
        FROM
            t_repair_factory
        WHERE
            factory_id = #{factoryId,jdbcType=VARCHAR}
    </select>
</mapper>