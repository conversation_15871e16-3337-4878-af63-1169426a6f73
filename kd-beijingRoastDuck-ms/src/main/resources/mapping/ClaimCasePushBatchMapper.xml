<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.ClaimCasePushBatchDao">
	
	<insert id="insertBatch" parameterType="kd.entity.ClaimCasePushBatch">
		<include refid="mapping.ClaimCasePushBatchMapper.insertSelective"/>
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.ClaimCasePushBatch">
		<include refid="mapping.ClaimCasePushBatchMapper.updateByPrimaryKeySelective" />
	</update>
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		<include refid="mapping.ClaimCasePushBatchMapper.deleteByPrimaryKey"/>
	</delete>

    <select id="selectByPrimaryKey" resultMap="mapping.ClaimCasePushBatchMapper.BaseResultMap" parameterType="java.lang.String">
		<include refid="mapping.ClaimCasePushBatchMapper.selectByPrimaryKey"/>
	</select>
</mapper>