<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.ApprovalTaskDao" >

  <select id="selectByPrimaryKey" resultMap="mapping.ApprovalTaskMapper.BaseResultMap" parameterType="java.lang.String" >
    <include refid="mapping.ApprovalTaskMapper.selectByPrimaryKey"/>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    <include refid="mapping.ApprovalTaskMapper.deleteByPrimaryKey"/>
  </delete>

  <insert id="insertSelective" parameterType="kd.entity.ApprovalTask" >
    <include refid="mapping.ApprovalTaskMapper.insertSelective"/>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="kd.entity.ApprovalTask" >
    <include refid="mapping.ApprovalTaskMapper.updateByPrimaryKeySelective"/>
  </update>

</mapper>