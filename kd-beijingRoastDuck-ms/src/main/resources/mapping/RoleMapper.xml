<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.RoleDao">
	
	<resultMap id="RoleGrantMap" type="kd.beijingRoastDuck.vo.RoleGrant" extends="mapping.RoleMapper.BaseResultMap">
		<result column="selected" property="selected" jdbcType="INTEGER" />
	</resultMap>
	
	<select id="selectByPrimaryKey" resultMap="mapping.RoleMapper.BaseResultMap"
		parameterType="java.lang.String">
		<include refid="mapping.RoleMapper.selectByPrimaryKey" />
	</select>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		<include refid="mapping.RoleMapper.deleteByPrimaryKey" />
	</delete>
	
	<insert id="insertSelective" parameterType="kd.entity.Role">
		<include refid="mapping.RoleMapper.insertSelective" />
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.Role">
		<include refid="mapping.RoleMapper.updateByPrimaryKeySelective" />
	</update>
	
	<select id="findAllList" resultMap="mapping.RoleMapper.BaseResultMap"
		parameterType="java.util.Map">
		select
		<include refid="mapping.RoleMapper.Base_Column_List" />
		from t_role a
		where 1=1
		<if test="platform != null">
			and a.platform = #{platform,jdbcType=INTEGER}
		</if>
		order by a.name
	</select>

	<select id="findAuthRoleListByManagerId" resultMap="mapping.RoleMapper.BaseResultMap"
		parameterType="java.lang.String">
		select
		<include refid="mapping.RoleMapper.Base_Column_List" />
		from t_role a
		left join tr_manager_role mr on a.id = mr.role_id
		left join t_manager m on mr.manager_id = m.id
		where m.id = #{managerId}
	</select>
	
	<select id="findManagerRoleGrantAllList" resultMap="RoleGrantMap"
		parameterType="java.lang.String">
		SELECT
			<include refid="mapping.RoleMapper.Base_Column_List" />,
			(
				SELECT
					count(1)
				FROM
					tr_manager_role
				WHERE
					role_id = a.id
				AND manager_id = #{managerId}
			) AS selected
		FROM
			t_role a
		WHERE a.platform = 0
	</select>

	<select id="existRoleName" resultType="java.lang.Integer"
		parameterType="map">
		select
		count(1)
		from t_role a
		where a.name = #{name}
		<if test="id != null">
			and a.id != #{id}
		</if>
		and a.platform = #{platform}
	</select>
	
	<select id="findEntRoleGrantAllList" resultMap="RoleGrantMap"
		parameterType="java.util.Map">
		SELECT
			<include refid="mapping.RoleMapper.Base_Column_List" />,
			(
				SELECT
					count(1)
				FROM
					tr_enterprise_user_role
				WHERE
					role_id = a.id
				AND enterprise_user_id = #{enterpriseUserId}
			) AS selected
		FROM
			t_role a
		WHERE a.platform = 1
		and a.creator in (SELECT id from t_manager)
	</select>
	
	<select id="selectByName" resultMap="mapping.RoleMapper.BaseResultMap"
		parameterType="java.lang.String">
		select 
			<include refid="mapping.RoleMapper.Base_Column_List" />
		from t_role a
		where a.name = #{name,jdbcType=VARCHAR}
	</select>

	<select id="selectAllList" resultMap="mapping.RoleMapper.BaseResultMap"
			parameterType="java.lang.String">
		select
		<include refid="mapping.RoleMapper.Base_Column_List" />
		from t_role a
		where a.`status` = 1
	</select>
</mapper>