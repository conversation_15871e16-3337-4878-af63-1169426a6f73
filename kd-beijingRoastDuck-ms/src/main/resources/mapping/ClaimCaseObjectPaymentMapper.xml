<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.ClaimCaseObjectPaymentDao">

    <insert id="insertSelective" parameterType="kd.entity.ClaimCaseObjectPayment">
        <include refid="mapping.ClaimCaseObjectPaymentMapper.insertSelective" />
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="kd.entity.ClaimCaseObjectPayment">
        <include refid="mapping.ClaimCaseObjectPaymentMapper.updateByPrimaryKeySelective" />
    </update>

    <update id="deleteByPrimaryKey" parameterType="java.lang.String">
        UPDATE t_claim_case_object_payment SET `status` = '1' WHERE id = #{id,jdbcType=VARCHAR}
    </update>

</mapper>