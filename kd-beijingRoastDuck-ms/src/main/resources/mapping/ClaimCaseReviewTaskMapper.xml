<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.ClaimCaseReviewTaskDao">
	
	<insert id="insertSelective" parameterType="kd.entity.ClaimCaseReviewTask">
		<include refid="mapping.ClaimCaseReviewTaskMapper.insertSelective" />
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.ClaimCaseReviewTask">
		<include refid="mapping.ClaimCaseReviewTaskMapper.updateByPrimaryKeySelective" />
	</update>

	<update id="closeTaskByClaimCaseId" parameterType="java.lang.String">
		update t_claim_case_review_task
		set status = 1, auditer = #{auditer,jdbcType=VARCHAR}, audit_time = NOW()
		where claim_case_id = #{claimCaseId,jdbcType=VARCHAR} and status = 0
	</update>


</mapper>