<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.ApprovalTemplateDao" >

  <select id="selectByPrimaryKey" resultMap="mapping.ApprovalTemplateMapper.BaseResultMap" parameterType="java.lang.String" >
    <include refid="mapping.ApprovalTemplateMapper.selectByPrimaryKey"/>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    <include refid="mapping.ApprovalTemplateMapper.deleteByPrimaryKey"/>
  </delete>

  <insert id="insertSelective" parameterType="kd.entity.ApprovalTemplate" >
    <include refid="mapping.ApprovalTemplateMapper.insertSelective"/>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="kd.entity.ApprovalTemplate" >
    <include refid="mapping.ApprovalTemplateMapper.updateByPrimaryKeySelective"/>
  </update>
</mapper>