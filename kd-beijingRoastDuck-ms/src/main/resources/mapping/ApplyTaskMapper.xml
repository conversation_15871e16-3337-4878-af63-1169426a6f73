<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.ApplyTaskDao" >

  <select id="selectByPrimaryKey" resultMap="mapping.ApplyTaskMapper.BaseResultMap" parameterType="java.lang.String" >
    <include refid="mapping.ApplyTaskMapper.selectByPrimaryKey"/>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    <include refid="mapping.ApplyTaskMapper.deleteByPrimaryKey"/>
  </delete>

  <insert id="insertSelective" parameterType="kd.entity.ApplyTask" >
    <include refid="mapping.ApplyTaskMapper.insertSelective"/>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="kd.entity.ApplyTask" >
    <include refid="mapping.ApplyTaskMapper.updateByPrimaryKeySelective"/>
  </update>
</mapper>