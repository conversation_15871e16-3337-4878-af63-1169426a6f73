<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.InsuranceNuclearAuditTaskDao">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="kd.entity.InsuranceNuclearAuditTask">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="reportNo" column="report_no" jdbcType="VARCHAR"/>
        <result property="caseNo" column="case_no" jdbcType="VARCHAR"/>
        <result property="taskNo" column="task_no" jdbcType="VARCHAR"/>
        <result property="claimCaseId" column="claim_case_id" jdbcType="VARCHAR"/>
        <result property="claimantName" column="claimant_name" jdbcType="VARCHAR"/>
        <result property="insuredName" column="insured_name" jdbcType="VARCHAR"/>
        <result property="idCardNo" column="id_card_no" jdbcType="VARCHAR"/>
        <result property="lossType" column="loss_type" jdbcType="VARCHAR"/>
        <result property="injuredTargetName" column="injured_target_name" jdbcType="VARCHAR"/>
        <result property="lossAmount" column="loss_amount" jdbcType="DECIMAL"/>
        <result property="insuranceCompany" column="insurance_company" jdbcType="VARCHAR"/>
        <result property="businessChannel" column="business_channel" jdbcType="VARCHAR"/>
        <result property="taskStatus" column="task_status" jdbcType="VARCHAR"/>
        <result property="tags" column="tags" jdbcType="VARCHAR"/>
        <result property="taskOperator" column="task_operator" jdbcType="VARCHAR"/>
        <result property="reportTime" column="report_time" jdbcType="TIMESTAMP"/>
        <result property="caseTime" column="case_time" jdbcType="TIMESTAMP"/>
        <result property="incidentTime" column="incident_time" jdbcType="TIMESTAMP"/>
        <result property="taskInflowTime" column="task_inflow_time" jdbcType="TIMESTAMP"/>
        <result property="accidentLiability" column="accident_liability" jdbcType="VARCHAR"/>
        <result property="accidentType" column="accident_type" jdbcType="VARCHAR"/>
        <result property="firstEstimateAmount" column="first_estimate_amount" jdbcType="DECIMAL"/>
        <result property="trafficStatus" column="traffic_status" jdbcType="VARCHAR"/>
        <result property="policyType" column="policy_type" jdbcType="VARCHAR"/>
        <result property="damagedVehicle" column="damaged_vehicle" jdbcType="VARCHAR"/>
        <result property="productName" column="product_name" jdbcType="VARCHAR"/>
        <result property="responsibilityRatio" column="responsibility_ratio" jdbcType="VARCHAR"/>
        <result property="delayedReport" column="delayed_report" jdbcType="VARCHAR"/>
        <result property="isLitigation" column="is_litigation" jdbcType="VARCHAR"/>
        <result property="isCompensation" column="is_compensation" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="yn" column="yn" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id,report_no,case_no,task_no,claim_case_id,claimant_name,
        insured_name,id_card_no,loss_type,injured_target_name,loss_amount,
        insurance_company,business_channel,task_status,tags,task_operator,
        report_time,case_time,incident_time,task_inflow_time,accident_liability,
        accident_type,first_estimate_amount,traffic_status,policy_type,damaged_vehicle,
        product_name,responsibility_ratio,delayed_report,is_litigation,is_compensation,
        create_time,modify_time,creator,modifier,yn
    </sql>

    <!-- 查询条件 -->
    <sql id="Query_Condition">
        <where>
            yn = 1
            <!-- 动态编号查询 -->
            <if test="reportNo != null and reportNo != ''">
                AND report_no LIKE CONCAT('%', #{reportNo}, '%')
            </if>
            <if test="caseNo != null and caseNo != ''">
                AND case_no LIKE CONCAT('%', #{caseNo}, '%')
            </if>
            <if test="taskNo != null and taskNo != ''">
                AND task_no LIKE CONCAT('%', #{taskNo}, '%')
            </if>
            <!-- 出险人查询 -->
            <if test="insuredName != null and insuredName != ''">
                AND insured_name LIKE CONCAT('%', #{insuredName}, '%')
            </if>
            <!-- 证件号码查询 -->
            <if test="idCardNo != null and idCardNo != ''">
                AND id_card_no LIKE CONCAT('%', #{idCardNo}, '%')
            </if>
            <!-- 损失类型多选查询 -->
            <if test="lossTypes != null and lossTypes.size() > 0">
                AND loss_type IN
                <foreach collection="lossTypes" item="lossType" open="(" separator="," close=")">
                    #{lossType}
                </foreach>
            </if>
            <!-- 任务状态查询 -->
            <if test="taskStatus != null and taskStatus != ''">
                AND task_status = #{taskStatus}
            </if>
            <!-- 承保机构查询 -->
            <if test="insuranceCompany != null and insuranceCompany != ''">
                AND insurance_company = #{insuranceCompany}
            </if>
            <!-- 业务归属渠道查询 -->
            <if test="businessChannel != null and businessChannel != ''">
                AND business_channel = #{businessChannel}
            </if>
            <!-- 任务操作员查询 -->
            <if test="taskOperator != null and taskOperator != ''">
                AND task_operator = #{taskOperator}
            </if>
            <!-- 时间范围查询 -->
            <if test="startTime != null and startTime != ''">
                <choose>
                    <when test="timeType == 'reportTime'">
                        AND report_time &gt;= #{startTime}
                    </when>
                    <when test="timeType == 'incidentTime'">
                        AND incident_time &gt;= #{startTime}
                    </when>
                    <when test="timeType == 'taskInflowTime'">
                        AND task_inflow_time &gt;= #{startTime}
                    </when>
                    <otherwise>
                        AND task_inflow_time &gt;= #{startTime}
                    </otherwise>
                </choose>
            </if>
            <if test="endTime != null and endTime != ''">
                <choose>
                    <when test="timeType == 'reportTime'">
                        AND report_time &lt;= #{endTime}
                    </when>
                    <when test="timeType == 'incidentTime'">
                        AND incident_time &lt;= #{endTime}
                    </when>
                    <when test="timeType == 'taskInflowTime'">
                        AND task_inflow_time &lt;= #{endTime}
                    </when>
                    <otherwise>
                        AND task_inflow_time &lt;= #{endTime}
                    </otherwise>
                </choose>
            </if>
        </where>
    </sql>

    <!-- 分页查询保司核损任务列表 -->
    <select id="findPageList" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_insurance_nuclear_audit_task
        <include refid="Query_Condition"/>
        ORDER BY create_time DESC
    </select>

    <!-- 查询保司核损任务总数 -->
    <select id="findPageCount" resultType="int" parameterType="java.util.Map">
        SELECT COUNT(1)
        FROM t_insurance_nuclear_audit_task
        <include refid="Query_Condition"/>
    </select>

    <!-- 统计任务状态数量 -->
    <select id="findTaskStatusCount" resultType="java.util.Map" parameterType="java.util.Map">
        SELECT
        COUNT(1) as totalCount,
        SUM(CASE WHEN task_status = '待处理' THEN 1 ELSE 0 END) as pendingCount,
        SUM(CASE WHEN task_status = '处理中' THEN 1 ELSE 0 END) as processingCount,
        SUM(CASE WHEN task_status = '暂存' THEN 1 ELSE 0 END) as tempSaveCount,
        SUM(CASE WHEN task_status = '已处理' THEN 1 ELSE 0 END) as completedCount
        FROM t_insurance_nuclear_audit_task
        <include refid="Query_Condition"/>
    </select>

    <!-- 根据ID查询任务详情 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_insurance_nuclear_audit_task
        WHERE id = #{id} AND yn = 1
    </select>

    <!-- 新增保司核损任务 -->
    <insert id="insertSelective" parameterType="kd.entity.InsuranceNuclearAuditTask">
        INSERT INTO t_insurance_nuclear_audit_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="reportNo != null">report_no,</if>
            <if test="caseNo != null">case_no,</if>
            <if test="taskNo != null">task_no,</if>
            <if test="claimCaseId != null">claim_case_id,</if>
            <if test="claimantName != null">claimant_name,</if>
            <if test="insuredName != null">insured_name,</if>
            <if test="idCardNo != null">id_card_no,</if>
            <if test="lossType != null">loss_type,</if>
            <if test="injuredTargetName != null">injured_target_name,</if>
            <if test="lossAmount != null">loss_amount,</if>
            <if test="insuranceCompany != null">insurance_company,</if>
            <if test="businessChannel != null">business_channel,</if>
            <if test="taskStatus != null">task_status,</if>
            <if test="tags != null">tags,</if>
            <if test="taskOperator != null">task_operator,</if>
            <if test="reportTime != null">report_time,</if>
            <if test="caseTime != null">case_time,</if>
            <if test="incidentTime != null">incident_time,</if>
            <if test="taskInflowTime != null">task_inflow_time,</if>
            <if test="accidentLiability != null">accident_liability,</if>
            <if test="accidentType != null">accident_type,</if>
            <if test="firstEstimateAmount != null">first_estimate_amount,</if>
            <if test="trafficStatus != null">traffic_status,</if>
            <if test="policyType != null">policy_type,</if>
            <if test="damagedVehicle != null">damaged_vehicle,</if>
            <if test="productName != null">product_name,</if>
            <if test="responsibilityRatio != null">responsibility_ratio,</if>
            <if test="delayedReport != null">delayed_report,</if>
            <if test="isLitigation != null">is_litigation,</if>
            <if test="isCompensation != null">is_compensation,</if>
            <if test="createTime != null">create_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
            <if test="yn != null">yn,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=VARCHAR},</if>
            <if test="reportNo != null">#{reportNo,jdbcType=VARCHAR},</if>
            <if test="caseNo != null">#{caseNo,jdbcType=VARCHAR},</if>
            <if test="taskNo != null">#{taskNo,jdbcType=VARCHAR},</if>
            <if test="claimCaseId != null">#{claimCaseId,jdbcType=VARCHAR},</if>
            <if test="claimantName != null">#{claimantName,jdbcType=VARCHAR},</if>
            <if test="insuredName != null">#{insuredName,jdbcType=VARCHAR},</if>
            <if test="idCardNo != null">#{idCardNo,jdbcType=VARCHAR},</if>
            <if test="lossType != null">#{lossType,jdbcType=VARCHAR},</if>
            <if test="injuredTargetName != null">#{injuredTargetName,jdbcType=VARCHAR},</if>
            <if test="lossAmount != null">#{lossAmount,jdbcType=DECIMAL},</if>
            <if test="insuranceCompany != null">#{insuranceCompany,jdbcType=VARCHAR},</if>
            <if test="businessChannel != null">#{businessChannel,jdbcType=VARCHAR},</if>
            <if test="taskStatus != null">#{taskStatus,jdbcType=VARCHAR},</if>
            <if test="tags != null">#{tags,jdbcType=VARCHAR},</if>
            <if test="taskOperator != null">#{taskOperator,jdbcType=VARCHAR},</if>
            <if test="reportTime != null">#{reportTime,jdbcType=TIMESTAMP},</if>
            <if test="caseTime != null">#{caseTime,jdbcType=TIMESTAMP},</if>
            <if test="incidentTime != null">#{incidentTime,jdbcType=TIMESTAMP},</if>
            <if test="taskInflowTime != null">#{taskInflowTime,jdbcType=TIMESTAMP},</if>
            <if test="accidentLiability != null">#{accidentLiability,jdbcType=VARCHAR},</if>
            <if test="accidentType != null">#{accidentType,jdbcType=VARCHAR},</if>
            <if test="firstEstimateAmount != null">#{firstEstimateAmount,jdbcType=DECIMAL},</if>
            <if test="trafficStatus != null">#{trafficStatus,jdbcType=VARCHAR},</if>
            <if test="policyType != null">#{policyType,jdbcType=VARCHAR},</if>
            <if test="damagedVehicle != null">#{damagedVehicle,jdbcType=VARCHAR},</if>
            <if test="productName != null">#{productName,jdbcType=VARCHAR},</if>
            <if test="responsibilityRatio != null">#{responsibilityRatio,jdbcType=VARCHAR},</if>
            <if test="delayedReport != null">#{delayedReport,jdbcType=VARCHAR},</if>
            <if test="isLitigation != null">#{isLitigation,jdbcType=VARCHAR},</if>
            <if test="isCompensation != null">#{isCompensation,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="modifyTime != null">#{modifyTime,jdbcType=TIMESTAMP},</if>
            <if test="creator != null">#{creator,jdbcType=VARCHAR},</if>
            <if test="modifier != null">#{modifier,jdbcType=VARCHAR},</if>
            <if test="yn != null">#{yn,jdbcType=TINYINT},</if>
        </trim>
    </insert>

    <!-- 更新保司核损任务 -->
    <update id="updateByPrimaryKeySelective" parameterType="kd.entity.InsuranceNuclearAuditTask">
        UPDATE t_insurance_nuclear_audit_task
        <set>
            <if test="reportNo != null">
                report_no = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="caseNo != null">
                case_no = #{caseNo,jdbcType=VARCHAR},
            </if>
            <if test="taskNo != null">
                task_no = #{taskNo,jdbcType=VARCHAR},
            </if>
            <if test="claimCaseId != null">
                claim_case_id = #{claimCaseId,jdbcType=VARCHAR},
            </if>
            <if test="claimantName != null">
                claimant_name = #{claimantName,jdbcType=VARCHAR},
            </if>
            <if test="insuredName != null">
                insured_name = #{insuredName,jdbcType=VARCHAR},
            </if>
            <if test="idCardNo != null">
                id_card_no = #{idCardNo,jdbcType=VARCHAR},
            </if>
            <if test="lossType != null">
                loss_type = #{lossType,jdbcType=VARCHAR},
            </if>
            <if test="injuredTargetName != null">
                injured_target_name = #{injuredTargetName,jdbcType=VARCHAR},
            </if>
            <if test="lossAmount != null">
                loss_amount = #{lossAmount,jdbcType=DECIMAL},
            </if>
            <if test="insuranceCompany != null">
                insurance_company = #{insuranceCompany,jdbcType=VARCHAR},
            </if>
            <if test="businessChannel != null">
                business_channel = #{businessChannel,jdbcType=VARCHAR},
            </if>
            <if test="taskStatus != null">
                task_status = #{taskStatus,jdbcType=VARCHAR},
            </if>
            <if test="tags != null">
                tags = #{tags,jdbcType=VARCHAR},
            </if>
            <if test="taskOperator != null">
                task_operator = #{taskOperator,jdbcType=VARCHAR},
            </if>
            <if test="reportTime != null">
                report_time = #{reportTime,jdbcType=TIMESTAMP},
            </if>
            <if test="caseTime != null">
                case_time = #{caseTime,jdbcType=TIMESTAMP},
            </if>
            <if test="incidentTime != null">
                incident_time = #{incidentTime,jdbcType=TIMESTAMP},
            </if>
            <if test="taskInflowTime != null">
                task_inflow_time = #{taskInflowTime,jdbcType=TIMESTAMP},
            </if>
            <if test="accidentLiability != null">
                accident_liability = #{accidentLiability,jdbcType=VARCHAR},
            </if>
            <if test="accidentType != null">
                accident_type = #{accidentType,jdbcType=VARCHAR},
            </if>
            <if test="firstEstimateAmount != null">
                first_estimate_amount = #{firstEstimateAmount,jdbcType=DECIMAL},
            </if>
            <if test="trafficStatus != null">
                traffic_status = #{trafficStatus,jdbcType=VARCHAR},
            </if>
            <if test="policyType != null">
                policy_type = #{policyType,jdbcType=VARCHAR},
            </if>
            <if test="damagedVehicle != null">
                damaged_vehicle = #{damagedVehicle,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                product_name = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="responsibilityRatio != null">
                responsibility_ratio = #{responsibilityRatio,jdbcType=VARCHAR},
            </if>
            <if test="delayedReport != null">
                delayed_report = #{delayedReport,jdbcType=VARCHAR},
            </if>
            <if test="isLitigation != null">
                is_litigation = #{isLitigation,jdbcType=VARCHAR},
            </if>
            <if test="isCompensation != null">
                is_compensation = #{isCompensation,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="yn != null">
                yn = #{yn,jdbcType=TINYINT},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除保司核损任务 -->
    <update id="deleteByPrimaryKey" parameterType="java.lang.String">
        UPDATE t_insurance_nuclear_audit_task
        SET yn = 0, modify_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 批量更新任务状态 -->
    <update id="batchUpdateStatus">
        UPDATE t_insurance_nuclear_audit_task
        SET task_status = #{status}, modifier = #{modifier}, modify_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 处理任务 -->
    <update id="processTask">
        UPDATE t_insurance_nuclear_audit_task
        SET task_status = '处理中', task_operator = #{operator}, modify_time = NOW()
        WHERE id = #{id}
    </update>

</mapper> 