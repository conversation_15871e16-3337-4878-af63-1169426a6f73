<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kd.beijingRoastDuck.dao.CollisionPartsDao" >

    <insert id="insertSelective" parameterType="kd.entity.CollisionParts">
        <include refid="mapping.CollisionPartsMapper.insertSelective"/>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="kd.entity.CollisionParts">
        <include refid="mapping.CollisionPartsMapper.updateByPrimaryKeySelective" />
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <include refid="mapping.CollisionPartsMapper.deleteByPrimaryKey"/>
    </delete>

    <select id="selectByPrimaryKey" resultMap="mapping.CollisionPartsMapper.BaseResultMap" parameterType="java.lang.String">
        <include refid="mapping.CollisionPartsMapper.selectByPrimaryKey"/>
    </select>

    <select id="selectByLossNo" resultMap="mapping.CollisionPartsMapper.BaseResultMap" parameterType="java.lang.String">
        <include refid="mapping.CollisionPartsMapper.selectByLossNo"/>
    </select>

    <select id="selectByLossNoAndCollisionWay" parameterType="java.util.Map" resultMap="mapping.CollisionPartsMapper.BaseResultMap">
        select
        <include refid="mapping.CollisionPartsMapper.Base_Column_List" />
        from t_collision_parts a
        where a.loss_no = #{lossNo,jdbcType=VARCHAR} and a.collision_way = #{collisionWay,jdbcType=VARCHAR}
    </select>

</mapper>