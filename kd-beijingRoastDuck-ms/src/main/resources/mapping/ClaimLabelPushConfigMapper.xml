<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.ClaimLabelPushConfigDao">

    <select id="selectByPrimaryKey" resultMap="mapping.ClaimLabelPushConfigMapper.BaseResultMap"
            parameterType="java.lang.String">
        <include refid="mapping.ClaimLabelPushConfigMapper.selectByPrimaryKey" />
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <include refid="mapping.ClaimLabelPushConfigMapper.deleteByPrimaryKey" />
    </delete>

    <insert id="insertSelective" parameterType="kd.entity.ClaimLabelPushConfig">
        <include refid="mapping.ClaimLabelPushConfigMapper.insertSelective" />
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="kd.entity.ClaimLabelPushConfig">
        <include refid="mapping.ClaimLabelPushConfigMapper.updateByPrimaryKeySelective" />
    </update>

</mapper>