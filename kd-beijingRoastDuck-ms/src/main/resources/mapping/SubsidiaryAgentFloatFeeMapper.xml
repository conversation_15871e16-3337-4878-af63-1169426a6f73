<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.SubsidiaryAgentFloatFeeDao">
    <insert id="insertSubsidiaryAgentFloatFeeList" parameterType="java.util.List">
        INSERT INTO t_subsidiary_agent_float_fee (
        id,
        ins_code,
        primary_agent_float_fee_id,
        agent_no,
        agent_name,
        province_id,
        city_id,
        area_group_id,
        area_group_name,
        cycle_seq,
        start_date,
        end_date,
        claim_settlement_start,
        claim_settlement_end,
        timely_reporting_start,
        timely_reporting_end,
        claim_settlement_ratio,
        claim_settlement_factor,
        timely_reporting_ratio,
        timely_reporting_factor,
        status,
        create_time
        )
        VALUES
        <foreach collection="subsidiaryAgentFloatFeeList" item="subsidiaryAgentFloatFee" separator=",">
            <trim prefix="(" suffix =")" suffixOverrides=",">
                #{subsidiaryAgentFloatFee.id,jdbcType=VARCHAR},
                #{subsidiaryAgentFloatFee.insCode,jdbcType=VARCHAR},
                #{subsidiaryAgentFloatFee.primaryAgentFloatFeeId,jdbcType=VARCHAR},
                #{subsidiaryAgentFloatFee.agentNo,jdbcType=VARCHAR},
                #{subsidiaryAgentFloatFee.agentName,jdbcType=VARCHAR},
                #{subsidiaryAgentFloatFee.provinceId,jdbcType=VARCHAR},
                #{subsidiaryAgentFloatFee.cityId,jdbcType=VARCHAR},
                #{subsidiaryAgentFloatFee.areaGroupId,jdbcType=VARCHAR},
                #{subsidiaryAgentFloatFee.areaGroupName,jdbcType=VARCHAR},
                #{subsidiaryAgentFloatFee.cycleSeq,jdbcType=INTEGER},
                #{subsidiaryAgentFloatFee.startDate,jdbcType=TIMESTAMP},
                #{subsidiaryAgentFloatFee.endDate,jdbcType=TIMESTAMP},
                #{subsidiaryAgentFloatFee.claimSettlementStart,jdbcType=TIMESTAMP},
                #{subsidiaryAgentFloatFee.claimSettlementEnd,jdbcType=TIMESTAMP},
                #{subsidiaryAgentFloatFee.timelyReportingStart,jdbcType=TIMESTAMP},
                #{subsidiaryAgentFloatFee.timelyReportingEnd,jdbcType=TIMESTAMP},
                #{subsidiaryAgentFloatFee.claimSettlementRatio,jdbcType=DECIMAL},
                #{subsidiaryAgentFloatFee.claimSettlementFactor,jdbcType=DECIMAL},
                #{subsidiaryAgentFloatFee.timelyReportingRatio,jdbcType=DECIMAL},
                #{subsidiaryAgentFloatFee.timelyReportingFactor,jdbcType=DECIMAL},
                #{subsidiaryAgentFloatFee.status,jdbcType=INTEGER},
                #{subsidiaryAgentFloatFee.createTime,jdbcType=TIMESTAMP},
            </trim>
        </foreach>
    </insert>

    <select id="checkSubsidiaryAgentExist" resultType="java.lang.Integer">
        select
            count(1)
        from t_subsidiary_agent_float_fee s
        where s.ins_code=#{insCode} AND s.primary_agent_float_fee_id=#{primaryAgentFloatFeeId} AND s.agent_no=#{agentNo} AND s.province_id=#{provinceId} AND s.city_id=#{cityId} AND s.area_group_id=#{areaGroupId} AND s.cycle_seq=#{cycleSeq} AND s.status=#{status}
    </select>
</mapper>