<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.beijingRoastDuck.dao.PushLogDao">
	
	<insert id="insertSelective" parameterType="kd.entity.PushLog">
		<include refid="mapping.PushLogMapper.insertSelective" />
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.PushLog">
		<include refid="mapping.PushLogMapper.updateByPrimaryKeySelective" />
	</update>

	<select id="selectByPrimaryKey" resultMap="mapping.PushLogMapper.BaseResultMap" parameterType="java.lang.String">
		<include refid="mapping.PushLogMapper.selectByPrimaryKey"/>
	</select>
</mapper>