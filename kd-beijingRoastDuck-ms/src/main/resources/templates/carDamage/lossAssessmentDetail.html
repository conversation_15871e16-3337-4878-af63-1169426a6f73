<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport" />
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <!-- Custom styles for this template -->

    <title>车辆定损详情</title>

    <!-- 引入公共CSS -->
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/carousel/dataCollection/viewer.css" rel="stylesheet">
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet"
          type="text/css"/>
    <link href="${ctx}/metronic/global/css/components.min.css" rel="stylesheet" id="style_components" type="text/css" />
    <link href="${ctx}/css/custom.css" rel="stylesheet" type="text/css" />

    <style>
        /* 修复页面滚动问题 */
        body {
            overflow-y: auto !important; /* 确保垂直滚动 */
            height: auto !important;
        }

        /* 基本信息、查看日志可以进行拖拽样式 */
        #floating-container {
            position: fixed;
            right: 20px;
            top: 55%;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .draggable-box {
            width: 60px;
            height: 60px;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 6px !important;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            user-select: none;
            border: 1px solid rgba(0, 0, 0, 0.1);
            font-size: 12px;
            color: #666;
            text-align: center;
            line-height: 1.3;
            padding: 13px;
            box-sizing: border-box;
            margin-bottom: 8px;
            cursor: move;
            transition: all 0.3s ease; /* 添加过渡效果 */

        }

        /* 鼠标悬停效果 - 边框变亮 */
        .draggable-box:hover {
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1); /* 内阴影 */
            transform: scale(1.1);
        }

        /* 选中状态 - 边框变亮且更明显 */
        .box-active {
            background-color: #ffffff;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1); /* 内阴影 */
            transform: translateY(1px); /* 模拟按下 */
        }

        .page-container {
            min-height: 100vh;
            overflow-y: auto;
        }

        .page-content-wrapper {
            overflow-y: auto;
            min-height: calc(100vh - 70px);
        }

        .page-content {
            padding-bottom: 50px; /* 底部留出空间 */
            overflow-y: auto;
        }
        .detail-form .form-group {
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }
        .detail-form .control-label {
            font-weight: bold;
            color: #333;
            text-align: right;
            padding-right: 10px;
            white-space: nowrap;
            min-width: 120px;
            width: 120px;
            flex: 0 0 120px;
        }
        .detail-form .form-control-static {
            padding: 8px 12px;
            margin-bottom: 0;
            min-height: 32px;
            border: 1px solid #e5e5e5;
            border-radius: 4px;
            background-color: #e5e5e5;
            color: #555;
            flex: 1;
            word-break: break-all;
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .page-header .logo-default {
            color: white !important;
            font-size: 18px !important;
        }

        .error-message {
            color: #e74c3c;
            font-size: 16px;
            text-align: center;
            padding: 40px 0;
        }
        /* 费用表格样式 */
        .cost-table {
            margin-top: 10px;
        }
        .cost-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
            vertical-align: middle;
        }
        .cost-table td {
            text-align: right;
            vertical-align: middle;
        }
        .cost-table .amount {
            color: #000000;
            font-weight: bold;
        }
        /* 自适应响应式布局 */
        .detail-form .row {
            margin-left: -8px;
            margin-right: -8px;
            margin-bottom: 8px;
        }
        .detail-form .row .col-md-4 {
            padding-left: 8px;
            padding-right: 8px;
        }

        /* 面包屑导航样式 */
        .page-breadcrumb {
            padding: 0;
            margin: 0 0 25px 0;
            background-color: transparent;
            border-radius: 0;
        }
        .page-breadcrumb > li + li:before {
            content: ">";
            padding: 0 5px;
            color: #777;
        }
        .page-title h1 {
            margin: 0;
            font-size: 24px;
            color: #333;
            font-weight: 300;
        }

        .cost-table .cost-type-col {
            text-align: center;
        }

        /* 卡片样式优化 */
        .portlet {
            margin-bottom: 5px;
        }
        .portlet .portlet-title {
            padding: 15px 20px;
            min-height: 48px;
            border-bottom: 1px solid #eee;
            position: relative;
        }
        .portlet .portlet-title .caption {
            float: left;
            display: inline-block;
            font-size: 18px;
            line-height: 18px;
        }
        .portlet .portlet-body {
            padding: 20px;
        }

        /* 自适应表格 */
        .table-responsive {
            overflow-x: auto;
        }

        /* 手机端优化 */
        @media (max-width: 768px) {
            body {
                padding-top: 60px !important; /* 手机端调整顶部间距 */
            }

            .detail-form .form-group {
                flex-direction: column;
                align-items: flex-start;
            }
            .detail-form .control-label {
                text-align: left;
                margin-bottom: 5px;
                min-width: auto;
                width: auto;
                flex: none;
            }
            .detail-form .form-control-static {
                width: 100%;
            }
            .cost-table th,
            .cost-table td {
                font-size: 12px;
                padding: 5px;
            }

            .page-content {
                padding-left: 10px;
                padding-right: 10px;
            }
        }

        /* 确保所有容器都能正常滚动 */
        html, body {
            overflow-x: hidden;
            overflow-y: auto !important;
        }

        * {
            box-sizing: border-box;
        }

        /* 确保按钮区域可见 */
        .portlet .portlet-body {
            min-height: 50px;
        }

        .portlet.light .portlet-body{
            padding: 1px;
        }
        .btn {
            margin-right: 10px;
            min-width: 80px;
        }

        .btn:last-child {
            margin-right: 0;
        }

        /* 强制显示按钮容器 */
        .text-right {
            display: block !important;
            visibility: visible !important;
        }

        @media (min-width: 992px) {
            .page-content-wrapper .page-content {
                margin-left: 0px!important;
            }
        }

        .center-box {
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            justify-content: center;
            align-items: center;

            /* 添加粘性定位 */
            position: sticky;
            top: 0; /* 固定在顶部 */
            z-index: 1000;

            /* 过渡动画 */
            transition: all 0.3s ease;
        }

        /* 滚动时添加的样式（可选） */
        .fixed-style {
            width: 100%;
            position: fixed;
            border-radius: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        /* 工具提示内容 */
        .tooltip-content {
            position: absolute;
            transform: translateX(-100%);
            padding: 10px;
            margin-bottom: 10px;
            background-color: #333;
            color: #fff;
            border-radius: 4px;
            font-size: 14px;
            line-height: 1.4;
            opacity: 0;
            visibility: hidden;
            z-index: 100;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            text-align: left;
        }

        /* 悬停时显示 */
        .icon-question:hover .tooltip-content {
            opacity: 1;
            visibility: visible;
        }
    </style>
</head>

<body class="page-header-fixed page-content-white">

<!-- 页面容器 -->
<div class="page-container" style="margin-top: 0px">
    <!-- 页面内容包装器 -->
    <div class="page-content-wrapper">
        <!-- 页面内容 -->
        <div class="page-content" style="margin-left: 0; padding-left: 0px!important;">
            <!-- 面包屑导航 -->
            <div class="page-bar">
                <ul class="page-breadcrumb">
                    <li>
                        <a href="${ctx}/claimCaseObjectController/claimCaseObjectList?comeFrom=1&status=0">车辆定损</a>
                    </li>
                    <li>任务详情</li>
                </ul>
            </div>

            <!-- 案件信息和快捷操作栏 -->

            <div class="row center-box" id="stickyBox" style="margin-bottom: 10px; background-color: white; border-radius: 5px; border: 1px solid #dee2e6;">

                <div class="row" style="padding-top: 10px">
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group" style="margin-bottom: 0;">
                                    <label class="control-label" style="font-weight: bold;">报案号:</label>
                                    <span style="margin-left: 10px; font-size: 14px;">${claimCase.claimCaseNo!''}</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group" style="margin-bottom: 0;">
                                    <label class="control-label" style="font-weight: bold;">立案号:</label>
                                    <span style="margin-left: 10px; font-size: 14px;">${claimCase.insuranceCaseNo!''}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="pull-right">
                            <button name="actionBtn" type="button" class="btn btn-default btn-sm red" onclick="callLossAssessment()">
                                定损
                            </button>
                            <button name="actionBtn" type="button" class="btn btn-default btn-sm yellow" onclick="seeLossAssessment()">
                                查看定损信息
                            </button>
                            <button name="actionBtn" type="button" class="btn btn-default btn-sm white" onclick="saveLossAssessment('BAX21')"style="color: #f5f5f5;background-color: #afa7a7;border-color: #fff;">
                                保存
                            </button>
                            <button name="actionBtn" type="button" class="btn btn-default btn-sm blue" onclick="submitLossAssessment(<#if claimCaseObject.status == 'BAX27'>'BAX25'<#else>'BAX22'</#if>)">
                                提交审核
                            </button>
                        </div>
                    </div>
                </div>


                <!-- 定损费用合计 -->
                <div class="row" style="margin-top: 1px">
                    <div>
                        <div class="portlet light bordered" style="border: 0px solid #ffffff !important;">
                            <div class="portlet-title" style="border-bottom: 0; margin-bottom: 0">
                                <div class="caption">
                                    <i class="icon-bar-chart font-dark"></i>
                                    <span class="caption-subject font-dark sbold uppercase">损失费用合计</span>
                                </div>

                                <div class="portlet-body">

                                    <form id="assessmentRecordForm">
                                        <input type="hidden" name="claimCaseObjectId" id="claimCaseObjectId" value="${claimCaseObject.id!''}">

                                        <div class="row">
                                            <div class="col-md-2" style="margin-left: 1rem">
                                                <div class="form-group" style="display: flex; align-items: center">
                                                    <label class="control-label">赔付方式:</label>
                                                    <select name="paymentMethod" id="paymentMethod" class="form-control" style="flex: 1; margin-left: 0.1rem">
                                                        <option value="" <#if !claimFirst?? || claimFirst == 0>selected</#if>>正常赔付</option>
                                                        <option value="ACX020" <#if claimFirst?? && claimFirst == 1>selected</#if>>先行赔付</option>
<!--                                                        <option value="分段赔付" <#if assessmentRecord?? && assessmentRecord.paymentMethod == '分段赔付'>selected</#if>>分段赔付</option>-->
<!--                                                        <option value="无需赔付" <#if assessmentRecord?? && assessmentRecord.paymentMethod == '无需赔付'>selected</#if>>无需赔付</option>-->
<!--                                                        <option value="一次性调解" <#if assessmentRecord?? && assessmentRecord.paymentMethod == '一次性调解'>selected</#if>>一次性调解</option>-->
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-8">
                                                <div class="form-group col-md-4" style="display: flex">
                                                    <label class="control-label">是否诉讼:</label>
                                                    <div style="flex: 1">
                                                        <label class="radio-inline">
                                                            <input type="radio" name="litigation" value="ACX003" <#if litigation?? && litigation == 1>checked</#if>> 是
                                                        </label>
                                                        <label class="radio-inline">
                                                            <input type="radio" name="litigation" value="" <#if !litigation?? || litigation == 0>checked</#if>> 否
                                                        </label>
                                                    </div>
                                                </div>

                                                <div class="form-group col-md-4" style="display: flex">
                                                    <label class="control-label">是否代位追偿:</label>
                                                    <div style="flex: 1">
                                                        <label class="radio-inline">
                                                            <input type="radio" name="subrogation" value="ACX018" <#if subrogation?? && subrogation == 1>checked</#if>> 是
                                                        </label>
                                                        <label class="radio-inline">
                                                            <input type="radio" name="subrogation" value="" <#if !subrogation?? || subrogation == 0>checked</#if>> 否
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            <div class="portlet-body" style="padding-top: 0">
                                <div class="table-responsive">
                                    <table class="table table-bordered cost-table">
                                        <thead>
                                        <tr>
                                            <th width="8%">类型</th>
                                            <th width="10%">合计配件金额</th>
                                            <th width="10%">配件管理费金额</th>
                                            <th width="10%">合计残值金额</th>
                                            <th width="10%">合计自付金额</th>
                                            <th width="10%">合计工时金额</th>
                                            <th width="10%">合计辅料金额</th>
                                            <th width="10%">合计外修金额</th>
                                            <th width="10%">合计施救费用金额</th>
                                            <th width="15%">总合计(不含其他费用)</th>
                                            <th>其他费用<i class="icon-question"></i></th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <#if costList?? && costList?size gt 0>
                                        <#list costList as cost>
                                        <tr>
                                            <td class="cost-type-col">${carAssessmentTypeMap.get(cost.assessmentType)}</td>
                                            <td class="amount"><#if cost.totalPartsAmount??>${cost.totalPartsAmount?string("0.00")}<#else>0.00</#if></td>
                                            <td class="amount"><#if cost.partsManagementFee??>${cost.partsManagementFee?string("0.00")}<#else>0.00</#if></td>
                                            <td class="amount"><#if cost.totalResidualValue??>${cost.totalResidualValue?string("0.00")}<#else>0.00</#if></td>
                                            <td class="amount"><#if cost.totalOutOfPocket??>${cost.totalOutOfPocket?string("0.00")}<#else>0.00</#if></td>
                                            <td class="amount"><#if cost.totalLaborAmount??>${cost.totalLaborAmount?string("0.00")}<#else>0.00</#if></td>
                                            <td class="amount"><#if cost.totalSuppliesAmount??>${cost.totalSuppliesAmount?string("0.00")}<#else>0.00</#if></td>
                                            <td class="amount"><#if cost.totalExternalRepair??>${cost.totalExternalRepair?string("0.00")}<#else>0.00</#if></td>
                                            <td class="amount"><#if cost.totalRescueFee??>${cost.totalRescueFee?string("0.00")}<#else>0.00</#if></td>
                                            <td class="amount"><#if cost.totalExcludingRescue??>${cost.totalExcludingRescue?string("0.00")}<#else>0.00</#if></td>
                                            <td class="amount"><#if cost.otherCostSum??>${cost.otherCostSum}<#else>0.00</#if></td>
                                        </tr>
                                        </#list>
                                        <#else>
                                        <tr>
                                            <td class="cost-type-col">定损</td>
                                            <td class="amount">0.00</td>
                                            <td class="amount">0.00</td>
                                            <td class="amount">0.00</td>
                                            <td class="amount">0.00</td>
                                            <td class="amount">0.00</td>
                                            <td class="amount">0.00</td>
                                            <td class="amount">0.00</td>
                                            <td class="amount">0.00</td>
                                            <td class="amount">0.00</td>
                                            <td class="amount">0.00</td>
                                        </tr>
                                        </#if>
                                        </tbody>
                                    </table>
                                </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


<div class="row" style="margin: 0px -15px">
    <div class="col-md-12">
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption">
                    <i class="icon-picture font-dark"></i>
                    <span class="caption-subject font-dark sbold uppercase">影像资料</span>
                </div>
            </div>

            <div class="portlet-body">
                <#include "/attach/attachComponent.html">
            </div>
        </div>
    </div>
</div>


    <!-- 拖拽浮窗 -->
    <div id="floating-container">
        <div id="box1" class="draggable-box box-active" onclick="showBasicInformation('${claimCase.id!''}', '${claimCaseObject.id!''}')">
            基本信息
        </div>
        <div id="box2" class="draggable-box" onclick="showDetailLog('${claimCaseObject.id!''}')">
            查看日志
        </div>
    </div>


</div>
</div>
</div>

<!-- 引入layer插件 -->
<script src="${ctx}/carousel/dataCollection/viewer.js"></script>
<script src="${ctx}/carousel/dataCollection/viewermain.js"></script>
<script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
<script src="${ctx}/metronic/layouts/layout4/scripts/layout.min.js" type="text/javascript"></script>
<script src="${ctx}/metronic/layouts/layout4/scripts/demo.min.js" type="text/javascript"></script>
<script src="${ctx}/metronic/global/plugins/jquery-multi-select/js/jquery.multi-select.js"
        type="text/javascript"></script>
<script type="text/javascript">
    jQuery(document).ready(function() {
        App.init();
    });

        let isCostDataLoading = false;
        let feeInfoJsonStr ;
        let isInitFeeInfo = true;
        let costRefreshTimer = null;

        $(function (){
            stopCostAutoRefresh();
            findFeeInfo()
            // 每隔3秒查询损失费用合计信息
            costRefreshTimer = setInterval(function () {
                findFeeInfo()
            }, 3000)
            console.log("costRefreshTimer=" + costRefreshTimer)
        })

        // 停止自动刷新
        function stopCostAutoRefresh() {
            if (costRefreshTimer) {
                console.log("停止循环......")
                clearInterval(costRefreshTimer);
                costRefreshTimer = null;
            }
        }

        function showCostError(msg) {
            layer.msg(msg || '网络请求失败，请稍后重试！', {
                icon: 2,
                offset: ['450px', ''],
                time: 2000
            });
        }

    //损失费用合计查询方法 Ajax获取费用数据
    function findFeeInfo() {
        // 可以添加加载指示器
        $.ajax({
            url: '${ctx}/insuranceNuclearAudit/findCostDataByLossNo?claimCaseObjectId=${claimCaseObject.id}', // 替换为实际接口地址
            type: 'POST',
            async: false,
            cache: false,
            contentType: false,
            processData: false,
            success: function(data) {
                let resData = JSON.stringify(data)
                if(feeInfoJsonStr != resData){
                    feeInfoJsonStr = resData
                    updateCostTable(JSON.parse(resData));
                }
            },
            error: function(xhr, status, error) {
                console.error('获取费用数据失败:', error);
				stopCostAutoRefresh();
                showCostError('获取费用数据失败，请稍后重试');
            },
            complete: function() {
                isCostDataLoading = false;

            }
        });
    }

    // 更新费用表格
    function updateCostTable(result) {
        let costList = result.costList;
        const $container = $('.table-responsive');
        $container.html('');
        if (costList && costList.length > 0) {
            let tableDiv = $('<table class="table table-bordered cost-table"></table>')
            if(result.totalCostInfo && result.totalCostInfo !== ""){
                tableDiv.append('<thead><tr><th width="8%">类型</th><th width="10%">合计配件金额</th><th width="10%">配件管理费金额</th><th width="10%">合计残值金额</th><th width="10%">合计自付金额</th><th width="10%">合计工时金额</th><th width="10%">合计辅料金额</th><th width="10%">合计外修金额</th><th width="10%">合计施救费用金额</th><th width="15%">总合计(不含其他费用)</th><th width="15%">其他费用<i class="icon-question"><div class="tooltip-content">' + result.totalCostInfo + '</div></i> </th></tr></thead>');
            }else {
                tableDiv.append('<thead><tr><th width="8%">类型</th><th width="10%">合计配件金额</th><th width="10%">配件管理费金额</th><th width="10%">合计残值金额</th><th width="10%">合计自付金额</th><th width="10%">合计工时金额</th><th width="10%">合计辅料金额</th><th width="10%">合计外修金额</th><th width="10%">合计施救费用金额</th><th width="15%">总合计(不含其他费用)</th><th width="15%">其他费用<i class="icon-question"></i> </th></tr></thead>');
            }
            let tbodyDiv = $('<tbody></tbody>')
            for(let cost of costList){
                tbodyDiv.append('<tr><td class="cost-type-col">'+cost.assessmentType+'</td><td class="amount">'+cost.totalPartsAmount.toFixed(2)+'</td><td class="amount">'+cost.partsManagementFee.toFixed(2)+'</td><td class="amount">'+cost.totalResidualValue.toFixed(2)+'</td><td class="amount">'+cost.totalOutOfPocket.toFixed(2)+'</td><td class="amount">'+cost.totalLaborAmount.toFixed(2)+'</td><td class="amount">'+cost.totalSuppliesAmount.toFixed(2)+'</td><td class="amount">'+cost.totalExternalRepair.toFixed(2)+'</td><td class="amount">'+cost.totalRescueFee.toFixed(2)+'</td><td class="amount" style="font-weight: bold; color: #e74c3c;">'+cost.totalExcludingRescue.toFixed(2)+'</td><td class="amount">'+ (parseFloat(cost.otherCostSum) || 0).toFixed(2) +'</td></tr>')
            }
            tableDiv.append(tbodyDiv)
            $container.append(tableDiv);
        } else {
            let tableDiv = $('<table class="table table-bordered cost-table"></table>')
            tableDiv.append('<thead><tr><th width="8%">类型</th><th width="10%">合计配件金额</th><th width="10%">配件管理费金额</th><th width="10%">合计残值金额</th><th width="10%">合计自付金额</th><th width="10%">合计工时金额</th><th width="10%">合计辅料金额</th><th width="10%">合计外修金额</th><th width="10%">合计施救费用金额</th><th width="15%">总合计(不含其他费用)</th><th width="15%">其他费用<i class="icon-question"></i></th></tr></thead>');
            let tbodyDiv = $('<tbody></tbody>')
            tbodyDiv.append('<tr><td class="cost-type-col">定损</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount">0.00</td><td class="amount" style="font-weight: bold; color: #e74c3c;">0.00</td><td class="amount">0.00</td></tr>')
            tableDiv.append(tbodyDiv)
            $container.append(tableDiv);
        }
        if(isInitFeeInfo){
            isInitFeeInfo = false
        }else {
     /*       layer.msg('损失费用合计已变更', {
                icon: 1,
                offset: ['200px', ''],
                time: 2000
            });*/
        }
    }

    window.addEventListener('scroll', function() {
        const box = document.getElementById('stickyBox');
        if (window.scrollY > 150) { // 滚动超过200px时添加额外样式
            box.classList.add('fixed-style');
        } else {
            box.classList.remove('fixed-style');
        }
    });

    function callLossAssessment(){
        let claimCaseObject = assembleData(status);

        $.ajax({
            url: '${ctx}/claimCaseObject4CarController/callLossAssessment',
            type: 'POST',
            data: JSON.stringify(claimCaseObject),
            async: true,
            cache: false,
            contentType: false,
            processData: false,
            success: function (data) {
                var result = eval("(" + data + ")");
                if (result.ret == "0")  {
                    $("button[name='actionBtn']").removeAttr("disabled");
                    window.open(result.url);
                }else {
                    $("button[name='actionBtn']").removeAttr("disabled");
                    alert(result.msg);
                }
            },
            error: function (data) {
                $("button[name='actionBtn']").removeAttr("disabled");
                var result = eval("(" + data + ")");
                alert(result.msg);
            }
        });
    }

    function seeLossAssessment(){
        let claimCaseObject = assembleData(status);

        $.ajax({
            url: '${ctx}/claimCaseObject4CarController/seeLossAssessment',
            type: 'POST',
            data: JSON.stringify(claimCaseObject),
            async: true,
            cache: false,
            contentType: false,
            processData: false,
            success: function (data) {
                var result = eval("(" + data + ")");
                if (result.ret == "0")  {
                    $("button[name='actionBtn']").removeAttr("disabled");
                    window.open(result.url);
                }else {
                    $("button[name='actionBtn']").removeAttr("disabled");
                    alert(result.msg);
                }
            },
            error: function (data) {
                $("button[name='actionBtn']").removeAttr("disabled");
                var result = eval("(" + data + ")");
                alert(result.msg);
            }
        });
    }


    // 暂存功能
    function saveLossAssessment(status) {
        layer.prompt({
            title: '请输入意见备注',
            formType: 2, // 0-文本输入框（默认），1-密码框，2-多行文本框
            value: '', // 默认内容
            maxlength: 200, // 最大输入长度
            area: ['740px', '490px'],
            btn: ['确定', '取消'],
            yes: function(index, elem){
                let remark = elem.find('.layui-layer-input').val();

                if (remark === '') {
                    layer.msg('请输入意见备注', {
                        icon: 2,
                        offset: ['450px', ''],
                        time: 2000
                    });
                    return false; // 阻止关闭
                }

                // 收集定损记录数据
                let claimCaseObject = assembleData(status);
                claimCaseObject['logRemark'] = remark;

                $.ajax({
                    url: '${ctx}/claimCaseObject4CarController/saveLossAssessment4Car',
                    type: 'POST',
                    data: JSON.stringify(claimCaseObject),
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0")  {
                            layer.msg(result.msg, {
                                icon: 1,
                                time: 2000,
                                shade: [0.0001, '#000']
                            },function(){
                                $("button[name='actionBtn']").removeAttr("disabled");
                            });
                        } else {
                            $("button[name='actionBtn']").removeAttr("disabled");
                            alert(result.msg);
                        }
                    },
                    error: function (data) {
                        $("button[name='actionBtn']").removeAttr("disabled");
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                });
            }
        });
    }

    // 提交评估
    function submitLossAssessment(status) {
    /*    if (confirm('确定要提交定损评估吗？提交后将无法修改')) {


        }*/

        // 收集定损记录数据
        let claimCaseObject = assembleData(status);

        $.ajax({
            url: '${ctx}/claimCaseObject4CarController/saveLossAssessment4Car',
            type: 'POST',
            data: JSON.stringify(claimCaseObject),
            async: true,
            cache: false,
            contentType: false,
            processData: false,
            success: function (data) {
                var result = eval("(" + data + ")");
                if (result.ret == "0")  {
                    //跳转确认页面
                    window.location.href = "${ctx}/claimCaseObject4CarController/submitConfirmPage?reportNo=${claimCaseObject.claimCaseNo}&insCode=${claimCaseObject.insCode}&checkCondition=${checkCondition}&status=" + result.status;
                }else {
                    $("button[name='actionBtn']").removeAttr("disabled");
                    alert(result.msg);
                }

            },
            error: function (data) {
                $("button[name='actionBtn']").removeAttr("disabled");
                var result = eval("(" + data + ")");
                alert(result.msg);
            }
        });
    }


    function showBasicInformation(claimCaseId, claimCaseObjectId){
        layer.open({
            title: "查看基本信息",
            type: 2,
            offset: 'auto',
            area: ['1200px', '700px'],
            fixed: false,
            closeBtn: 1,
            maxmin: true,
            content: "${ctx}/insuranceNuclearAudit/showBasicInformation?claimCaseId=" + claimCaseId + "&claimCaseObjectId=" + claimCaseObjectId,
            success: function (layero, index) {
                layer.iframeAuto(index);
            }
        });
    }

    //可拖动基本信息、查看日志
    const container = document.getElementById('floating-container');
    const boxes = {
        box1: document.getElementById('box1'),
        box2: document.getElementById('box2')
    };

    const controlButtons = document.querySelectorAll('.control-btn');
    let activeBox = boxes.box1;
    let isDragging = false;
    let offsetX, offsetY;

    // 切换活动浮框
    function setActiveBox(box) {
        activeBox.classList.remove('box-active');
        activeBox = box;
        activeBox.classList.add('box-active');
    }

    // 控制面板按钮事件
    controlButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            setActiveBox(boxes[this.dataset.target]);
        });
    });

    // 容器拖拽事件
    container.addEventListener('mousedown', function(e) {
        // 只有当点击的是容器或浮框时才触发拖拽
        if (e.target === container || e.target.classList.contains('draggable-box')) {
            isDragging = true;
            offsetX = e.clientX - container.getBoundingClientRect().left;
            offsetY = e.clientY - container.getBoundingClientRect().top;

            container.style.transition = 'none';
            Array.from(container.children).forEach(box => {
                box.classList.add('dragging');
            });

            e.preventDefault();
        }
    });

    // 鼠标移动事件
    document.addEventListener('mousemove', function(e) {
        if (!isDragging) return;

        let newLeft = e.clientX - offsetX;
        let newTop = e.clientY - offsetY;

        // 限制位置（防止移出屏幕）
        const maxLeft = window.innerWidth - container.offsetWidth;
        const maxTop = window.innerHeight - container.offsetHeight;

        newLeft = Math.max(0, Math.min(newLeft, maxLeft));
        newTop = Math.max(0, Math.min(newTop, maxTop));

        container.style.left = newLeft + 'px';
        container.style.top = newTop + 'px';
        container.style.right = 'auto';
    });

    // 鼠标释放事件
    document.addEventListener('mouseup', function(e) {
        if (!isDragging) return;
        isDragging = false;

        container.style.transition = 'all 0.3s ease';
        Array.from(container.children).forEach(box => {
            box.classList.remove('dragging');
        });

        // 判断吸附到左侧还是右侧
        const containerRect = container.getBoundingClientRect();
        const screenCenter = window.innerWidth / 2;
        const containerCenter = containerRect.left + containerRect.width / 2;

        if (containerCenter < screenCenter) {
            container.style.left = '20px';
            container.style.right = 'auto';
        } else {
            container.style.left = 'auto';
            container.style.right = '20px';
        }
    });

    // 窗口大小变化时重新定位
    window.addEventListener('resize', function() {
        const containerRect = container.getBoundingClientRect();

        if (parseInt(container.style.left) > 0 || container.style.left === '') {
            container.style.left = '20px';
            container.style.right = 'auto';
        } else {
            container.style.left = 'auto';
            container.style.right = '20px';
        }

        const maxTop = window.innerHeight - container.offsetHeight;
        const currentTop = parseInt(container.style.top || containerRect.top);
        container.style.top = Math.max(0, Math.min(currentTop, maxTop)) + 'px';
    });

    // 点击浮框切换活动状态
    Object.values(boxes).forEach(box => {
        box.addEventListener('click', function() {
            setActiveBox(this);
        });
    });


    // 查看日志（保司侧）
    function showDetailLog(claimCaseObjectId){
        layer.open({
            type: 2,
            title: '查看日志',
            content: "${ctx}/insuranceNuclearAudit/showDetailLog?claimCaseObjectId=" + claimCaseObjectId,
            area: ['1200px', '400px'],
            maxmin: true, // 启用最大化按钮
            scrollbar: false, // 禁用layer自带的滚动条
            success: function(layero, index) {
                console.log('弹框当前处于状态');
                layer.iframeAuto(index);
            },
            full: function(layero) {
                console.log('弹框当前处于最大化状态');
                // 最大化时添加全屏类
                var iframe = $(layero).find('iframe');
                try {
                    var iframeDoc = iframe[0].contentDocument || iframe[0].contentWindow.document;
                    $(iframeDoc).find('.table-responsive').css({
                        'max-height': 'calc(110vh - 200px)', // 根据实际布局调整
                        'overflow-y': 'auto'  // 保持滚动条
                    });
                    $(iframeDoc).find('tbody tr').show();

                } catch(e) {
                    console.log('样式注入错误:', e);
                }
            },
            restore: function(layero) {
                console.log('弹框当前处于恢复状态');
                // 恢复时移除全屏类
                var iframe = $(layero).find('iframe');
                try {
                    var iframeDoc = iframe[0].contentDocument || iframe[0].contentWindow.document;
                    $(iframeDoc).find('.table-responsive').css({
                        'max-height': 'calc(6 * 40px + 41px)',
                        'overflow-y': 'auto'
                    });
                } catch(e) {
                    console.log('样式注入错误:', e);
                }
            }
        });
    }

    // 组装数据
    function assembleData(status) {

        let isError = false;
        let errorMsg = "";

        $("button[name='actionBtn']").attr('disabled','1');

        let claimCaseObjectId = $("#claimCaseObjectId").val().trim();
        if (!claimCaseObjectId) {
            isError = true;
            errorMsg += "赔付对象不能为空！</br>";
        }


        let object = {};
        object["id"] = "${claimCaseObject.id}";
        object["claimCaseId"] = "${claimCaseObject.claimCaseId}";
        object["claimCaseNo"] = "${claimCaseObject.claimCaseNo}";
        object["type"] = "${claimCaseObject.type}";
        object["category"] = "${claimCaseObject.category}";
        object["comeFrom"] = "${comeFrom}";
        object["checkCondition"] = "${checkCondition}";
        object["litigation"] = $('input[name="litigation"]:checked').val();
        object["subrogation"] = $('input[name="subrogation"]:checked').val();
        object["paymentMethod"] = $('#paymentMethod').val();
        object["status"] = status;

        if(isError){
            layer.msg(errorMsg, {icon: 2, time: 3000},function () {
                $("button[name='actionBtn']").removeAttr("disabled");
            });
            return;
        }

        console.log(JSON.stringify(object));

        return object;
    }
</script>

</body>
</html> 