<!--[if IE 8]> <html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
<!--<![endif]-->
<head>
<meta charset="utf-8" />
<title>新增用户</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1" name="viewport" />
<meta content="" name="description" />
<meta content="" name="author" />
<#include "/common/cssResource.html">
<!-- BEGIN PAGE LEVEL PLUGINS -->
<link href="${ctx}/metronic/global/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet" type="text/css" />
<!-- END PAGE LEVEL PLUGINS -->

<#include "/common/jsResource.html">
<!-- BEGIN PAGE LEVEL PLUGINS -->
<script src="${ctx}/metronic/global/plugins/jstree/dist/jstree.min.js" type="text/javascript"></script>
<!-- END PAGE LEVEL PLUGINS -->

<script type="text/javascript">

	$(document).ready(function() {
		
		$('#resourceGrantTree').jstree({
			'plugins' : [ "contextmenu", "wholerow" ],
			"contextmenu" : {
				"items" : genContextMenu4EachNode
			}, 
			'core' : {
				// 必须要有'check_callback': true的配置，否则无法新建node
				"check_callback" : true,
				"themes" : {
					"responsive" : false,
					"stripes" : true
				},
				'data' : [ 
				<#list resourceList as r>
					{	"id" : "${r.id}", 
						<#if r.parentId != null>
							"parent" : "${r.parentId}",
						<#else>
							// 根节点必须要设置为#
							"parent" : "#", 
						</#if>
						<#if r.type == 0>
							"state" : {
								"opened" : true
							},
							"icon" : "fa fa-folder icon-state-warning icon-lg",
						<#elseif r.type == 1>
							"state" : {
								"opened" : true
							},
							"icon" : "fa fa-file icon-state-success",
						<#else>
							"icon" : "fa fa-leaf icon-state-danger",
						</#if>
						"li_attr" : {"type" : ${r.type}},
						"text" : "${r.name}" 
					},
				</#list>
				]
			}
		});
		
		function genContextMenu4EachNode(node) {

		    var items = {
	    		"newCom" : {  
	                "label" : "新建公司",
	                "separator_after" : true,
	                "action" : function(data){  
	                    var ref = jQuery.jstree.reference(data.reference);
	                    var obj = ref.get_node(data.reference);  
	                    alert(obj.id);
	                    var id = ref.create_node(obj, { "text" : "mynode", 
	                    								"icon" : "fa fa-file icon-state-success", 
	                    								"state" : { "opened" : true } });
	                    $('#xx').attr("src", "${ctx}/testController/nodeCompanyNew");
	                }  
	            },  
	            "newDep" : {  
	                "label" : "新建部门",
	                "separator_after" : true,
	                "action" : function(data){  
	                    var ref = jQuery.jstree.reference(data.reference);
	                    var obj = ref.get_node(data.reference);  
	                    var id = ref.create_node(obj, { "text" : "mynode", 
                										"icon" : "fa fa-leaf icon-state-danger" });
	                    $('#xx').attr("src", "${ctx}/testController/nodeDepartmentNew");
	                }  
	            }
		    }
		    if (!node.li_attr.hasOwnProperty("type")) {
		    	alert("请先保存当前信息");
		    	return null;
		    }
		    if (node.li_attr.type == 2) {
		    	return null;
		    }
		    return items;
		}
		
	});

</script>

</head>
<body>
	<div class="portlet light bordered">
		<div class="portlet-body form">
			<!-- BEGIN FORM-->
			<form id="inputForm" class="form-horizontal form-row-seperated">
				<div class="form-body">
					<div class="row">
						<div class="col-sm-4">
							<div id="resourceGrantTree" class="tree"> </div>
						</div>
						<div class="col-sm-8">
							<iframe id="xx" src="${ctx}/testController/nodeGroupNew" width="100%" height="91%" frameborder="0"></iframe>
						</div>
					</div>
				</div>
				<div class="form-actions">
					<div class="row">
						<div class="col-sm-6">
							<div class="row">
								<div class="col-sm-offset-3 col-sm-9">
									<button type="button" class="btn green" onclick="grantResource()">提交</button>
									<button type="button" class="btn default"
										onclick="parent.layer.closeAll();">取消</button>
								</div>
							</div>
						</div>
						<div class="col-sm-6"></div>
					</div>
				</div>
			</form>
		</div>
		<!-- END FORM-->
	</div>
</body>
</html>