<!--[if IE 8]> <html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
    <!--<![endif]-->
<head>
<meta charset="utf-8" />
<title>角色管理</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1" name="viewport" />
<meta content="" name="description" />
<meta content="" name="author" />
<#include "/common/cssResource.html">
<#include "/common/jsResource.html">
<script type="text/javascript">

$(document).ready(function() {
	$("#platform${platform}").attr("class", "active");

});

function page(n,s){
	$("#pageNum").val(n);
	$("#pageSize").val(s);
	$("#searchForm").submit();
	return false;
}

function openNew(){
	var openWindowWidth = $(document).width() * 0.8 + "px";
	var offsetH = ($(window).height()/5 - 20 > 120 ? 120 : $(window).height()/5 - 20) + "px";
	layer.open({
		type: 2,
		area: openWindowWidth,
		offset : offsetH,
		fix: false, //不固定
		maxmin: true,
		content: '${ctx}/roleController/form?operationType=new&platform=${platform}',
		success: function(layero, index){
		    layer.iframeAuto(index);
		}
	});
}

function openEdit(roleId){
	var openWindowWidth = $(document).width() * 0.8 + "px";
	var offsetH = ($(window).height()/5 - 20 > 120 ? 120 : $(window).height()/5 - 20) + "px";
	layer.open({
		type: 2,
		area: openWindowWidth,
		offset : offsetH,
		fix: false, //不固定
		maxmin: true,
		content: '${ctx}/roleController/form?id='+roleId+'&operationType=edit&platform=${platform}',
		success: function(layero, index){
		    layer.iframeAuto(index);
		}
	});
}

function grantResourcePage(roleId){
	var openWindowWidth = $(document).width() * 0.8 + "px";
	var openWindowHeight = $(document).height() * 0.8 + "px";
	layer.open({
		type: 2,
		area: [openWindowWidth, openWindowHeight],
		offset : '60px',
		fix: false, //不固定
		maxmin: true,
		content: '${ctx}/roleController/grantResourcePage?platform=${platform}&roleId=' + roleId
	});
}

function submitForm(tabIdx) {
	if(tabIdx == 0){
		$("#platform").val("0");
	}
	$('#searchForm').submit();
}

</script>

</head>
<body>
	<!-- BEGIN PAGE BASE CONTENT -->
	<div class="row">
		<div class="col-sm-12">
			<!-- BEGIN EXAMPLE TABLE PORTLET-->
			<div class="portlet light portlet-fit bordered">
				<div class="portlet-title">
					<ul class="page-breadcrumb breadcrumb">
						<li>新职业平台 <i class="fa fa-circle"></i></li>
						<li><span class="active">角色管理</span></li>
					</ul>
				</div>
				<div class="portlet-body">
					<div class="table-toolbar">
						<div class="row">
							<div class="col-sm-12">
								<div class="btn-group pull-right">
									<button id="sample_editable_1_new" class="btn green" onclick="openNew()">新增</button>
								</div>
							</div>
						</div>
					</div>
					<ul class="nav nav-tabs">
						<li id="platform0"><a data-toggle="tab" onclick="submitForm(0);"> 系统端 </a></li>
					</ul>
					<table class="table table-striped table-bordered table-hover table-header-fixed"
						id="roleTable">
						<thead>
							<tr>
								<th>角色名</th>
								<th>状态</th>
								<th>备注</th>
								<th>操作</th>
							</tr>
						</thead>
						<tbody>
							<#list page.list as role>
								<tr>
									<td class="col-sm-3" style="word-wrap:break-word;word-break:break-all;">${role.name}</td>
									<td class="col-sm-1"><#if role.status = 1> 有效 <#else> 无效 </#if></td>
									<td class="col-sm-6" style="word-wrap:break-word;word-break:break-all;">${role.remark}</td>
									<td class="col-sm-2">
										<a href="#" onclick="openEdit('${role.id}')">编辑</a>
										<a href="#" onclick="grantResourcePage('${role.id}')">授权</a>
										<a href="${ctx}/roleController/deleteRole?id=${role.id}&platform=0" onclick="return confirm('确认要执行 删除 操作吗？', this.href)">删除</a>
									</td>
								</tr>
							</#list>
						</tbody>
					</table>
				</div>
			</div>
			<!-- END EXAMPLE TABLE PORTLET-->
		</div>
	</div>
	<!-- END PAGE BASE CONTENT -->
	<@sc.pagination page=page />

	<form id="searchForm" action="${ctx}/roleController/roleList" method="post">
		<input id="pageNum" name="pageNum" type="hidden" value="${page.pageNum}" />
		<input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}" />
		<input id="platform" name="platform" type="hidden" value="${platform}" />
	</form>
</body>
</html>