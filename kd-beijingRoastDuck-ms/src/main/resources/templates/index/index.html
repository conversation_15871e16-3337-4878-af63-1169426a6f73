<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <!-- Custom styles for this template -->
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">

    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <script src="${ctx}/metronic/pages/scripts/components-select2.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script src="${ctx}/js/index.js?v=1" type="text/javascript"></script>
    <#--widget-->
    <script src="${ctx}/metronic/global/plugins/counterup/jquery.waypoints.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/counterup/jquery.counterup.min.js" type="text/javascript"></script>
    <#--echarts-->
    <script src="${ctx}/a/echarts.min.js"></script>

    <script type="text/javascript">

        $(document).ready(function () {

            basicDataAudit();

            caseBackToDisk();

            //每隔1分钟自动调用方法，更新
            window.setInterval(basicDataAudit, 1000 * 60);
            window.setInterval(caseBackToDisk, 1000 * 60);


            var casesNubList=[];
            var dealCasesNubList=[];
            var monthsList=[];
            var approvedMoneyList=[];


            $.ajax({
                url: "statisticalChart",
                type: 'GET',
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == 1) {

                        for(var i=0;i<result.claimCaseVoList.length;i++){
                            monthsList.push(result.claimCaseVoList[i].months);
                            casesNubList.push(result.claimCaseVoList[i].casesNub);
                            dealCasesNubList.push(result.claimCaseVoList[i].dealCasesNub);
                            approvedMoneyList.push(result.claimCaseVoList[i].sumApprovedMoney);
                        }


                        echarts.init(document.getElementById('compensationStatistics')).setOption({

                            legend: {},

                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'cross',
                                    crossStyle: {
                                        color: '#999'
                                    }
                                }
                            },
                            dataset: {
                                source: [
                                    ['product', '申请案件数', '赔付案件数', '赔付金额']
                                ]
                            },
                            xAxis: [
                                {
                                    type: 'category',
                                    data: monthsList,
                                    axisPointer: {
                                        type: 'shadow'
                                    }
                                }
                            ],
                            yAxis: [
                                {
                                    type: 'value',
                                    name: '申请案件数',
                                    axisLabel: {
                                        formatter: '{value} 件'
                                    }
                                },
                                {
                                    type: 'value',
                                    name: '赔付金额',
                                    axisLabel: {
                                        formatter: '{value} 元'
                                    }
                                }
                            ],
                            series: [
                                {
                                    type: 'bar',
                                    itemStyle: {
                                        color: '#4472C4'
                                    },
                                    name: '申请案件数',
                                    data: casesNubList
                                },
                                {
                                    type: 'bar',
                                    itemStyle: {
                                        color: '#ED7D31'
                                    },
                                    name: '赔付案件数',
                                    data: dealCasesNubList
                                },
                                {
                                    type: 'line',
                                    itemStyle: {
                                        color: '#1177DB'
                                    },
                                    name: '赔付金额',
                                    yAxisIndex: 1,
                                    data: approvedMoneyList
                                }
                            ]
                        });
                    } else {
                        alert("统计图加载失败，请稍后重试");
                    }

                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

        });

    </script>
    <style>
        .widget-thumb-subtitle {
            color: #333333 !important;
            font-size: 16px !important;
        }

        .fa-clock-o:before {
            color: #E87E04 !important;
        }

        .widget-thumb-body-stat {
            color: #02A7F0 !important;
            font-size: 28px !important;
        }

        .fa-arrow-circle-o-right {
            font-size: 20px;
            color: #3598DC !important;
        }

        .widget-thumb-body-stat > span {
            color: #333333 !important;
            font-size: 16px !important;
        }

        .widget-thumb .widget-thumb-wrap {
            text-align: center !important;
        }

        .widget-thumb .widget-thumb-body {
            text-align: left !important;
        }

        .widget-thumb .widget-thumb-wrap .widget-thumb-icon {
            width: 40px;
            height: 40px;
            margin-top: 9px;
            padding: 0 !important;
        }

        .widget-thumb .widget-thumb-body .widget-thumb-body-stat {
            font-weight: 500 !important;
        }

        .portlet.light {
            margin-top: 10px !important;
        }

    </style>
</head>
<body style="padding-top:0px;">
<!-- BEGIN PAGE BASE CONTENT -->
<div class="row">
    <div class="col-sm-12">
        <!-- BEGIN EXAMPLE TABLE PORTLET-->
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <div class="page-bar" style="background-color: #ffffff; margin-bottom: 0;">
                    <ul class="page-breadcrumb">

                        <li><a class="active">首页</a></li>
                    </ul>
                </div>
            </div>

            <div class="portlet-body">
                <div class="row">
                    <form id="inputForm" method="post" action="${ctx}/homePageController/homePage">
                        <input type="hidden" name="category" id="category" value="${category}"/>
                    </form>
                    <div class="row widget-row" style="margin-left: 25px;">
                        <div class="col-sm-12 col-md-3">
                            <!-- BEGIN WIDGET THUMB -->
                            <div class="widget-thumb widget-bg-color-white text-uppercase margin-bottom-20 bordered">
                                <div class="widget-thumb-wrap">
                                    <img class="widget-thumb-icon" src="${ctx}/images/index/1.png">
                                    <div class="widget-thumb-body">
                                        <div class="widget-thumb-subtitle">·数据采集任务数</div>
                                        <div class="widget-thumb-body-stat">
                                            <span id="dataCollection">--</span>
                                            <span>&nbsp;个</span>
                                            <a href="${ctx}/claimCaseController/claimCaseCollectView">
                                                <i class="fa fa-arrow-circle-o-right" style="float: right;"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- END WIDGET THUMB -->
                        </div>
                        <div class="col-sm-12 col-md-3">
                            <!-- BEGIN WIDGET THUMB -->
                            <div class="widget-thumb widget-bg-color-white text-uppercase margin-bottom-20 bordered">
                                <div class="widget-thumb-wrap">
                                    <img class="widget-thumb-icon" src="${ctx}/images/index/2.png">
                                    <div class="widget-thumb-body">
                                        <span class="widget-thumb-subtitle">·理赔审核任务数</span>
                                        <span class="widget-thumb-body-stat">
                                             <span id="claimCheck">--</span>
                                            <span>&nbsp;个</span>
                                            <a href="${ctx}/claimCaseController/claimCaseCheckList">
                                        <i class="fa fa-arrow-circle-o-right" style="float: right;"></i>
                                                </a>
                                        </span>
                                    </div>

                                </div>
                            </div>
                            <!-- END WIDGET THUMB -->
                        </div>
                        <div class="col-sm-12 col-md-3">
                            <!-- BEGIN WIDGET THUMB -->
                            <div class="widget-thumb widget-bg-color-white text-uppercase margin-bottom-20 bordered">
                                <div class="widget-thumb-wrap">
                                    <img class="widget-thumb-icon" src="${ctx}/images/index/3.png">
                                    <div class="widget-thumb-body">
                                        <span class="widget-thumb-subtitle">·意见反馈任务数</span>
                                        <span class="widget-thumb-body-stat">
                                            <span id="feedBack">--</span>
                                            <span>&nbsp;个</span>
                                            <a href="${ctx}/feedbackController/feedbackList">
                                        <i class="fa fa-arrow-circle-o-right" style="float: right;"></i>
                                                </a>
                                        </span>

                                    </div>
                                </div>
                            </div>
                            <!-- END WIDGET THUMB -->
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-sm-12">
                        <div class="row widget-row">
                            <div class="col-sm-12 col-md-4">
                                <!-- BEGIN WIDGET THUMB -->
                                <div class="widget-thumb widget-bg-color-white text-uppercase margin-bottom-20 bordered">
                                    <div class="widget-thumb-wrap">
                                        <div class="widget-thumb-body">
                                            <div class="widget-thumb-body-stat" style="color: #70b603 !important;">
                                                <span>总申请案件数：</span>
                                                <span id="applicationCase">--</span>
                                                <span>单</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- END WIDGET THUMB -->
                            </div>
                            <div class="col-sm-12 col-md-4">
                                <!-- BEGIN WIDGET THUMB -->
                                <div class="widget-thumb widget-bg-color-white text-uppercase margin-bottom-20 bordered">
                                    <div class="widget-thumb-wrap">
                                        <div class="widget-thumb-body">
                                            <span class="widget-thumb-body-stat" style="color: #8080ff !important;">
                                                <span>总赔付案件数：</span>
                                                <span id="compensationCases">--</span>
                                                <span>单</span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <!-- END WIDGET THUMB -->
                            </div>
                            <div class="col-sm-12 col-md-4">
                                <!-- BEGIN WIDGET THUMB -->
                                <div class="widget-thumb widget-bg-color-white text-uppercase margin-bottom-20 bordered">
                                    <div class="widget-thumb-wrap">
                                        <div class="widget-thumb-body">
                                            <span class="widget-thumb-body-stat" style="color: #f59a23 !important;">
                                                <span>总赔付金额：</span>
                                                <span id="compensationAmount">--</span>
                                                <span>元</span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <!-- END WIDGET THUMB -->
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-12 form-group" style="font-size: 18px;">
                        <i class="fa fa-bookmark"></i>
                        <span>赔付统计</span>
                        <div class="portlet light bordered">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div id="compensationStatistics" style="height:400px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>