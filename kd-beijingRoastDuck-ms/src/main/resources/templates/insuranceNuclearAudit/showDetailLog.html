<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>备注意见</title>

    <script src="${ctx}/metronic/global/plugins/jquery.min.js" type="text/javascript"></script>
    <link href="${ctx}/metronic/global/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/metronic/global/plugins/bootstrap-switch/css/bootstrap-switch.min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/metronic/global/plugins/simple-line-icons/simple-line-icons.min.css" rel="stylesheet" type="text/css" />
    <style>
        html, body {
            overflow: hidden !important;
            margin: 0;
            padding: 20px;
        }

        .portlet .portlet-body {
            padding: 0px;
        }

        /* 确保按钮区域可见 */
        .portlet .portlet-body {
            min-height: 50px;
        }

        /* 固定高度并显示滚动条 */
        .table-responsive {
            max-height: calc(6 * 40px + 41px); /* 6行数据+表头高度 */
            overflow-y: auto;
            margin-bottom: 15px;
        }

        /* 固定表头 */
        thead th {
            position: sticky;
            top: 0;
            background: #f8f9fa;
            z-index: 10;
        }

    </style>

</head>


<body>
    <!-- 备注意见 -->
    <div class="row">
        <div class="col-md-12">
            <div class="portlet light bordered">
                <div class="portlet-body">
                    <#if remarkList?? && (remarkList?size > 0)>
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th width="15%" class="text-center">操作方式</th>
                                    <th width="15%" class="text-center">金额</th>
                                    <th width="15%" class="text-center">操作人</th>
                                    <th width="20%" class="text-center">操作时间</th>
                                    <th width="35%" class="text-center">处理意见</th>
                                </tr>
                            </thead>
                            <tbody>
                                <#list remarkList as remark>
                                <tr>
                                    <td class="text-center">${remark.operatingMode!''}</td>
                                    <td class="text-center">
                                        <#if remark.money??>
                                            ${remark.money}
                                        <#else>
                                            -
                                        </#if>
                                    </td>
                                    <td class="text-center">${remark.operator!''}</td>
                                    <td class="text-center">
                                        <#if remark.operatorTime??>
                                            ${remark.operatorTime}
                                        <#else>
                                            -
                                        </#if>
                                    </td>
                                    <td class="text-left">${remark.dealOpinion!''}</td>
                                </tr>
                                </#list>
                            </tbody>
                        </table>
                    </div>
                    <#else>
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i> 暂无备注意见数据
                    </div>
                    </#if>
                </div>
            </div>
        </div>
    </div>
</body>
</html>