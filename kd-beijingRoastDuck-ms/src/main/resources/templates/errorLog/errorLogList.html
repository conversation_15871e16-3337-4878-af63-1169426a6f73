<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>

    <script src="${ctx}/metronic/global/scripts/app.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/layout.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/demo.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/jquery-multi-select/js/jquery.multi-select.js"
            type="text/javascript"></script>
    <script type="text/javascript">
        const loader =  new Loaders({style:"rectangle"});
        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        $(document).ready(function () {

            // 业务标签 select2初始化
            $("#type").select2({
                placeholder: "请选择",
                width: null
            });
        });

        
        function statusSwitch(status) {
            $("#status").val(status);
            $("#searchForm").submit();
        }

        function startAudit(id) {
            window.location.href = "${ctx}/errorLogController/errorLogDetail?errorLogId="+id;
        }

        function openClick() {
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '批量完成',
                area: [openWindowWidth,'600px'],
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/errorLogController/openClick",
                btn: ['确定', '取消'], //按钮
                maxmin: true,
                btn1: function (index, layero) {
                    //当点击‘确定'按钮的时候，获取弹出层返回的值
                    var res = window["layui-layer-iframe" + index].callbackdata();

                    if (res.textareaData != "") {
                        $.ajax({
                            url: "${ctx}/errorLogController/updateErrorLogListStatus?errorLogListStr=" + res.textareaData,
                            type: 'POST',
                            async: true,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 2000
                                    },function () {
                                        window.location.reload();
                                    });
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 2,
                                        time: 2000
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });

                        //最后关闭弹出层
                        layer.close(index);
                    } else {
                        // alert("跳过原因不能为空");
                        layer.msg('至少要选择一个已完成的任务')
                    }

                }, btn2: function (index, layero) {
                    layer.close(index);
                }
            });
        }

        function startAudit2(id) {
            $.ajax({
                url: "${ctx}/errorLogController/updateErrorLogStatus?errorLogId=" + id,
                type: 'POST',
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layer.msg(result.msg, {
                            icon: 1,
                            time: 2000
                        },function () {
                            window.location.reload();
                        });
                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

    </script>
    <style>

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
        }
        .form-active > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
        }
        td > a {
            display: inline-block;
            margin: 3px;
        }
    </style>
</head>
<body>

<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>新职业平台</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">异常处理</span></li>
                </ul>
            </div>
            <div class="portlet-body">

                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal" action="${ctx}/errorLogController/errorLogList"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">是否已处理：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                       <select class="form-control" name="status" id="status" value="">
                                           <option value="">请选择</option>
                                           <option value="0" <#if errorLog.status==0> selected </#if>>待处理</option>
                                           <option value="1" <#if errorLog.status==1> selected </#if>>已完成</option>
                                           <option value="-1" <#if errorLog.status==-1> selected </#if>>已撤销</option>
                                       </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">业务描述：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="businessName" id="businessName"
                                               value="${errorLog.businessName}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group pull-right" style="margin-bottom: 10px;">
                                    <button id="query" type="button" class="btn green" style="margin-bottom: 10px;margin-right: 20px" onclick="openClick()">批量完成</button>
                                    <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;">查询</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th width="15%">业务描述</th>
                        <th width="15%">接口名称</th>
                        <th width="14%">请求类型</th>
                        <th width="14%">描述</th>
                        <th width="14%">状态</th>
                        <th width="14%" >创建时间</th>
                        <th width="14%">功能</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as vo>
                        <tr>
                            <td title="">${vo.businessName}</td>
                            <td title="">${vo.methodName}</td>
                            <td>
                                <#if vo.reqType == 1>
                                    内部请求
                                <#else >
                                    外部请求
                                </#if>
                            </td>
                            <td title="">${vo.description}</td>
                            <td>
                                <#if vo.status == -1>
                                    已撤销
                                <#elseif vo.status == 0>
                                    未处理
                                <#else >
                                    已处理
                                </#if>
                            </td>

                            <td>${vo.createTime?string('yyyy-MM-dd HH:mm:ss')}</td>

                            <td>
                                <a href="#" onclick="startAudit('${vo.id}')">查看</a>
                                <#if vo.status == 0>
                                    <a href="#" onclick="startAudit2('${vo.id}')">已完成</a>
                                </#if>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>