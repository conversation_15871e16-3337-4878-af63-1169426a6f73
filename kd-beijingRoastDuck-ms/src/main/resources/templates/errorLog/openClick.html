<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <#--widget-->


    <script type="text/javascript">
        function iframeH() {
            var ifm = parent.document.getElementById("contentFrame");
            if (ifm) {
                var ifmH = $(ifm).height()
                var h = $(document.body).height() + 120;
                if (ifmH < h) {
                    parent.document.getElementById("contentFrame").height = h;
                }
            }
        }

        function callbackdata() {
            var ids=new Array();
            $("input:checkbox:checked").each(function(){
                ids.push($(this).attr("id"));
            });

            var data = {
                textareaData: ids
            };
            return data;
        }


        $(document).ready(function () {
        });


    </script>
    <style>

        .block-head-label a {
            font-size: 15px;
            margin-left: 10px;
        }

        .block-head-label span {
            font-size: 15px;
        }

        .attachInfo label {
            margin-top: 20px;
        }

        .attachInfo img {
            width: 100%;
            height: 100%;
            border: 1px solid;
        }

        .logInfo button {
            margin-top: 20px;
            margin-right: 30px;
        }

        .logListInfo {
            margin-bottom: 2%;
        }

        .logListInfo table {
            border: 1px solid #C2C2C2;
        }

        .logListInfo .detailsInfo > td {
            word-wrap: break-word;
            word-break: break-all;
        }

        .logListInfo .rowInfo:hover {
            cursor: pointer;
        }

    </style>
</head>

<body>

<div class="row" >
    <div class="col-sm-12">
        <div class="row logListInfo" style="padding: 5% 10%">
            <div class="col-sm-12">
                <table class="table">
                    <thead>
                    <tr>
                        <td width="30%"></td>
                        <td width="70%">描述</td>
                    </tr>
                    </thead>
                    <tbody>
                    <#if logs??>
                        <#list logs as log>
                            <tr class="rowInfo" >
                                <td width="10%" align="center">
                                    <input style="left: 0px;right: 0px;margin-left: 0px;" type="checkbox" id="${log.id}">
                                </td>
                                <td width="20%">${log.description}</td>

                            </tr>
                        </#list>
                    </#if>

                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
</div>
</body>
</html>