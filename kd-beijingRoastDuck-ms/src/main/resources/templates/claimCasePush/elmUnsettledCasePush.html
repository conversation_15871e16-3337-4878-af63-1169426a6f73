<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>推送饿了么</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">

    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script>
        $(function(){
            var ratioList = [
                {id:'0.80',text:'0.80'},
                {id:'0.85',text:'0.85'},
                {id:'0.90',text:'0.90'},
                {id:'0.95',text:'0.95'},
                {id:'1.00',text:'1.00'},
                {id:'1.05',text:'1.05'},
                {id:'1.10',text:'1.10'},
                {id:'1.15',text:'1.15'},
                {id:'1.20',text:'1.20'}
            ]
            $("#ratio").select2({
                placeholder: "请选择",
                width: null,
                data: ratioList
            });

            $("#unsettledContainer").on("change", "input[name='isFinish']", function() {
                var isFinish = $("input[name='isFinish']:checked").val();
                if (isFinish == 1) {
                    $(".ratio-div").hide();
                } else {
                    $(".ratio-div").show();
                }
            });
        });

        // 单案件推送未决
        function elmPushUnsettledCase() {
            var jsonData = {
                "claimCaseId": "${claimCase.id}",
                "systemAmount": "${systemAmount}"
            };
            var isFinish = $("input[name='isFinish']:checked").val();
            if (!isFinish) {
                layer.msg("请选择推送状态", {icon: 2, time: 2000, shade: [0.1, '#000']});
                return;
            }
            jsonData["isFinish"] = isFinish;
            // 非关闭 才有推送系数
            if (isFinish != 1) {
                var ratio = $("#unsettledContainer").find("#ratio").val();
                if (!ratio) {
                    layer.msg("请选择推送系数", {icon: 2, time: 2000, shade: [0.1, '#000']});
                    return;
                }
                jsonData["ratio"] = ratio;
            }
            jsonData["isConfirm"] = "0";
            console.log(JSON.stringify(jsonData));
            $.ajax({
                url: "${ctx}/claimCaseTripartiteDiscountController/pushElmCase",
                data: JSON.stringify(jsonData),
                type: 'POST',
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "1001") {
                        layer.confirm("请确认该批次推送金额是否存在超保额情况，<br/>" + result.msg + "，<br/>确认完毕请点击推送？", {
                            icon: 3,
                            title: '温馨提示'
                        }, function (index) {
                            jsonData["isConfirm"] = "1";
                            $.ajax({
                                url: "${ctx}/claimCaseTripartiteDiscountController/pushElmCase",
                                type: 'POST',
                                data: JSON.stringify(jsonData),
                                async: false,
                                cache: false,
                                contentType: false,
                                processData: false,
                                success: function (data) {
                                    var result = eval("(" + data + ")");
                                    if (result.ret == "0") {
                                        layer.msg(result.msg, {
                                            icon: 1,
                                            time: 2000,
                                            shade: [0.1, '#000']
                                        }, function (index) {
                                            window.parent.location.reload();
                                            layer.close(index);
                                        });
                                    } else {
                                        layer.msg(result.msg, {icon: 2, time: 2000, shade: [0.1, '#000']}, function (index) {
                                            layer.close(index);
                                        });
                                    }
                                },
                                error: function (data) {
                                    var result = eval("(" + data + ")");
                                    alert(result.msg);
                                    layer.closeAll();
                                }
                            });
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 2000, shade: [0.1, '#000']}, function (index) {
                            layer.close(index);
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
    }

</script>
    <style>
        .row-other-class {
            margin: 20px;
            display: flex;
            align-items: center;
        }

        input[type=radio] {
            position: relative !important;
            margin-left: 0px !important;
        }
    </style>
</head>
<body>
    <#--未决推送-->
    <div id="unsettledContainer" style="width:100%; height:100%; padding: 10px 3%; background-color: white;">
        <div class="row row-other-class" >
            <div class="col-sm-3 text-right">案件号：</div>
            <div class="col-sm-8">
                <input type="text" class="form-control" name="claimCaseNo" id="claimCaseNo"
                       value="${claimCase.claimCaseNo}" disabled/>
            </div>
        </div>
        <div class="row row-other-class" >
            <div class="col-sm-3 text-right">系统未决金额：</div>
            <div class="col-sm-8">
                <input type="text" class="form-control" name="systemAmount" id="systemAmount"
                       value="${systemAmount}" disabled/>
            </div>
        </div>
        <div class="row row-other-class" >
            <div class="col-sm-3 text-right">推送状态：</div>
            <div class="col-sm-8">
                <input type="radio" value="1" id="yes" name="isFinish" <#if !claimCase.status?ends_with("-1")>disabled</#if>> <label for="yes">关闭</label>
                <input type="radio" value="0" id="not" name="isFinish" <#if !claimCase.status?ends_with("-1")>checked disabled</#if>> <label for="not">未决</label>
            </div>
        </div>
        <div class="row row-other-class ratio-div" >
            <div class="col-sm-3 text-right">推送系数：</div>
            <div class="col-sm-8">
                <select class="form-control" id="ratio" name="ratio">
                    <option value="">请选择</option>
                </select>
            </div>
        </div>
        <div class="row row-other-class" style="margin: 20px;display: flex;align-items: center">
            <div class="col-sm-3 text-right">已推未决金额：</div>
            <div class="col-sm-8">
                <input type="text" class="form-control" name="pushedAmount" id="pushedAmount"
                       value="${pushedAmount}" disabled/>
            </div>
        </div>
        <div class="row text-center" style="margin: 20px;display: flex;justify-content: space-evenly;">
            <button class="btn btn-primary margin-right-10"
                    onclick="elmPushUnsettledCase()">推送
            </button>
            <button class="btn btn-warning"
                    onclick="parent.layer.closeAll();">取消
            </button>
        </div>
    </div>
</body>
</html>