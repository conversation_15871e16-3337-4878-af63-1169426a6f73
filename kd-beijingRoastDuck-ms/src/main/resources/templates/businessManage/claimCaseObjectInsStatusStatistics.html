<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">




    </script>
    <style>
        td:hover {
            background-color: #eef1f5;
        }
    </style>
</head>
<body>
<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>新职业平台</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">待处理复核任务</span></li>
                </ul>
            </div>
            <div class="portlet-body">
                <table class="table table-striped table-bordered table-header-fixed">
                    <thead>
                    <tr>
                        <th></th>
                        <#list insStatusMap.keySet() as insStatusKey>
                            <th title="${insStatusKey}">${insStatusMap.get(insStatusKey)}</th>
                        </#list>
                    </tr>
                    </thead>
                    <tbody>
                    <#list insCodeMap.keySet() as insCodeKey>
                        <tr>
                            <td>${insCodeMap.get(insCodeKey)}</td>
                            <#list insStatusMap.keySet() as insStatusKey>
                                <#if resultMap?exists && resultMap.get(insStatusKey)?exists>
                                    <td>${resultMap.get(insStatusKey).get(insCodeKey)!'0'}</td>
                                <#else>
                                    <td>0</td>
                                </#if>
                            </#list>
                        </tr>
                    </#list>
                    <#if isHaveChannel?? && isHaveChannel == 'true'>
                        <tr>
                            <td>其他</td>
                            <#list insStatusMap.keySet() as insStatusKey>
                                <#if resultMap?exists && resultMap.get(insStatusKey)?exists>
                                    <td>${resultMap.get(insStatusKey).get("其他")!'0'}</td>
                                <#else>
                                    <td>0</td>
                                </#if>
                            </#list>
                        </tr>
                    </#if>
                    <tr>
                        <td>总计</td>
                        <#list insStatusMap.keySet() as insStatusKey>
                            <#if resultMap?exists && resultMap.get(insStatusKey)?exists>
                                <td>${resultMap.get(insStatusKey).get("总计")!'0'}</td>
                            <#else>
                                <td>0</td>
                            </#if>
                        </#list>
                    </tr>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
</body>
</html>