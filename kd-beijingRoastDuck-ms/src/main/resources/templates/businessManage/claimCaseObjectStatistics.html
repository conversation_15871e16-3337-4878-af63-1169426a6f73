<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">

        $(document).ready(function () {

            var createInput = function (name, value, type = "hidden") {
                var inputElement = document.createElement("input");
                inputElement.type = type;
                inputElement.name = name;
                if (value != null) {
                    inputElement.value = value;
                }
                return inputElement;
            }

            $("table").on("click", "td:not(:first-of-type)", function(){

                var auditer = $(this).attr("data-auditer");
                var statisticalType = $(this).attr("data-statistical-type");

                if (!statisticalType) {
                    layer.msg("缺少必要参数！",{icon: 2,time: 1500});
                    return;
                }

                var formElement = document.createElement("form");
                formElement.action = "${ctx}/claimCaseObjectController/claimCaseObjectStatisticsList";
                formElement.method = "post";
                formElement.style.display = "none";
                if (auditer) {
                    formElement.appendChild(createInput("auditer", auditer));
                }
                formElement.appendChild(createInput("statisticalType", statisticalType));
                document.body.appendChild(formElement);
                formElement.submit();
                formElement.remove();
            });


        });


    </script>
    <style>
        td:hover {
            background-color: #eef1f5;
        }
    </style>
</head>
<body>
<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>新职业平台</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">个人待处理</span></li>
                </ul>
            </div>
            <div class="portlet-body">
                <table class="table table-striped table-bordered table-header-fixed">
                    <thead>
                    <tr>
                        <th></th>
                        <th>处理中任务数</th>
                        <th>完成任务数</th>
                        <th>关闭任务数</th>
                        <th>当日接收数</th>
                        <th>当日完成数</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#if statisticsMap?? && (statisticsMap.keySet()?size > 0) >
                        <#list statisticsMap.keySet() as key>
                            <#assign countMap = statisticsMap.get(key) >
                            <tr>
                                <td title="${managerMap.get(key)}">${managerMap.get(key)}</td>
                                <td title="${countMap.get("processCount")}" data-auditer="${key}" data-statistical-type="1" >${countMap.get("processCount")}</td>
                                <td title="${countMap.get("finishCount")}" data-auditer="${key}" data-statistical-type="2" >${countMap.get("finishCount")}</td>
                                <td title="${countMap.get("closeCount")}" data-auditer="${key}" data-statistical-type="3" >${countMap.get("closeCount")}</td>
                                <td title="${countMap.get("todayReceiveCount")}" data-auditer="${key}" data-statistical-type="4" >${countMap.get("todayReceiveCount")}</td>
                                <td title="${countMap.get("todayFinishCount")}" data-auditer="${key}" data-statistical-type="5" >${countMap.get("todayFinishCount")}</td>
                            </tr>
                        </#list>
                        <tr>
                            <td title="">合计</td>
                            <td title="${totalMap.get("processCountTotal")}" data-statistical-type="1" >${totalMap.get("processCountTotal")}</td>
                            <td title="${totalMap.get("finishCountTotal")}" data-statistical-type="2" >${totalMap.get("finishCountTotal")}</td>
                            <td title="${totalMap.get("closeCountTotal")}" data-statistical-type="3" >${totalMap.get("closeCountTotal")}</td>
                            <td title="${totalMap.get("todayReceiveCountTotal")}" data-statistical-type="4" >${totalMap.get("todayReceiveCountTotal")}</td>
                            <td title="${totalMap.get("todayFinishCountTotal")}" data-statistical-type="5" >${totalMap.get("todayFinishCountTotal")}</td>
                        </tr>
                    </#if>

                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
</body>
</html>