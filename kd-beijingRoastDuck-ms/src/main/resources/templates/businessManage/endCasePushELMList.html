<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        const loader =  new Loaders({style:"rectangle"});
        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        $(document).ready(function () {

            // 业务标签 select2初始化
            $("#type").select2({
                placeholder: "请选择",
                width: null
            });
        });

        
        function statusSwitch(status) {
            $("#status").val(status);
            $("#searchForm").submit();
        }

        function casePushELM(id) {

            var openWindowWidth = $(document).width() * 0.5 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 360 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '支付时间',
                area: openWindowWidth,
                offset: offsetH,
                shadeClose: false,
                fix: false, //不固定
                btn: ['确定', '取消'], //按钮
                maxmin: true,
                btn1: function (index, layero) {
                    //当点击‘确定'按钮的时候，获取弹出层返回的值
                    var res = window["layui-layer-iframe" + index].callbackdata();
                    if (res.paytime.trim() && res.payAmount.trim()) {
                        //打印返回的值，看是否有我们想返回的值。
                        var formData = new FormData();
                        formData.append("id", id);
                        formData.append("payTimeStr", res.paytime);
                        formData.append("payAmountStr", res.payAmount);
                        $.ajax({
                            url: "${ctx}/claimCaseController/casePushELM",
                            type: 'POST',
                            data: formData,
                            async: true,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 2000
                                    },function () {
                                        window.location.reload();
                                    });
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 2,
                                        time: 2500 //1秒关闭（如果不配置，默认是3秒）
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });
                        //最后关闭弹出层
                        layer.close(index);
                    } else {
                        alert("支付时间与支付金额不能为空");
                    }

                },
                content: '${ctx}/claimCaseController/payTime'
            });
        }


        //查看日志
        function seeCaseProcessReason(claimCaseId) {
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '查看日志',
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/getCaseProcessReason?claimCaseId=" + claimCaseId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        // 整案重推
        function claimDataPushUploadImg(claimCaseId) {
            $.ajax({
                url: "${ctx}/claimCaseController/claimDataPushUploadImg?claimCaseId=" + claimCaseId,
                type: 'POST',
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layer.msg(result.msg, {icon: 1, time: 1500});
                    }else {
                        layer.msg(result.msg, {icon: 2, time: 2000});
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

        }

        // 影像推送
        function uploadImg(claimCaseId) {
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.prompt({
                formType: 0,
                title: '请输入保司报案号',
                fixed: false,
                offset: offsetH,
                area: 'auto',
                closeBtn: 0,
                yes: function (index, elem) {
                    var value = $('#layui-layer'+index + " .layui-layer-input").val();
                    if (!value) {
                        layer.msg("保司报案号不能为空", {
                            icon: 2,
                            time: 2000
                        });
                        return;
                    }
                    var formData = new FormData();
                    formData.append("claimCaseId", claimCaseId);
                    formData.append("channelCaseNo", value);
                    $.ajax({
                        url: "${ctx}/claimCaseController/uploadImg",
                        type: 'POST',
                        data: formData,
                        async: true,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                layer.msg(result.msg, {
                                    icon: 1,
                                    time: 2000,
                                });
                            } else {
                                layer.msg(result.msg, {
                                    icon: 2,
                                    time: 2000
                                });
                            }
                        },
                        error: function (data) {
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    });
                }
            });
        }

    </script>
    <style>

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
        }
        .form-active > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
        }
        td > a {
            display: inline-block;
            margin: 3px;
        }
    </style>
</head>
<body>

<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>新职业平台</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">ELM回盘</span></li>
                </ul>
            </div>
            <div class="portlet-body">

                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal" action="${ctx}/claimCaseController/endCasePushELMList?tagChoose=1"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="claimCaseNo" id="claimCaseNo"
                                               value="${claimCaseVo.claimCaseNo}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group pull-right" style="margin-bottom: 10px;">
                                    <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;">查询</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th width="20%">报案号</th>
                        <th width="20%">赔付金额</th>
                        <th width="20%">状态</th>
                        <th width="20%" >创建时间</th>
                        <th width="20%">功能</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as vo>
                        <tr>
                            <td title="">${vo.claimCaseNo}</td>
                            <td title="">${vo.payAmount!'--'}</td>
                            <td>${vo.status} </td>
                            <td>${vo.createTime?string('yyyy-MM-dd')}</td>
                            <td>
                                <a href="#" onclick="casePushELM('${vo.id}')">结案推送</a>
                                <a href="#" onclick="seeCaseProcessReason('${vo.id}')">查看日志</a>
                                <#if vo.insCode == 'DD' && !(vo.province == '辽宁省' && vo.treatDate?datetime gte "2023-09-29 00:00:00"?datetime) && vo.caseType != "CP">
                                    <@shiro.hasPermission name="CLAIM_DATA_PUSH_UPLOAD_IMG">
                                        <a onclick="claimDataPushUploadImg('${vo.id}')">整案重推</a>
                                        <a onclick="uploadImg('${vo.id}')">重推影像</a>
                                    </@shiro.hasPermission>
                                </#if>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>