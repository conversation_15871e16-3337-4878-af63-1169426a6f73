<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        const loader = new Loaders({style: "rectangle"});
        $(document).ready(function () {
            $("#dateTime").datepicker({
                startView: "year",
                minViewMode: "months",
                autoclose: true
            });

            // 开始日期选择器配置
            $('#startTime').datepicker({
                startView: "year",
                minViewMode: "days",
                autoclose: true,
                format: "yyyy-mm-dd"
            });

            // 结束日期选择器配置
            $('#endTime').datepicker({
                startView: "year",
                minViewMode: "days",
                autoclose: true,
                format: "yyyy-mm-dd"
            });
        });

        function downCasePayHX() {
            window.location.href = "${ctx}/claimCaseController/downCasePayHX";
        }

    </script>
    <style>
        td:hover {
            background-color: #eef1f5;
        }
    </style>
</head>
<body>
<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>新职业平台</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">海峡回推支付成功导出</span></li>
                </ul>
            </div>
            <div class="portlet-body">
                <!-- BEGIN FORM-->
                <#if error??>
                    <div class="alert alert-danger">
                        <button class="close" data-close="alert"></button>
                        <span>${error}</span>
                    </div>
                </#if>
                <button class="btn btn-bule btn-export" onclick="downCasePayHX()">海峡回推支付成功导出
                </button>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
</body>
</html>