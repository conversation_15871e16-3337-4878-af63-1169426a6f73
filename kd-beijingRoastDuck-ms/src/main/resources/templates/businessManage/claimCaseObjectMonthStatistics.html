<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        const loader = new Loaders({style: "rectangle"});
        $(document).ready(function () {
            $("#dateTime").datepicker({
                startView: "year",
                minViewMode: "months",
                autoclose: true
            });

            // 开始日期选择器配置
            $('#startTime').datepicker({
                startView: "year",
                minViewMode: "days",
                autoclose: true,
                format: "yyyy-mm-dd"
            });

            // 结束日期选择器配置
            $('#endTime').datepicker({
                startView: "year",
                minViewMode: "days",
                autoclose: true,
                format: "yyyy-mm-dd"
            });
        });



    </script>
    <style>
        td:hover {
            background-color: #eef1f5;
        }
    </style>
</head>
<body>
<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>新职业平台</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">个人已完成</span></li>
                </ul>
            </div>
            <div class="portlet-body">
                <!-- BEGIN FORM-->
                <#if error??>
                    <div class="alert alert-danger">
                        <button class="close" data-close="alert"></button>
                        <span>${error}</span>
                    </div>
                </#if>
                <form id="searchForm" class="form-horizontal"
                      action="${ctx}/claimCaseObjectController/claimCaseObjectMonthStatistics"
                      method="post">
                    <!--                <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>-->
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-4">月份：</label>
                                    <div class="col-sm-8">
                                        <div class="input-group date"
                                             data-date-format="yyyy-mm" id="dateTime">
                                            <input type="text" class="form-control" name="createTime" id="createTime" autocomplete="off"
                                                   value="${claimCaseObjectVo.createTime}">
                                            <span class="input-group-btn">
                                                <button class="btn default" type="button">
                                                    <i class="fa fa-calendar"></i>
                                                </button>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-4">姓名：</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" name="checkName" id="checkName"
                                               value="${claimCaseObjectVo.checkName}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-4">日期范围：</label>
                                    <div class="col-sm-8">
                                        <div class="input-group input-daterange">
                                            <input type="text" class="form-control" name="startTime" id="startTime" autocomplete="off"
                                                   value="${claimCaseObjectVo.startTime}" placeholder="开始日期">
                                            <span class="input-group-addon"> - </span>
                                            <input type="text" class="form-control" name="endTime" id="endTime" autocomplete="off"
                                                   value="${claimCaseObjectVo.endTime}" placeholder="结束日期">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group pull-right" style="margin-bottom: 10px;">
                                    <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;">查询</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>

                <table class="table table-striped table-bordered table-header-fixed">
                    <thead>
                    <tr>
                        <th></th>
                        <th>当月进件量</th>
                        <th>当月所有关闭数</th>
                        <th>理算通过数量</th>
                        <th>当月进案结案数</th>
                        <th>平台开通至今当月结案量</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#if statisticsMap?? && (statisticsMap.keySet()?size > 0) >
                        <#list statisticsMap.keySet() as key>
                            <#assign countMap = statisticsMap.get(key) >
                            <#if managerMap.get(key)??>
                                <tr>
                                    <td title="${managerMap.get(key)}">${managerMap.get(key)}</td>
                                    <td data-auditer="${key}" data-statistical-type="1" >${countMap.get("monthCount")}</td>
                                    <td data-auditer="${key}" data-statistical-type="2" >${countMap.get("closeCountFromStart")}</td>
                                    <td data-auditer="${key}" data-statistical-type="3" >${countMap.get("passCount")}</td>
                                    <td data-auditer="${key}" data-statistical-type="4" >${countMap.get("finishCount")}</td>
                                    <td data-auditer="${key}" data-statistical-type="4" >${countMap.get("finishCountFromStart")}</td>
                                </tr>
                            </#if>
                        </#list>
                    </#if>

                    </tbody>
                </table>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
</body>
</html>