<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        const loader =  new Loaders({style:"rectangle"});

        var tabStatus="${tabStatus}";

        console.log("tabStatus"+tabStatus);

        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        $(document).ready(function () {

            // 出险类型
            $("#applyType").select2({
                placeholder: "请选择",
                width: null
            });

            // 业务标签
            var labelList = [];
            <#if labelShowMap?? && (labelShowMap.keySet()?size>0)>
                <#list labelShowMap.keySet() as key>
                    labelList.push({id:'${key}',text:'${labelShowMap.get(key).msg}'})
                </#list>
            </#if>
            $("#label").select2({
                placeholder: "请选择",
                width: null,
                data: labelList
            });
            var nowLabel =[];
            <#if claimCaseVo.label?exists>
                <#list claimCaseVo.label?split(",") as code>
                    nowLabel.push("${code}");
                </#list>
                $("#label").val(nowLabel).trigger('change');
            </#if>

            $("body").on("change", "select[name='type-category']", function() {
                var value = $(this).val();
                $("input[name='type']").val(value.split("-")[0]);
                $("input[name='category']").val(value.split("-")[1]);
            });


        });

        
        function statusSwitch(status) {
            $("#status").val(status);
            $("#searchForm").submit();
        }

        function startTask(id) {
            window.location.href = "${ctx}/claimCaseObjectController/startTask?comeFrom=${claimCaseObjectVo.comeFrom}&id="+id;
        }
        function showObject(id) {
            window.location.href = "${ctx}/claimCaseObjectController/showObject?id="+id;
        }
        function showCase(claimCaseId) {
            window.open("${ctx}/claimCaseController/caseDetail?caseId="+claimCaseId);
        }

        // 分配人员 - 赔付对象
        function bindPerson(id) {
            layer.open({
                title: "人员任务分配",
                type: 2,
                area: ['900px','500px'],
                offset: 'auto',
                fix: false, //不固定
                maxmin: true,
                scrollbar: false,
                content: '${ctx}/claimCaseDistributionController/getBindPersonLayer4BS?id='+id+"&isNeedUpd=true",
                success: function (layero, index) {
                    // layer.iframeAuto(index);
                }
            });
        }

        //查看详情
        function caseDetail(claimCaseId) {
            //window.location.href="${ctx}/claimCaseController/casePretreatmentDetail?caseId="+claimCaseId+"&tabStatus=${tabStatus}";
            window.open("${ctx}/claimCaseController/casePretreatmentDetail?caseId="+claimCaseId+"&tabStatus=${tabStatus}");
        }

        // 展示挂起原因
        function showHangUpDescription(hangUpReason, e) {
            layer.tips(hangUpReason,$(e), {shadeClose: true, time: 0, area: 'auto'});
        }
        function closeTips() {
            layer.closeAll();
        }

    </script>
    <style>

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
            border-right: 0;
            overflow: hidden;
            vertical-align: bottom;
            padding: 0px;
            display: inline-block;
        }
        .form-active > div > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
        }
        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 70px 6px 30px;
            font-size: 17px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
            border-right: 1px solid #e7ecf1;
        }
        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }
        td > a {
            display: inline-block;
            margin: 3px;
        }

        .li-blue {
            background: #0b94ea ;
            color: #fff;
        }

        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }
    </style>
</head>
<body>

<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>新职业平台</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">预处理任务</span></li>
                </ul>
            </div>
            <div class="portlet-body">

                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal" action="${ctx}/claimCaseController/casePretreatmentList"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="control-label col-sm-3" style="padding-right: 0">报案号：</label>
                                <div class="col-sm-8" style="padding-left: 0;">
                                    <input type="text" class="form-control" name="claimCaseNo" id="claimCaseNo"
                                           value="${claimCaseVo.claimCaseNo}"
                                           placeholder="请输入"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="control-label col-sm-3" style="padding-right: 0">报案人姓名：</label>
                                <div class="col-sm-8" style="padding-left: 0;">
                                    <input type="text" class="form-control" name="applyName" id="applyName"
                                           value="${claimCaseVo.applyName}"
                                           placeholder="请输入"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="control-label col-sm-3" style="padding-right: 0">出险人姓名：</label>
                                <div class="col-sm-8" style="padding-left: 0;">
                                    <input type="text" class="form-control" name="treatName" id="treatName"
                                           value="${claimCaseVo.treatName}"
                                           placeholder="请输入"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="control-label col-sm-3" style="padding-right: 0">出险人身份证：</label>
                                <div class="col-sm-8" style="padding-left: 0;">
                                    <input type="text" class="form-control" name="treatIdNum" id="treatIdNum"
                                           value="${claimCaseVo.treatIdNum}"
                                           placeholder="请输入"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="control-label col-sm-3" style="padding-right: 0">出险类型：</label>
                                <div class="col-sm-8" style="padding-left: 0;">
                                    <select class="form-control select2-multiple" name="applyType"
                                            id="applyType">
                                        <option value=" " selected>请选择</option>
                                        <#if appyTypeNewMap?exists>
                                            <#list appyTypeNewMap.keySet() as key>
                                                <option value="${key}" <#if claimCaseVo.applyType==key>selected</#if>>${appyTypeNewMap.get(key).msg}</option>
                                            </#list>
                                        </#if>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">标签：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <select class="form-control select2-multiple"  name="label"
                                                id="label" multiple>
                                            <option value="">请选择</option>
                                            <#--<#if labelShowMap?? && (labelShowMap.keySet()?size>0)>
                                                <#list labelShowMap.keySet() as key>
                                                    <option <#if (claimCaseVo.label!'-')==key>selected</#if>value="${key}">
                                                        ${labelShowMap.get(key).msg}
                                                    </option>
                                                </#list>
                                            </#if>-->
                                        </select>
                                    </div>
                                </div>
                            </div>
                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group pull-right" style="margin-bottom: 10px;">
                                    <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;margin-right: 20px;">查询</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-active">
                        <div>
                            <input type="hidden" name="status" id="status" value="${claimCaseVo.status}">
                            <ul>
                                <li class="li-default <#if claimCaseVo.status == 4>li-blue</#if>" onclick="statusSwitch(4)">待处理</li>
                                <li class="li-default <#if claimCaseVo.status == 15>li-blue</#if>"  onclick="statusSwitch(15)">缺材料待补材</li>
                                <li class="li-default <#if claimCaseVo.status == 16>li-blue</#if>"  onclick="statusSwitch(16)">已补材待审核</li>
                                <li class="li-default <#if claimCaseVo.status == 13>li-blue</#if>" onclick="statusSwitch(13)">新职伤</li>
                                <li class="li-default <#if claimCaseVo.status == 14>li-blue</#if>" onclick="statusSwitch(14)">特殊商</li>
                                <li class="li-default <#if claimCaseVo.status == 12>li-blue</#if>"  onclick="statusSwitch(12)">挂起任务</li>
                            </ul>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th width="10%">报案号</th>
                        <th width="10%">出险人姓名</th>
                        <th width="10%">出险人身份证</th>
                        <#if claimCaseVo.status == 4 || claimCaseVo.status == 15 || claimCaseVo.status == 16>
                        <th width="12%">预处理时间</th>
                        </#if>
                        <#if claimCaseVo.status == 12>
                            <th width="12%">挂起时间</th>
                        </#if>
                        <th width="12%">出险类型</th>
                        <th width="10%">标签</th>
                        <th width="7%">估损金额</th>
                        <th width="13%">功能</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as vo>
                        <tr>
                            <td title="">${vo.claimCaseNo}</td>
                            <td title="">${vo.treatName}</td>
                            <td title="">${vo.treatIdNum}</td>
                            <#if claimCaseVo.status == 4 || claimCaseVo.status == 15 || claimCaseVo.status == 16>
                                <td <#if vo.processingTime??>onmouseover="showHangUpDescription('${vo.processingDescription}', this)" onmouseout="closeTips()"</#if>>${(vo.processingTime?string["yyyy-MM-dd HH:mm:ss"])!''}</td>
                            </#if>
                            <#if claimCaseVo.status == 12>
                                <td <#if vo.modifyTime??>onmouseover="showHangUpDescription('${vo.remark}', this)" onmouseout="closeTips()"</#if>>${(vo.modifyTime?string["yyyy-MM-dd HH:mm:ss"])!''}</td>
                            </#if>
                            <td>
                                <#if vo.applyType??>
                                    <#list vo.applyType?split(",") as name>
                                        <span class="span-type">${name}</span>
                                    </#list>
                                </#if>
                            </td>
                            <td title="" class="labelGroup">
                                <#if vo.label??>
                                    <#list vo.label.split(",") as key>
                                        <#if (key?trim)!="">
                                            <span class="${key} span-type" style="color: ${labelShowMap.get(key).fontColor};background-color: ${labelShowMap.get(key).color}"><#if labelShowMap.get(key)??>${labelShowMap.get(key).msg}<#else>${key}</#if></span>
                                        </#if>
                                    </#list>
                                </#if>
                            </td>
                            <td>${vo.appraisalAmount!'--'}</td>
                            <#--功能-->
                            <td>
                                <a onclick="caseDetail('${vo.id}')">开始处理</a>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>