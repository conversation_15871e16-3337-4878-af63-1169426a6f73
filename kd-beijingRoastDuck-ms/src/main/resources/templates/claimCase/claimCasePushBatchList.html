<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        const loader =  new Loaders({style:"rectangle"});
        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        $(document).ready(function () {

            // 业务标签 select2初始化
            $("#applyType").select2({
                placeholder: "请选择",
                width: null
            });

            // 业务标签 select2初始化
            var labelList = [];
            <#if labelShowMap?? && (labelShowMap.keySet()?size>0)>
            <#list labelShowMap.keySet() as key>
            labelList.push({id:'${key}',text:'${labelShowMap.get(key).msg}'})
            </#list>
            </#if>
            console.log(labelList);
            $("#label").select2({
                placeholder: "请选择",
                width: null,
                data: labelList
            });
            var nowLabel =[];
            <#if claimCaseVo.label?exists>
            <#list claimCaseVo.label?split(",") as code>
            nowLabel.push("${code}");
            </#list>
            console.log(nowLabel);
            $("#label").val(nowLabel).trigger('change');
            </#if>


        });



        //查看详情
        function caseDetail(batchNo) {
            window.location.href="${ctx}/claimCaseController/claimCasePushBatchDetail?batchNo="+batchNo;
        }

        //检查最大值是否大于等于最小值
        function validateMaxCaseNum() {
            var minCaseNum = parseInt(document.getElementById('minCaseNum').value, 10);
            var maxCaseNum = parseInt(document.getElementById('maxCaseNum').value, 10);

            if (!isNaN(minCaseNum) && !isNaN(maxCaseNum) && maxCaseNum < minCaseNum) {
                document.getElementById('maxCaseNum').value = '';
            }
        }
    </script>
    <style>

        .layui-layer-msg {
            z-index: 1989101411 !important;
        }

        /*loader需要的遮罩层css start */
        #screen {
            height: 100%;
            width: 100%;
            z-index: 198910141;
            position: fixed;
            background: rgba(0,0,0,0.3);
            top:0;
        }

        #screen #screenLoading{
            margin: 0 auto;
            top: 40%;
            transform: translateY(-50%);
            background: greenyellow;
        }
        /*loader需要的遮罩层css end*/

        .clear-padding {
            padding: 0px !important;
        }

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
            border-right: 0;
            overflow: hidden;
            vertical-align: bottom;
            padding: 0px;
            display: inline-block;
        }
        .form-active > div > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
            display: flex;
        }
        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 70px 6px 30px;
            font-size: 17px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
            border-right: 1px solid #e7ecf1;
        }
        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }
        .li-blue {
            background: #0b94ea ;
            color: #fff;
        }
        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }
        td > a {
            display: inline-block;
            margin: 3px;
        }

        .td-overflow {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 100px;
        }
        .labelGroup span{
            margin-left: 5px;
            margin-right: 5px;
            background-color: #1676FF;
            color: white;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="row">
        <div class="col-sm-12">
            <div class="portlet light portlet-fit bordered">
                <div class="portlet-title">
                    <ul class="page-breadcrumb breadcrumb">
                        <li><span>RB案件推送</span> <i class="fa fa-circle"></i></li>
                        <li><span class="active">RB批次列表管理</span></li>
                    </ul>
                </div>
                <div class="portlet-body">

                    <!-- BEGIN FORM-->
                    <form id="searchForm" class="form-horizontal" action="${ctx}/claimCaseController/claimCasePushBatchList"
                          method="post">
                        <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                        <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                        <div class="form-body">
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="control-label col-sm-3" style="padding-right: 0">案件推送状态：</label>
                                        <div class="col-sm-8" style="padding-left: 0;">
                                            <select class="form-control" name="caseStatus" id="caseStatus" value="">
                                                <option value="">请选择</option>
                                                <option value="1" <#if claimCasePushBatchVo.caseStatus==1>selected</#if>>抄单</option>
                                                <option value="2" <#if claimCasePushBatchVo.caseStatus==2>selected</#if>>报案</option>
                                                <option value="3" <#if claimCasePushBatchVo.caseStatus==3>selected</#if>>估损</option>
                                                <option value="4" <#if claimCasePushBatchVo.caseStatus==4>selected</#if>>核损</option>
                                                <option value="5" <#if claimCasePushBatchVo.caseStatus==5>selected</#if>>核赔</option>
                                                <option value="6" <#if claimCasePushBatchVo.caseStatus==6>selected</#if>>影像</option>
                                                <option value="7" <#if claimCasePushBatchVo.caseStatus==7>selected</#if>>注销</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="control-label col-sm-3" style="padding-right: 0">操作时间：</label>
                                        <div class="col-sm-8" style="padding-left: 0;">
                                            <div class="input-group date-picker input-daterange"
                                                 data-date-format="yyyy-mm-dd" >
                                                <input type="text" class="form-control" name="finishTimeStart" id="finishTimeStart" autocomplete="off"
                                                       value="${claimCasePushBatchVo.finishTimeStart}">
                                                <span class="input-group-addon"><font style="vertical-align: inherit;"><font
                                                        style="vertical-align: inherit;"> 至 </font></font></span>
                                                <input type="text" class="form-control" name="finishTimeEnd" id="finishTimeEnd" autocomplete="off"
                                                       value="${claimCasePushBatchVo.finishTimeEnd}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="control-label col-sm-3" style="padding-right: 0">批次状态：</label>
                                        <div class="col-sm-8" style="padding-left: 0;">
                                            <select class="form-control" name="status" id="status" value="">
                                                <option value="">请选择</option>
                                                <option value="-2" <#if claimCasePushBatchVo.status==-2>selected</#if>>全部失败</option>
                                                <option value="-1" <#if claimCasePushBatchVo.status==-1>selected</#if>>部分失败</option>
                                                <option value="0" <#if claimCasePushBatchVo.status==0>selected</#if>>推送中</option>
                                                <option value="1" <#if claimCasePushBatchVo.status==1>selected</#if>>成功</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="control-label col-sm-3" style="padding-right: 0">案件数量范围：</label>
                                        <div class="col-sm-8" style="padding-left: 0;">
                                            <div class="input-group layui-input-number-out-of-range">
                                                <input type="text" class="form-control" name="minCaseNum" id="minCaseNum"
                                                       value="${claimCasePushBatchVo.minCaseNum}"
                                                       placeholder="请输入"
                                                       oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                                                       onchange="validateMaxCaseNum()"/>
                                                <span class="input-group-addon"><font style="vertical-align: inherit;"><font
                                                        style="vertical-align: inherit;"> 至 </font></font></span>
                                                <input type="text" class="form-control" name="maxCaseNum" id="maxCaseNum"
                                                       value="${claimCasePushBatchVo.maxCaseNum}"
                                                       placeholder="请输入"
                                                       oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                                                       onchange="validateMaxCaseNum()"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="control-label col-sm-3" style="padding-right: 0">保司标识：</label>
                                        <div class="col-sm-8" style="padding-left: 0;">
                                            <select class="form-control" name="insCode" id="insCode" value="">
                                                <option value="">请选择</option>
                                                <option value="HMRB" <#if claimCasePushBatchVo.insCode == "HMRB">selected</#if>>盒马人保</option>
                                                <option value="RB" <#if claimCasePushBatchVo.insCode == "RB">selected</#if>>人保</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="btn-group pull-right" style="margin-bottom: 10px;">
                                        <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;">查询</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                    <table class="table table-striped table-bordered table-hover table-header-fixed">
                        <thead>
                        <tr>
                            <th width="10%">批次号</th>
                            <th width="7%">保司标识</th>
                            <th width="6%">案件数量</th>
                            <th width="10%">案件推送状态</th>
                            <th width="5%">批次状态</th>
                            <th width="10%">完成推送时间</th>
                            <th width="7%">操作人</th>
                            <th width="10%">操作时间</th>
                            <th width="7%">功能</th>
                        </tr>
                        </thead>
                        <tbody>
                        <#list page.list as vo>
                            <tr>
                                <td title="">${vo.batchNo}</td>
                                <td title="">
                                    <#if vo.insCode??>
                                        <#if vo.insCode == "RB">
                                            <span class="label label-sm label-info">人保</span>
                                        <#elseif vo.insCode == "HMRB">
                                            <span class="label label-sm label-danger">盒马人保</span>
                                        </#if>
                                    </#if>
                                </td>
                                <td title="">${vo.caseNum}</td>
                                <td title="">
                                    <#if vo.caseStatus??>
                                        <#if vo.caseStatus == 1>
                                            <span class="label label-sm label-success">抄单</span>
                                        <#elseif vo.caseStatus == 2>
                                            <span class="label label-sm label-success">报案</span>
                                        <#elseif vo.caseStatus == 3>
                                            <span class="label label-sm label-success">估损</span>
                                        <#elseif vo.caseStatus == 4>
                                            <span class="label label-sm label-success">核损</span>
                                        <#elseif vo.caseStatus == 5>
                                            <span class="label label-sm label-success">核赔</span>
                                        <#elseif vo.caseStatus == 6>
                                            <span class="label label-sm label-success">影像</span>
                                        <#elseif vo.caseStatus == 7>
                                            <span class="label label-sm label-success">注销</span>
                                        </#if>
                                    </#if>
                                </td>
                                <td title="">
                                    <#if vo.status??>
                                        <#if vo.status == -2>
                                            <span class="label label-sm label-danger">全部失败</span>
                                        <#elseif vo.status == -1>
                                            <span class="label label-sm label-warning">部分失败</span>
                                        <#elseif vo.status == 0>
                                            <span class="label label-sm label-success">推送中</span>
                                        <#elseif vo.status == 1>
                                            <span class="label label-sm label-success">成功</span>
                                        </#if>
                                    </#if>
                                </td>
                                <td title="">
                                    <#if vo.finishTime??>
                                        ${vo.finishTime?string["yyyy-MM-dd HH:mm:ss"]}
                                    <#else>
                                        &nbsp;
                                    </#if>
                                </td>
                                <td title="">${vo.creator}</td>
                                <td title="">
                                    <#if vo.createTime??>
                                        ${vo.createTime?string["yyyy-MM-dd HH:mm:ss"]}
                                    <#else>
                                        &nbsp;
                                    </#if>
                                </td>
                                <td>
                                    <a style="padding-left: 10px" onclick="caseDetail('${vo.batchNo}')">查看详情</a>
                                </td>
                            </tr>
                        </#list>
                        </tbody>
                    </table>
                    <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                        <div class="modal-dialog">
                            <img src="${ctx}/images/load.gif">
                        </div>
                    </div>
                </div>
                <!-- END FORM-->
            </div>
        </div>
    </div>
    <@sc.pagination page=page />
</body>
</html>