<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        const loader =  new Loaders({style:"rectangle"});
        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        $(document).ready(function () {

            // 业务标签 select2初始化
            $("#applyType").select2({
                placeholder: "请选择",
                width: null
            });

            // 业务标签 select2初始化
            var labelList = [];
            <#if labelShowMap?? && (labelShowMap.keySet()?size>0)>
            <#list labelShowMap.keySet() as key>
            labelList.push({id:'${key}',text:'${labelShowMap.get(key).msg}'})
            </#list>
            </#if>
            console.log(labelList);
            $("#label").select2({
                placeholder: "请选择",
                width: null,
                data: labelList
            });
            var nowLabel =[];
            <#if claimCaseVo.label?exists>
            <#list claimCaseVo.label?split(",") as code>
            nowLabel.push("${code}");
            </#list>
            console.log(nowLabel);
            $("#label").val(nowLabel).trigger('change');
            </#if>


        });

        function generate() {
            window.location.href = "${ctx}/claimCaseController/claimCaseGenerate?TYPE=new";
        }
        
        function statusSwitch(status) {
            $("#status").val(status);
            if (status==null||status!==4){
                $("#tagChoose").val(1);
            }
            $("#searchForm").submit();
        }

        function startAudit(id) {
            window.location.href = "${ctx}/claimCaseController/startAudit?claimCaseId="+id;
        }

        function closeCase(caseId) {
            var content = '<div class="row" style="padding: 0px 30px" ><div class="col-sm-3">&nbsp;</div><textarea rows="5" class="col-sm-6 form-control" name="closeCaseMsg" id="closeCaseMsg" autocomplete="off" placeholder="请输入关闭案件原因"></textarea></div>' ;
            layer.open({
                type: 1,
                content: content,
                title: '关闭案件',
                area:  ['500px', '300px'],
                btn: ['确认','取消'],
                yes: function(index,obj){
                    console.log(typeof $("#closeCaseMsg").val());
                    var closeCaseMsg = $("#closeCaseMsg").val();
                    if(typeof closeCaseMsg != 'string' || closeCaseMsg.trim()==''){
                        layer.msg("关闭案件原因不能为空", {icon: 2, time: 3000,offset: 'r'});
                    }else {
                        var formData = new FormData();
                        formData.append("claimCaseId", caseId);
                        formData.append("type", 3);
                        formData.append("description", closeCaseMsg);
                        $.ajax({
                            url: "${ctx}/insuranceCaseController/closeCase",
                            type: 'POST',
                            data: formData,
                            async: false,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    loader.show();
                                    setTimeout(function(){
                                        layer.msg('关闭案件成功', {
                                            icon: 1,
                                            time: 1000 //1秒关闭（如果不配置，默认是3秒）
                                        }, function () {
                                            window.location.href = "${ctx}/claimCaseController/claimCaseList";
                                        });
                                    },1200);
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 1500 //1秒关闭（如果不配置，默认是3秒）
                                    }, function (index) {
                                        layer.close(index);
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });
                    }
                }
            });
        }

        //查看详情
        function caseDetail(claimCaseId) {
            window.location.href="${ctx}/claimCaseController/caseDetail?caseId="+claimCaseId;
        }

        //补发短信
        function reSendMessage(claimCaseId) {
            layer.open({
                title: "补发短信",
                type: 1,
                content: $('#reSendMessageContainer'),
                area: ['750px', '300px'],
                fixed: false,
                offset: 't',
                btn: ['确认', '取消'],
                closeBtn: 0,
                yes: function (index, layero) {
                    let check = $('#reSendMessageContainer').find('span.checked');
                    let children = check.children();
                    let code = children.attr("code");
                    if (typeof code == "undefined" || code == null) {
                        layer.msg("请选择短信类型！！！", {
                            icon: 2,
                            time: 2000
                        });
                        return;
                    }
                    let reissueReason = $("#reissueReason").val();
                    if (reissueReason.trim() == "") {
                        layer.msg("请输入补发原因！！！", {
                            icon: 2,
                            time: 2000
                        });
                        return;
                    }
                    var formData = new FormData();
                    formData.append("claimCaseId", claimCaseId);
                    formData.append("messageCode", code);
                    formData.append("reissueReason", reissueReason);
                    $.ajax({
                        url: "${ctx}/claimCaseController/reSendMessage",
                        type: 'POST',
                        data: formData,
                        async: true,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                layer.msg("发送成功", {
                                    icon: 1,
                                    time: 2000
                                },function () {
                                    layer.closeAll();
                                });
                            } else {
                                layer.msg(result.msg, {
                                    icon: 2,
                                    time: 2000
                                });
                            }
                        },
                        error: function (data) {
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    });
                },
                btn2: function (index, layero) {
                    layer.closeAll();
                }
            });
        }

        function dj() {
            $.ajax({
                url: "${ctx}/claimCaseController/test",
                type: 'POST',
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layer.msg(result.msg, {
                            icon: 1,
                            time: 2000
                        },function () {
                            layer.closeAll();
                        });
                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

        function bindPolicyPerson(id) {
            $.ajax({
                url: "${ctx}/claimCaseController/bindPolicyPerson?id=" + id,
                type: 'POST',
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layer.msg(result.msg, {
                            icon: 1,
                            time: 2000
                        },function () {
                            window.location.reload();
                        });
                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

    </script>
    <style>

        .layui-layer-msg {
            z-index: 1989101411 !important;
        }

        /*loader需要的遮罩层css start */
        #screen {
            height: 100%;
            width: 100%;
            z-index: 198910141;
            position: fixed;
            background: rgba(0,0,0,0.3);
            top:0;
        }

        #screen #screenLoading{
            margin: 0 auto;
            top: 40%;
            transform: translateY(-50%);
            background: greenyellow;
        }
        /*loader需要的遮罩层css end*/

        .clear-padding {
            padding: 0px !important;
        }

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
            border-right: 0;
            overflow: hidden;
            vertical-align: bottom;
            padding: 0px;
            display: inline-block;
        }
        .form-active > div > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
            display: flex;
        }
        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 70px 6px 30px;
            font-size: 17px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
            border-right: 1px solid #e7ecf1;
        }
        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }
        .li-blue {
            background: #0b94ea ;
            color: #fff;
        }
        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }
        td > a {
            display: inline-block;
            margin: 3px;
        }

        .td-overflow {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 100px;
        }
        .labelGroup span{
            margin-left: 5px;
            margin-right: 5px;
            background-color: #1676FF;
            color: white;
            border-radius: 2px;
        }
    </style>
</head>
<body>

<#--<h1 onclick="dj()">点击</h1>-->

<#--补发短信-->
<div id="reSendMessageContainer" style="display: none;width: 100%;height: 100%">
    <div class="row" style="padding-top: 30px;margin: 0px 0px">
        <div class="col-sm-3">
            <span class="pull-right" style="font-size: 10px;color: #7f7f7f">选择短信类型：</span>
        </div>
        <div class="col-sm-9">
            <#if messageType?? && (messageType.keySet()?size>0)>
                <#list messageType.keySet() as key>
                    <div class="col-sm-3 clear-padding" style="margin-bottom: 10px;">
                        <input name="messageType" style="left: 0px;right: 0px;margin-left: 0px;" code="${key}"
                               type="radio">${messageType.get(key)}
                    </div>
                </#list>
            </#if>
            <textarea class="form-control" id="reissueReason" rows="5" placeholder="请输入补发原因"></textarea>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>新职业平台</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">汇嘉报案管理</span></li>
                </ul>
            </div>
            <div class="portlet-body">

                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal" action="${ctx}/claimCaseController/claimCaseListSpecial"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="claimCaseNo" id="claimCaseNo"
                                               value="${claimCaseVo.claimCaseNo}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案人姓名：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="applyName" id="applyName"
                                               value="${claimCaseVo.applyName}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案人手机号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="applyMobile" id="applyMobile"
                                               value="${claimCaseVo.applyMobile}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <#--<div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险类型：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                       <select class="form-control select2-multiple" multiple name="applyType" id="applyType">
                                           <option value="">请选择</option>
                                           <#list applyTypeList as type>
                                               <option value="${type.code}" <#if claimCaseVo.applyType?contains("${type.code}")>selected</#if>>${type.parentTypeName+"-"+type.childTypeName}</option>
                                           </#list>
                                       </select>
                                    </div>
                                </div>
                            </div>-->
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人姓名：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatName" id="treatName"
                                               value="${claimCaseVo.treatName}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人身份证：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatIdNum" id="treatIdNum"
                                               value="${claimCaseVo.treatIdNum}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
<!--                            <div class="col-sm-4">-->
<!--                                <div class="form-group" >-->
<!--&lt;!&ndash;                                    <label class="control-label col-sm-3" style="padding-right: 0">标签：</label>&ndash;&gt;-->
<!--                                    <div class="col-sm-8" style="padding-left: 0;">-->
<!--                                        <select class="form-control select2-multiple"  name="label"-->
<!--                                                id="label" type="hidden" multiple>-->
<!--                                            <option value="ACX017" type="hidden">请选择</option>-->
<!--                                            <#&#45;&#45;<#if labelShowMap?? && (labelShowMap.keySet()?size>0)>-->
<!--                                                <#list labelShowMap.keySet() as key>-->
<!--                                                    <option <#if (claimCaseVo.label!'-')==key>selected</#if>value="${key}">-->
<!--                                                        ${labelShowMap.get(key).msg}-->
<!--                                                    </option>-->
<!--                                                </#list>-->
<!--                                            </#if>&ndash;&gt;-->
<!--                                        </select>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                        </div>
                        <div class="row">
                            <#--<div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">是否立案：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                       <select class="form-control" name="isRegister" id="isRegister" value="">
                                           <option value="">请选择</option>
                                           <option value="1" <#if claimCaseVo.isRegister==1>selected</#if>>是</option>
                                           <option value="0" <#if claimCaseVo.isRegister==0>selected</#if>>否</option>
                                       </select>
                                    </div>
                                </div>
                            </div>-->
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险时间：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <div class="input-group date-picker input-daterange"
                                             data-date-format="yyyy-mm-dd" >
                                            <input type="text" class="form-control" name="treatDateStart" id="treatDateStart" autocomplete="off"
                                                   value="${claimCaseVo.treatDateStart}">
                                            <span class="input-group-addon"><font style="vertical-align: inherit;"><font
                                                            style="vertical-align: inherit;"> 至 </font></font></span>
                                            <input type="text" class="form-control" name="treatDateEnd" id="treatDateEnd" autocomplete="off"
                                                   value="${claimCaseVo.treatDateEnd}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案时间：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <div class="input-group date-picker input-daterange"
                                             data-date-format="yyyy-mm-dd" >
                                            <input type="text" class="form-control" name="startDateStart" id="startDateStart" autocomplete="off"
                                                   value="${claimCaseVo.startDateStart}">
                                            <span class="input-group-addon"><font style="vertical-align: inherit;"><font
                                                            style="vertical-align: inherit;"> 至 </font></font></span>
                                            <input type="text" class="form-control" name="startDateEnd" id="startDateEnd" autocomplete="off"
                                                   value="${claimCaseVo.startDateEnd}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group pull-right" style="margin-bottom: 10px;">
                                    <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;">查询</button>
                                    <button type="button" class="btn btn-info" style="margin-left: 10px;" onclick="startAudit('')">开始审核</button>
                                    <button type="button" class="btn btn-info" style="margin-left: 10px;" onclick="generate()">创建报案</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-active" >
                        <div>
                            <input type="hidden" name="status" id="status" value="${claimCaseVo.status}">
                            <input type="hidden" name="tagChoose" id="tagChoose" value="">
                            <ul>
                                <@shiro.hasPermission name="REPORT_CASE_LIST_ALL">
                                    <li class="li-default <#if claimCaseVo.status?? == false>li-blue</#if>" onclick="statusSwitch()">全部
                                    </li>
                                </@shiro.hasPermission>
                                <@shiro.hasPermission name="REPORT_CASE_LIST_NOT_DEAL">
                                    <li class="li-default  <#if claimCaseVo.status == 4>li-blue</#if>" onclick="statusSwitch(4)">待处理
                                    </li>
                                </@shiro.hasPermission>
                                <@shiro.hasPermission name="REPORT_CASE_LIST_DEAD">
                                    <li class="li-default  <#if claimCaseVo.status == 1>li-blue</#if>" onclick="statusSwitch(1)">死亡
                                    </li>
                                </@shiro.hasPermission>
                                <@shiro.hasPermission name="REPORT_CASE_LIST_DIFFCULT">
                                    <li class="li-default <#if claimCaseVo.status == 2>li-blue</#if>" onclick="statusSwitch(2)">疑难
                                    </li>
                                </@shiro.hasPermission>
                                <@shiro.hasPermission name="REPORT_CASE_LIST_OVER_TIME">
                                    <li class="li-default <#if claimCaseVo.status == 3>li-blue</#if>" onclick="statusSwitch(3)">超时
                                    </li>
                                </@shiro.hasPermission>
                                <@shiro.hasPermission name="REPORT_CASE_LIST_NO_POLICYPERSON">
                                    <li class="li-default <#if claimCaseVo.status == 5>li-blue</#if>"  onclick="statusSwitch(5)"> 无保全
                                    </li>
                                </@shiro.hasPermission>
                            </ul>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th width="10%">报案号</th>
                        <th width="7%">报案人姓名</th>
                        <#--<th width="8%">报案人身份证</th>-->
                        <th width="10%">出险人姓名</th>
                        <th width="10%">出险人身份证</th>
                        <th width="10%">出险类型</th>
                        <th width="14%">标签</th>
                        <th width="7%">报案时间</th>
                        <th width="7%">出险时间</th>
                        <#--<th width="5%">是否立案</th>-->
                        <th width="5%">估损金额</th>
                        <#--<th width="5%">赔款金额</th>
                        <th width="6%" class="td-overflow">案件状态</th>-->
                        <th width="5%">责任人</th>
                        <th width="15%">功能</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as vo>
                        <tr>
                            <td title="">${vo.claimCaseNo}</td>
                            <td title="">${vo.applyName}</td>
                            <#--<td title="">${vo.applyIdNum}</td>-->
                            <td title="">${vo.treatName}</td>
                            <td title="">${vo.treatIdNum}</td>
                            <td>
                                <#if vo.applyType??>
                                    <#list vo.applyType?split(",") as name>
                                        <span class="span-type">${name}</span>
                                    </#list>
                                </#if>
                            </td>
                            <td title="" class="labelGroup">
                                <#if vo.label??>
                                    <#list vo.label.split(",") as key>
                                        <#if (key?trim)!="">
                                            <span class="${key} span-type" style="color: ${labelShowMap.get(key).fontColor};background-color: ${labelShowMap.get(key).color}"><#if labelShowMap.get(key)??>${labelShowMap.get(key).msg}<#else>${key}</#if></span>
                                        </#if>
                                    </#list>
                                </#if>
                            </td>
                            <td>${vo.startDate?string["yyyy-MM-dd"]}</td>
                            <td>${vo.treatDate?string["yyyy-MM-dd"]}</td>
                            <#--<td>
                                <#if vo.status?contains("aax")>
                                    否
                                <#else >
                                    是
                                </#if>
                            </td>-->
                            <td>${vo.appraisalAmount!'--'}</td>
                            <#--<td>${vo.payAmount!'--'}</td>
                            <td class="td-overflow" title="${claimCaseStatusEumMap.get(vo.status).msg}">
                                ${claimCaseStatusEumMap.get(vo.status).msg}
                            </td>-->
                            <#--功能-->
                            <td>
                                <#if vo.modifier??>
                                    <#if vo.modifier?contains("-") && vo.modifier!="-1" >
                                        ${vo.modifier?substring(0,vo.modifier?index_of("-"))}
                                    <#else>
                                        ${vo.modifier}
                                    </#if>
                                </#if>
                            </td>
                            <td>
                                    <#if vo.status == "aax20">
                                        <a onclick="startAudit('${vo.id}')">加急审核</a>
                                    </#if>
                                    <@shiro.hasPermission name="REPORT_CASE_LIST_CLOSE_CASE">
                                        <#if !vo.status?contains("-1") &&  !vo.status?contains("aex")>
                                            <a onclick="closeCase('${vo.id}')">关闭报案</a>
                                        </#if>
                                    </@shiro.hasPermission>
                                   <#-- <a onclick="reSendMessage('${vo.id}')">补发短信</a>-->
                                    <a onclick="caseDetail('${vo.id}')">查看详情</a>
                                    <#if claimCaseVo.status == 5>
                                        <a onclick="bindPolicyPerson('${vo.id}')">绑定保全</a>
                                    </#if>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>