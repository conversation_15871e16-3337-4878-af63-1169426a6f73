<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        const loader =  new Loaders({style:"rectangle"});
        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#batchNo").val("${claimCasePushBatch.batchNo}");
            $("#searchForm").submit();
            return false;
        }

        $(document).ready(function () {

            // 业务标签 select2初始化
            $("#applyType").select2({
                placeholder: "请选择",
                width: null
            });

            // 业务标签 select2初始化
            var labelList = [];
            <#if labelShowMap?? && (labelShowMap.keySet()?size>0)>
            <#list labelShowMap.keySet() as key>
            labelList.push({id:'${key}',text:'${labelShowMap.get(key).msg}'})
            </#list>
            </#if>
            console.log(labelList);
            $("#label").select2({
                placeholder: "请选择",
                width: null,
                data: labelList
            });
            var nowLabel =[];
            <#if claimCaseVo.label?exists>
            <#list claimCaseVo.label?split(",") as code>
            nowLabel.push("${code}");
            </#list>
            console.log(nowLabel);
            $("#label").val(nowLabel).trigger('change');
            </#if>


        });







    </script>
    <style>

        .layui-layer-msg {
            z-index: 1989101411 !important;
        }

        /*loader需要的遮罩层css start */
        #screen {
            height: 100%;
            width: 100%;
            z-index: 198910141;
            position: fixed;
            background: rgba(0,0,0,0.3);
            top:0;
        }

        #screen #screenLoading{
            margin: 0 auto;
            top: 40%;
            transform: translateY(-50%);
            background: greenyellow;
        }
        /*loader需要的遮罩层css end*/

        .clear-padding {
            padding: 0px !important;
        }

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
            border-right: 0;
            overflow: hidden;
            vertical-align: bottom;
            padding: 0px;
            display: inline-block;
        }
        .form-active > div > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
            display: flex;
        }
        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 70px 6px 30px;
            font-size: 17px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
            border-right: 1px solid #e7ecf1;
        }
        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }
        .li-blue {
            background: #0b94ea ;
            color: #fff;
        }
        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }
        td > a {
            display: inline-block;
            margin: 3px;
        }

        .td-overflow {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 100px;
        }
        .labelGroup span{
            margin-left: 5px;
            margin-right: 5px;
            background-color: #1676FF;
            color: white;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="row">
        <div class="col-sm-12">
            <div class="portlet light portlet-fit bordered">
                <div class="portlet-title">
                    <ul class="page-breadcrumb breadcrumb">
                        <li><span>RB案件推送</span> <i class="fa fa-circle"></i></li>
                        <li><span class="active">RB批次列表管理</span><i class="fa fa-circle"></i></li>
                        <li><span class="active">详情</span></li>
                    </ul>
                </div>
                <div class="portlet-body">

                    <!-- BEGIN FORM-->
                    <form id="searchForm" class="form-horizontal" action="${ctx}/claimCaseController/claimCasePushBatchDetail"
                          method="post">
                        <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                        <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                        <input id="batchNo" name="batchNo" type="hidden" value=""/>
                        <div class="form-body">
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-3" style="padding-right: 0;width:250px">批次号：${claimCasePushBatch.batchNo}</label>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-3" style="width:200px ;padding-right: 0">保司标识：${claimCasePushBatch.insCode}</label>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-3" style="padding-right: 0;width:150px">案件数量：${claimCasePushBatch.caseNum}</label>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-3" style="padding-right: 0;width:154px">
                                            案件推送状态:
                                            <#if claimCasePushBatch.caseStatus??>
                                                <#if claimCasePushBatch.caseStatus == 1>
                                                    抄单
                                                <#elseif claimCasePushBatch.caseStatus == 2>
                                                    报案
                                                <#elseif claimCasePushBatch.caseStatus == 3>
                                                    估损
                                                <#elseif claimCasePushBatch.caseStatus == 4>
                                                    核损
                                                <#elseif claimCasePushBatch.caseStatus == 5>
                                                    核赔
                                                <#elseif claimCasePushBatch.caseStatus == 6>
                                                    影像
                                                <#elseif claimCasePushBatch.caseStatus == 7>
                                                    注销
                                                </#if>
                                            </#if>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-sm-3" style="width:270px ;padding-right: 0">
                                            批次推送状态：
                                            <#if claimCasePushBatch.status??>
                                                <#if claimCasePushBatch.status == -2>
                                                全部失败
                                                <#elseif claimCasePushBatch.status == -1>
                                                部分失败
                                                <#elseif claimCasePushBatch.status == 0>
                                                推送中
                                                <#elseif claimCasePushBatch.status == 1>
                                                成功
                                                </#if>
                                            </#if>
                                        </label>
                                    </div>
                                </div>
                                <!--<div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="control-label col-sm-3" style="padding-right: 0;width:150px">案件数量：${claimCasePushBatch.caseNum}</label>
                                    </div>
                                </div>-->
                            </div>
                        </div>
                    </form>
                    <table class="table table-striped table-bordered table-hover table-header-fixed">
                        <thead>
                        <tr>
<!--                            <th width="8%">保司标识</th>
                            <th width="7%">批次号</th>
                            <th width="8%">案件ID</th>-->
                            <th width="8%">案件号</th>
                            <th width="8%">案件状态</th>
                            <th width="7%">推送状态</th>
                            <th width="7%">推送时间</th>
                            <th width="7%">返回信息</th>
                            <th width="7%">备注</th>
                            <th width="7%">操作人</th>
                            <th width="10%">操作时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        <#list page.list as vo>
                            <tr>
<!--                                <td title="">${vo.insCode}</td>
                                <td title="">${vo.batchNo}</td>
                                <td title="">${vo.claimCaseId}</td>-->
                                <td title="">${vo.claimCaseNo}</td>
                                <td class="td-overflow" title="${claimCaseStatusEumMap.get(vo.caseStatus)}">
                                    ${claimCaseStatusEumMap.get(vo.caseStatus)}
                                </td>
                                <td title="">
                                    <#if vo.status??>
                                        <#if vo.status == -1>
                                            <span class="label label-sm label-danger">失败</span>
                                        <#elseif vo.status == 0>
                                            <span class="label label-sm label-warning">待推送</span>
                                        <#elseif vo.status == 1>
                                            <span class="label label-sm label-success">成功</span>
                                        </#if>
                                    </#if>
                                </td>
                                <td title="">
                                    <#if vo.pushTime??>
                                        ${vo.pushTime?string["yyyy-MM-dd HH:mm:ss"]}
                                    <#else>
                                        &nbsp;
                                    </#if>
                                </td>
                                <td title="">${vo.msg}</td>
                                <td title="">${vo.remark}</td>
                                <td title="">${vo.creator}</td>
                                <td title="">
                                    <#if vo.createTime??>
                                        ${vo.createTime?string["yyyy-MM-dd HH:mm:ss"]}
                                    <#else>
                                        &nbsp;
                                    </#if>
                                </td>
                            </tr>
                        </#list>
                        </tbody>
                    </table>
                    <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                        <div class="modal-dialog">
                            <img src="${ctx}/images/load.gif">
                        </div>
                    </div>
                </div>
                <!-- END FORM-->
            </div>
        </div>
    </div>
    <@sc.pagination page=page />
</body>
</html>