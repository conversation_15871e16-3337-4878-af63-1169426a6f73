<!--[if IE 8]>
<html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]>
<html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
<!--<![endif]-->
<head>
    <meta charset="utf-8"/>
    <title>产品展示</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">

    <link href="${ctx}/metronic/global/plugins/jquery-nestable/jquery.nestable.css" rel="stylesheet" type="text/css" />
    <script src="${ctx}/metronic/global/plugins/jquery-nestable/jquery.nestable.js" type="text/javascript"></script>
    <script type="text/javascript">

        var curFiles = [];

        var uploadAttachType;

        $(document).ready(function () {

            var map = '${imgInfoMap}';
            var imgInfoMap = JSON.parse(map);
            for (var imageType of Array.from(Object.keys(imgInfoMap))) {
                var elementId = imageType + "-dropzone";
                var dropZone = document.getElementById(elementId);
                dropZone.addEventListener('dragover', handleDragOver, false);
                dropZone.addEventListener('drop', handleFileSelect, false);
            }



            // 20241126 取消点击图片操作，防止点击后重新出现删除按钮
            // activate Nestable for list 1
            // $('#nestable_list_1').nestable({
            //     group: 1,
            //     maxDepth: 1
            // }).on("change", function () {
            //     $.each($(".deleteF"), function (index, obj) {
            //         $(this).remove();
            //     });
            //     $.each($(".dd-handle"), function (index, obj) {
            //         $(this).parent().after(`<span class="glyphicon glyphicon-remove deleteF"></span>`);
            //     });
            //
            // });

            $("body").on("change", "#files", function () {
                console.log("files方法");
                var _file = this.files;
                // 存储更新所选文件
                console.log(_file);

                if (_file.length > 0) {
                    for (let i = 0; i < _file.length; i++) {
                        var reader = new FileReader();
                        let fileName = _file[i].name;
                        let nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;
                        reader.readAsDataURL(_file[i]);
                        reader.onload = function () {
                            let imgURl = this.result;

                            $("#" + uploadAttachType + "-addAttach").before(`
                        <li class="dd-item" newAttach="1" attachFileNmae="` + fileName + `">
                            <div class="dd-handle">
                                <img  src="` + imgURl + `" onerror="javascript:this.src='/a/job_done.png'">
                            </div>
                            <input title="` + nameWithoutExt + `" type="text" class="img-name" value="` + nameWithoutExt + `" maxlength="64" />
                            <div class="name-error" style="display: none; color: red; font-size: 12px; white-space: nowrap">*照片名称编辑支持中文、英文、数字、(、)、_</div>
                        </li>
                        <span class="glyphicon glyphicon-remove deleteF"></span>`);
                        }
                    }
                }

                if (_file && _file.length) {
                    Array.prototype.push.apply(curFiles, _file);
                }

                $("#files").val('');
            });

            function handleDragOver(event) {
                event.stopPropagation();
                event.preventDefault();
                event.dataTransfer.dropEffect = 'copy';
            }

            function handleFileSelect(event) {
                event.stopPropagation();
                event.preventDefault();

                uploadAttachType = $(this).attr("upload_attach_type");

                var _file = event.dataTransfer.files;

                if (_file.length > 0) {
                    for (let i = 0; i < _file.length; i++) {
                        var reader = new FileReader();
                        let fileName = _file[i].name;
                        let nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;
                        reader.readAsDataURL(_file[i]);
                        reader.onload = function () {
                            let imgURl = this.result;

                            $("#" + uploadAttachType + "-addAttach").before(`
                        <li class="dd-item" newAttach="1" attachFileNmae="` + fileName + `">
                            <div class="dd-handle">
                                <img  src="` + imgURl + `" onerror="javascript:this.src='/a/job_done.png'">
                            </div>
                            <input title="\` + nameWithoutExt + \`" type="text" class="img-name" value="` + nameWithoutExt + `" maxlength="64" />
                            <div class="name-error" style="display: none; color: red; font-size: 12px; white-space: nowrap">*照片名称编辑支持中文、英文、数字、(、)、_</div>
                        </li>
                        <span class="glyphicon glyphicon-remove deleteF"></span>`);
                        }
                    }
                }

                if (_file && _file.length) {
                    Array.prototype.push.apply(curFiles, _file);
                }

                $("#files").val('');
            }


            //删除文件
            $('body').on('click', '.deleteF', function (e) {
                e.preventDefault();
                e.stopPropagation();

                let parentDdItem = $(this).prev();
                let newAttach = parentDdItem.attr("newAttach");
                if (newAttach == "1") {
                    let name = parentDdItem.attr("attachFileNmae");
                    console.log(name);
                    // 去除该文件
                    var index = -1;
                    for (let i = 0; i < curFiles.length; i++) {
                        let file = curFiles[i];

                        if (file.name == name) {
                            console.log("进入");
                            index = i;
                            break;
                        }
                    }
                    console.log(index);
                    if (index != -1) {
                        curFiles.splice(index, 1)
                        console.log(curFiles);
                        parentDdItem.remove();
                        $(this).remove();
                    }
                } else {
                    parentDdItem.addClass("detelAttach");
                    parentDdItem.attr("isDelete", "1");
                    $(this).remove();
                }
            });
        });

        function uploadImage(imgaeType) {
            uploadAttachType = imgaeType;
            $("#files").click();
        }

        function closeAttachList() {
            var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
            parent.layer.close(index); //再执行关闭
        }

        function submitAttachList() {
            layer.msg("正在上传影像资料中，请稍候", {icon: 16, time: 10000, shade: [0.1, '#000']});
            //是否删除，id，type，file
            let sendData = [];
            let initData = $(".dd-handle");
            let newAttachNum = 0;

            let fileMap = {};
            for (let i = 0; i < curFiles.length; i++) {
                let file = curFiles[i];
                fileMap[file.name] = file;
            }

            let errorMsg = "";
            $.each(initData, function (index, obj) {
                let ddItem = $(this).parent();
                let isNewAttach = ddItem.attr("newAttach");
                let attachType = ddItem.closest("div[name='attachTypeArea']").attr("attach-type");
                console.log(attachType);
                if (isNewAttach == '1') {
                    let attachFileNmae = ddItem.attr("attachFileNmae");
                    let attachName = ddItem.find(".img-name").val().trim();
                    console.log("新上传的attachName:" + attachName)
                    newAttachNum++;
                    if (!(attachFileNmae in fileMap)) {
                        errorMsg = "文件上传错误！！！";
                    } else {
                        sendData.push({
                            "isDelete": "0",
                            "id": "",
                            "attachType": attachType,
                            "fileName": attachFileNmae,
                            "attachName": attachName
                        })
                    }
                } else {
                    let isDelete = ddItem.attr("isDelete");
                    let id = ddItem.attr("id");
                    if (isDelete == "1") {
                        //判断老影像是否删除
                        sendData.push({
                            "isDelete": "1",
                            "id": id,
                            "attachType": attachType
                        })
                    } else {
                        //判断未删除的老影像是否修改的影像类型
                        let oldAttahType = ddItem.attr("oldAttahType");
                        let oldAttachName = ddItem.attr("oldAttachName")
                        let attachName = ddItem.find(".img-name").val();
                        console.log("这是要修改的attachName:" + attachName)
                        console.log("这是旧的attachName：" + oldAttachName)
                        if (oldAttahType !== attachType || oldAttachName !== attachName) {
                            sendData.push({
                                "isDelete": "0",
                                "id": id,
                                "attachType": attachType,
                                "attachName": attachName
                            })
                        }
                    }
                }
            });

            console.log(typeof  JSON.stringify(sendData));
            console.log(JSON.stringify(sendData));
            if(errorMsg!=""){
                layer.msg(errorMsg,{icon: 2,time: 200});
                return;
            }

            var formData = new FormData();
            // for(let i=0;i<sendData.length;i++){
            //     formData.append("sendData",sendData[i]);
            // }
            formData.append("sendData",JSON.stringify(sendData));
            for (let i = 0; i < curFiles.length; i++) {
                formData.append("files[]",curFiles[i]);
            }
            let claimCaseId = `${claimCaseId}`;
            formData.append("claimCaseId",claimCaseId);
            $.ajax({
                url: "${ctx}/claimCaseController/modifyAttach",
                type: 'POST',
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                data:formData,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layer.msg(result.msg, {
                            icon: 1,
                            time: 2000,
                            offset: '300px',
                            shade: [0.1, '#000']
                        },function () {
                            parent.location.reload();
                        });
                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 3000,
                            offset: '300px',
                            shade: [0.1, '#000']
                        });
                    }
                },
                error: function (data) {
                    $("#confirmBtn").prop('disabled', false);
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

        }

        window.onscroll = function() {
            var floatingW = document.getElementById('floatingW');
            var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            if (scrollTop > 0) {
                floatingW.style.top = '0';
            } else {
                floatingW.style.top = '-50px'; // 根据需要调整
            }
        };

        let isComposing = false;

        // 监听拼音输入法组合状态
        $(document).on('compositionstart', '.img-name', function() {
            isComposing = true;
        });

        $(document).on('compositionend', '.img-name', function() {
            isComposing = false;
            validateInput($(this));
        });

        // 输入事件过滤非法字符，兼容拼音输入
        $(document).on('input', '.img-name', function() {
            if (!isComposing) {
                validateInput($(this));
            }
        });

        function validateInput($input) {
            const val = $input.val();
            const validPattern = /^[\u4e00-\u9fa5a-zA-Z0-9()_]*$/;

            // 显示或隐藏错误提示
            if (!validPattern.test(val)) {
                let $errorTip = $input.siblings('.name-error');
                $errorTip.show();
                setTimeout(function () {
                    $errorTip.hide();
                }, 5000);
            } else {
                $input.siblings('.name-error').hide();
            }

            // 过滤非法字符
            const filtered = val.replace(/[^\u4e00-\u9fa5a-zA-Z0-9()_]/g, '');
            if (val !== filtered) {
                $input.val(filtered);
            }
        }

        $(document).on("input", ".img-name", function () {
            var newName = $(this).val();
            $(this).attr("title", newName);
        });
    </script>
    <style type="text/css">

        #eaitAttachHtml{
            height: 700px !important;
            overflow: scroll;
        }

        #eaitAttachHtml::-webkit-scrollbar {
            display: none; /* Chrome Safari */
        }

        #eaitAttachHtml {
            scrollbar-width: none; /* firefox */
            -ms-overflow-style: none; /* IE 10+ */
        }

        /*#nestable_list_1 {
            height: 700px !important;
            overflow: scroll;
        }

        #nestable_list_1::-webkit-scrollbar {
            display: none; !* Chrome Safari *!
        }

        #nestable_list_1 {
            scrollbar-width: none; !* firefox *!
            -ms-overflow-style: none; !* IE 10+ *!
        }*/

        #nestable_list_1 .dd-list {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            flex-grow: 0;
        }

        #nestable_list_1 .dd-item {
            margin-top: 30px;
            padding: 1% 1%;
            width: 16.6%;
            height: 150px !important;
        }

        #nestable_list_1 .dd-handle {
            height: 100%;
        }

        #nestable_list_1 img {
            width: 100%;
            height: 100%;
        }

        .portlet-body {
            padding: 0px 5%;
        }

        .deleteF {
            color: #CA0000;
            position: relative;
            top: 5px;
            right: 3px;
            z-index: 9999999999
        }

        .detelAttach {
            display: none;
        }

        body > .dd-dragel {
            width: auto !important;
        }
        body > .dd-dragel .dd-handle {
            height: auto !important;
        }
        body > .dd-dragel img {
            width: 180px;
            height: 160px;
        }
        #floatingW {
            position: fixed;
            z-index: 1000;
            transition: top 0.5s; /* 平滑过渡效果 */
        }
        .img-name {
            width: 170px;
            max-width: 300px;       /* 控制最大宽度 */
            white-space: nowrap;    /* 不换行 */
            overflow: hidden;       /* 超出隐藏 */
            text-overflow: ellipsis; /* 省略号 */
            display: inline-block;
            vertical-align: middle;
        }
    </style>
</head>
<body>
<!-- BEGIN PAGE BASE CONTENT -->
<div class="row" id="eaitAttachHtml">
    <div class="col-sm-12" >
        <div  class="col-sm-12">
            *照片名称编辑支持中文、英文、数字、(、)、_
        </div>
        <div class="row" id="floatingW" style="padding-left: 75%;margin: 40px;margin-bottom: 20px;">
            <button class="btn" style="background-color: #1676FF;color: white"
                    onclick="submitAttachList()">确认
            </button>
            <button class="btn" style="background-color: #1676FF;color: white"
                    onclick="closeAttachList()">取消
            </button>
        </div>
        <!-- BEGIN EXAMPLE TABLE PORTLET-->
        <div class="portlet light portlet-fit" >
            <div class="portlet-body" style="border-bottom: 1px solid #ababab;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="dd" id="nestable_list_1">
                            <#if imgInfoMaps?exists>
                            <#list imgInfoMaps.keySet() as key>
                            <div name="attachTypeArea" attach-type="${key}">
                                <span style="width: 100%;">${imgInfoMaps.get(key)}</span>
                                <ol class="dd-list">
                                    <#list attachCollect.get(key) as attach>
                                    <li class="dd-item" newAttach="0" oldAttahType="${key}"
                                        oldAttachName = "${attach.attachName!''}"
                                        id="${attach.id}">
                                        <div class="dd-handle">
                                            <img src="${attach.fileObjectId}"
                                                 onerror="javascript:this.src='/a/job_done.png'">
                                            <input title="${attach.attachName!''}" style="margin-top: 11px; width: 170px" type="text" class="img-name" value="${attach.attachName!''}" maxlength="64" />
                                            <div class="name-error" style="display: none; color: red; font-size: 12px; white-space: nowrap">*照片名称编辑支持中文、英文、数字、(、)、_</div>
                                            <#--                                                        <button class="btn btn-danger deleteF">删除</button>-->
                                        </div>
                                    </li>
                                    <!-- 删除影像权限 -->
                                    <@shiro.hasPermission name="DELETE_CLAIM_ATTACH">
                                    <!-- 案件已结案不允许删除影像 -->
                                    <#if !claimCaseStatus?contains("aex")>
                                        <span class="glyphicon glyphicon-remove deleteF"></span>
                                    </#if>
                                    </@shiro.hasPermission>

                            </#list>
                                <div style="margin-bottom: 50px" class="dd-item" id="${key}-addAttach">
                                    <div id="${key}-dropzone" upload_attach_type="${key}">
                                        <img src="${attach.fileObjectId}"
                                             onerror="javascript:this.src='/a/addImgIcon.png'"
                                             onclick="uploadImage('${key}')">
                                    </div>
                                </div>
                                </ol>
                            </div>
                        </#list>
                    </#if>
                    <#--<#if attachCollect.get("未知")??>
                    <span style="width: 100%">未知</span>
                    <ol class="dd-list">
                        <#list attachCollect.get("未知") as attach>
                        <li class="dd-item" newAttach="0" oldAttahType="未知"
                            id="${attach.id}">
                            <div class="dd-handle">
                                <img src="${attach.fileObjectId}"
                                     onerror="javascript:this.src='/a/job_done.png'">
                                &lt;#&ndash;                                                        <button class="btn btn-danger deleteF">删除</button>&ndash;&gt;
                            </div>
                        </li>
                        <span class="glyphicon glyphicon-remove deleteF"></span>
                    </#list>
                    </ol>
                </#if>-->
            </div>
        </div>
        <input multiple="multiple" accept="image/*" type="file" id="files" name="file" style="display:none">
    </div>
</div>
</div>
<!-- END EXAMPLE TABLE PORTLET-->
</div>
</div>
<!-- END PAGE BASE CONTENT -->
</body>
</html>