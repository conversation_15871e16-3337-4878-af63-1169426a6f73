<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <!-- Custom styles for this template -->
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <#--widget-->


    <script type="text/javascript">

        /*日期格式转换,时间戳转标准格式*/
        function msToDate (msec) {
            let datetime = new Date(msec);
            let year = datetime.getFullYear();
            let month = datetime.getMonth();
            let date = datetime.getDate();
            let hour = datetime.getHours();
            let minute = datetime.getMinutes();
            let second = datetime.getSeconds();

            let result1 = year +
                '-' +
                ((month + 1) >= 10 ? (month + 1) : '0' + (month + 1)) +
                '-' +
                ((date) < 10 ? '0' + date : date) +
                ' ' +
                ((hour + 1) < 10 ? '0' + hour : hour) +
                ':' +
                ((minute + 1) < 10 ? '0' + minute : minute) +
                ':' +
                ((second + 1) < 10 ? '0' + second : second);
            let result = {
                hasTime: result1
            };

            return result;
        }
        function iframeH() {
            var ifm = parent.document.getElementById("contentFrame");
            if (ifm) {
                var ifmH = $(ifm).height()
                var h = $(document.body).height() + 120;
                if (ifmH < h) {
                    parent.document.getElementById("contentFrame").height = h;
                }
            }
        }
        /*两个日期最大不超过30天*/
        function isIntervalLessThanThirtyDays(dateString1, dateString2) {
            // 将日期字符串转换为Date对象
            var date1 = new Date(dateString1);
            var date2 = new Date(dateString2);

            // 计算两个日期之间的天数差
            var timeDiff = Math.abs(date2.getTime() - date1.getTime());
            var daysDiff = timeDiff / (1000 * 3600 * 24);

            // 检查天数差是否不超过30天
            return daysDiff <= 30;
        }
        $(document).ready(function () {
            // baseUserId = document.getElementById("baseUserInput").value;
            $(".logListInfo").on("click", ".rowInfo", function (e) {
                e.stopPropagation();
                console.log('hhhh')
                let icon = $(this).children(1).children(1);
                var iconClass = icon.attr("class");
                if (iconClass == 'icon-plus') {
                    icon.attr("class", "minus");
                    $(this).next().show();
                }
                if (iconClass == 'minus') {
                    icon.attr("class", "icon-plus");
                    $(this).next().hide();
                }
                iframeH();
            });



        });

        function downLoadDZBD(policyPersonId){
            if (typeof policyPersonId == 'undefined' || policyPersonId == '') {
                layerTop.msg('暂无数据！！！', {
                    icon: 2,
                    time: 2000
                });
                return;
            }

            var formData = new FormData();
            formData.append("policyPersonId", policyPersonId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/onloadEPolicy",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        window.open(result.msg, "_blank");
                    } else {
                        layerTop.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }
        var layerTop = top.layer;

        function queryByDate(){
          var endDate = $('#endDate').val();
          var endDate1 = $('#endDate1').val();
            if (endDate!=="" && endDate1!=="" && !isIntervalLessThanThirtyDays(endDate,endDate1)){
                layerTop.msg("查询失败：两个日期间隔最大不能超过30天！！！", {
                    icon: 2,
                    time: 2000
                });
                return
            }

            // 组装数据对象
            var dataObj = {
                endDate: endDate,
                endDate1: endDate1
                // 可以添加更多需要发送给后端的数据
            };

            // 将数据对象序列化为JSON字符串
            var jsonData = JSON.stringify(dataObj);
            let baseUserId = document.getElementById("baseUserInput").value;
            $.ajax({
                url: "${ctx}/insuranceCaseController/getHistoryRecordPersonAjax?baseUserId="+baseUserId,
                type: 'POST',
                data: jsonData,
                async: true,
                cache: false,
                contentType: 'application/json',
                processData: false,
                success: function (data) {
                    layerTop.msg("查询成功", {
                        icon: 1,
                        time: 2000
                    });
                    var result = eval("(" + data + ")");
                    var newPolicyPersonList = result.policyPersonList;
                    $('#policyPersonTableBody').empty();
                    // 检查返回的数据是否为空
                    if (newPolicyPersonList.length === 0) {
                        $('#policyPersonTableBody').append(
                            '<tr><td colspan="7"><h2>暂无数据！！</h2></td></tr>'
                        );
                        return;
                    }
                    // 遍历返回的新数据，并构建新的表格行
                    newPolicyPersonList.forEach(function(policyPerson) {

                        let policyList = policyPerson;
                        $('#policyPersonTableBody').append(
                            "<tr class=\"rowInfo\">" +
                            "<td>"+policyList.policyNo+"</td>" +
                            "<td>"+policyList.customerPolicyNo+"</td>" +
                            "<td>"+msToDate(policyList.startDate).hasTime+"</td>" +
                            "<td>"+msToDate(policyList.endDate).hasTime+"</td>" +
                            "<td>" +
                            "<button class=\"btn-bule btn\" " +
                            "onclick=\"downLoadDZBD('"+policyPerson.id+"')\">电子保单</button>" +
                            "</td></tr>"
                        );
                    });
                }
            });
        }



    </script>
    <style>
        .attachInfo label {
            margin-top: 20px;
        }

        .attachInfo img {
            width: 100%;
            height: 100%;
            border: 1px solid;
        }

        .attachInfo .col-sm-2 {
            margin-top: 20px !important;
        }

        .btn-bule {
            color: #FFFFFF;
            background-color: #1676FF;
            border-color: #1676FF;
            border-radius: 10px;
            margin: 2px;
        }

        .btn-bule:hover {
            color: #FFFFFF;
        }

        .attachInfo .icon-attach {
            display: block;
            position: absolute;
            background-color: #D8D8D8;
            color: #709BF3;
            width: 20px;
            height: 25px;
            right: 15px;
            top: 0px;
            text-align: center;
            line-height: 25px;
            font-size: 10px;
        }

        .payDutyInfo .col-sm-6 {
            display: block;
            height: 246px;
            padding: 0px 2.5%;
            margin-bottom: 20px;;
        }

        .payDutyInfo .notice {
            width: 100%;
            height: 100%;
            border-radius: 10px;
            color: white;
            padding: 2% 5% 2% 2%;
        }


        .logInfo button {
            margin-top: 20px;
            margin-right: 30px;
        }

        .logListInfo {
            margin-bottom: 2%;
        }

        .logListInfo table {
            border: 1px solid #C2C2C2;
        }

        .logListInfo .detailsInfo {
            display: none;
            padding-left: 8%;
        }

        .logListInfo .detailsInfo > td {
            word-wrap: break-word;
            word-break: break-all;
        }

        .logListInfo .rowInfo:hover {
            cursor: pointer;
        }


        #reSendMessageContainer .radio {
            margin-left: 0px;
        }

        td{
            text-align: center;
        }

        .block-head-label {
            margin-bottom: 1%;
            font-size: 20px;
            margin-left: 15px;
        }
    </style>
</head>

<body>


<div class="col-sm-3 form-group clear-padding" style="padding-top: 50px;left: 120px">
    <input type="text" id="baseUserInput" value="${baseUserId}" hidden="hidden">
    <label class="control-label col-sm-3" style="padding-right: 0;width: auto;left: -30px">保单起止日期：</label>
    <div class="col-sm-8" style="padding-right: 0;width: 120%;left: 80px;top: -30px">
        <div class="input-group date-picker input-daterange"
             data-date-format="yyyy-mm-dd" d>
            <input type="text" class="form-control" name="treatDateStart" id="endDate" autocomplete="off"
            >
            <span class="input-group-addon"><font style="vertical-align: inherit;"><font
                    style="vertical-align: inherit;"> 至 </font></font></span>
            <input type="text" class="form-control" name="treatDateEnd" id="endDate1" autocomplete="off"
            >
        </div>
    </div>
</div>

<div class="btn-group pull-right" style="position: relative;left: -50px;top: 60px;">
    <div  id="query" type="button" class="btn green" onclick="queryByDate()">查询</div>
</div>



<div class="row" >
    <div class="col-sm-12" style="border-top: 1px solid #8c918d">
        <div class="row logListInfo" style="padding: 3%">
            <div class="block-head-label">
                历史保单信息
            </div>
            <div class="col-sm-12">
                <table class="table">
                    <thead>
                    <tr>
                        <td>保单号</td>
                        <td>订单号</td>
<!--                        <td>被保险人姓名</td>-->
<!--                        <td>被保险人身份证号</td>-->
                        <td>保单起期</td>
                        <td>保单止期</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody id="policyPersonTableBody">
                    <#if policyPersonList??>
                        <#list policyPersonList as policyPerson>
                            <tr class="rowInfo">
                                <td>
                                    ${policyPerson.policyNo!'-'}
                                </td>
                                <td>${policyPerson.customerPolicyNo!'-'}</td>
<!--                                <td>-->
<!--                                    ${policyPerson.name!'-'}-->
<!--                                </td>-->
<!--                                <td>-->
<!--                                    ${policyPerson.idNumber!'-'}-->
<!--                                </td>-->
                                <td>
                                    ${policyPerson.startDate?string["yyyy-MM-dd HH:mm:ss"]}
                                </td>
                                <td>
                                    ${policyPerson.endDate?string["yyyy-MM-dd HH:mm:ss"]}
                                </td>
                                <td>
                                    <button class="btn-bule btn" onclick="downLoadDZBD('${policyPerson.id}')">电子保单</button>
                                </td>
                            </tr>
                        </#list>
                        <#else >
                        <tr>
                            <td colspan="7"><h2>暂无数据！！</h2></td>
                        </tr>
                    </#if>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
</div>
</body>
</html>