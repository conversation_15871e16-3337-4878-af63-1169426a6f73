<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <!-- Custom styles for this template -->
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <#--widget-->


    <script type="text/javascript">
        function iframeH() {
            var ifm = parent.document.getElementById("contentFrame");
            if (ifm) {
                var ifmH = $(ifm).height()
                var h = $(document.body).height() + 120;
                if (ifmH < h) {
                    parent.document.getElementById("contentFrame").height = h;
                }
            }
        }

        $(document).ready(function () {

            $(".logListInfo").on("click", ".rowInfo", function (e) {
                e.stopPropagation();
                console.log('hhhh')
                let icon = $(this).children(1).children(1);
                var iconClass = icon.attr("class");
                if (iconClass == 'icon-plus') {
                    icon.attr("class", "minus");
                    $(this).next().show();
                }
                if (iconClass == 'minus') {
                    icon.attr("class", "icon-plus");
                    $(this).next().hide();
                }
                iframeH();
            });

        });

        function seeCaseDetail(claimCaseId) {
            window.open("${ctx}/insuranceCaseController/caseDetail?caseId="+claimCaseId);
        }

        function seeVerifyApplyBook(claimCaseId) {
            window.open("${ctx}/claimCaseController/viewClaimApplyBook?claimCaseId="+claimCaseId);
        }
    </script>
    <style>
        .attachInfo label {
            margin-top: 20px;
        }

        .attachInfo img {
            width: 100%;
            height: 100%;
            border: 1px solid;
        }

        .attachInfo .col-sm-2 {
            margin-top: 20px !important;
        }

        .btn-bule {
            width: 100px;           /* 固定宽度 */
            height: 40px;           /* 固定高度 */
            color: #FFFFFF;
            background-color: #1676FF;
            border-color: #1676FF;
            border-radius: 4px !important;     /* 圆角 */
            margin: 2px;
        }

        .btn-bule:hover {
            color: #FFFFFF;
        }

        .attachInfo .icon-attach {
            display: block;
            position: absolute;
            background-color: #D8D8D8;
            color: #709BF3;
            width: 20px;
            height: 25px;
            right: 15px;
            top: 0px;
            text-align: center;
            line-height: 25px;
            font-size: 10px;
        }

        .payDutyInfo .col-sm-6 {
            display: block;
            height: 246px;
            padding: 0px 2.5%;
            margin-bottom: 20px;;
        }

        .payDutyInfo .notice {
            width: 100%;
            height: 100%;
            border-radius: 10px;
            color: white;
            padding: 2% 5% 2% 2%;
        }


        .logInfo button {
            margin-top: 20px;
            margin-right: 30px;
        }

        .logListInfo {
            margin-bottom: 2%;
        }

        .logListInfo table {
            border: 1px solid #C2C2C2;
        }

        .logListInfo .detailsInfo {
            display: none;
            padding-left: 8%;
        }

        .logListInfo .detailsInfo > td {
            word-wrap: break-word;
            word-break: break-all;
        }

        .logListInfo .rowInfo:hover {
            cursor: pointer;
        }


        #reSendMessageContainer .radio {
            margin-left: 0px;
        }

        td{
            text-align: center;
        }

        .applyTypeGroup .AA001,.AA002,.AA003,.AA004{
            background-color: #1676ff !important;
            color: white;
        }
        .applyTypeGroup .AB001,.AB002,.AB003,.AB004{
            background-color: #ff6633 !important;
            color: white;
        }
        .applyTypeGroup .AC001,.AC002,.AC003,.AC004{
            background-color: #CA0000 !important;
            color: white;
        }

        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }

        .label-hide-overflow {
            max-width: 195px !important;
        }

        .block-head-label {
            margin-bottom: 1%;
            font-size: 20px;
            margin-left: 15px;
        }
    </style>
</head>

<body>

<div class="row" >
    <div class="col-sm-12">
        <div class="row logListInfo" style="padding: 5% 3%">
            <div class="block-head-label">
                历史案件信息
            </div>
            <div class="col-sm-12">
                <table class="table">
                    <thead>
                    <tr>
                        <td width="12%">报案人姓名</td>
                        <td width="10%">报案人手机号</td>
                        <td width="12%">出险人姓名</td>
                        <td width="10%">出险人身份证</td>
                        <td width="10%">案件类型</td>
                        <td width="10%">报案时间</td>
                        <td width="10%">出险时间</td>
                        <td width="15%" class="label-hide-overflow">事故信息描述</td>
                        <td width="11%">功能</td>
                    </tr>
                    </thead>
                    <tbody>
                    <#if claimCaseList??>
                        <#list claimCaseList as claimCase>
                            <tr class="rowInfo">
                                <td>
                                    ${claimCase.applyName!'-'}
                                </td>
                                <td>${claimCase.applyMobile!'-'}</td>
                                <td>
                                    ${claimCase.treatName!'-'}
                                </td>
                                <td>
                                    ${claimCase.treatIdNum!'-'}
                                </td>
                                <td title="">
                                    ${caseTypeMap.get(claimCase.caseType)!'未分类'}
                                </td>
                                <td>
                                    ${claimCase.startDate?string["yyyy-MM-dd HH:mm:ss"]}
                                </td>
                                <td>
                                    ${claimCase.treatDate?string["yyyy-MM-dd HH:mm:ss"]}
                                </td>
                                <td class="label-hide-overflow" title="${claimCase.description}">
                                    ${claimCase.description!'-'}
                                </td>
                                <td>
                                    <button class="btn btn-bule" onclick="seeCaseDetail('${claimCase.id}')">查看详情</button>
                                    <button class="btn btn-bule" onclick="seeVerifyApplyBook('${claimCase.id}')">理赔申请书</button>
                                </td>
                            </tr>
                        </#list>
                        <#else >
                        <tr>
                            <td colspan="7"><h2>暂无数据！！</h2></td>
                        </tr>
                    </#if>

                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
</div>
</body>
</html>