<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>赔案审核</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <#--<link rel="stylesheet" type="text/css" href="${ctx}/static/css/claimApplyReview.css"/>-->
    <link rel="stylesheet" type="text/css" href="${ctx}/static/css/dataCollection.css"/>
    <script src="${ctx}/carousel/dataCollection/viewer.js"></script>
    <script src="${ctx}/carousel/dataCollection/viewermain.js"></script>
    <link href="${ctx}/carousel/dataCollection/viewer.css" rel="stylesheet">
    <script src="${ctx}/static/js/BigDecimal.js" type="text/javascript"></script>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/metronic/global/scripts/app.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/layout.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/demo.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/jquery-multi-select/js/jquery.multi-select.js"
            type="text/javascript"></script>
    <#--    试算js-->
    <script src="${ctx}/carousel/TrialVerify.js"></script>
    <style>

        .chart-search {
            margin-bottom: 10px;
            display: none;
        }


        .search-channel-content {
            height: 100px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .search-ul-list {
            list-style-type: none;
            height: 20px;
            padding-left: 0px;
            color: black;
        }

        .channel {
            float: left;
            display: block;
        }

        .typeClick {
            height:112px;
            margin-bottom: 10px;
            border:2px solid black;
            border-radius:5px!important;
            margin-left: 20px;
            cursor: pointer
        }

        .typeClickClick {
            height:112px;
            margin-bottom: 10px;
            border:2px solid #1676ff;
            background-color: #1676ff;
            color: white;
            border-radius:5px!important;
            margin-left: 20px;
            cursor: pointer
        }
        .has-error {
            border-color: #ce0d0d!important;
            color: red!important;
        }
        .bckColor {
            background-color: rgba(51, 153, 241, 0.3);
        }
        .imageListChoose{
            /*background-color: #bad6ff !important;*/
            border: 5px solid #055ad7;
        }
        .collectionCompleted {
            background-color: #E8F2FF;
        }
        .collectionButton{
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: transparent;
            border-radius: 6px !important;
            color: #1ABB00;
        }
        .collectionButtonClick{
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00 !important;
            border-radius: 6px !important;
            color: #FFFFFF;
        }
        .collectionData:hover{
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00;
            color: #FFFFFF;
        }
        .middleTable{
            border: 4px solid #1767ff !important;
        }
        .hangup {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #ce0d0d !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }
        .error {
            color: #ce0d0d !important;
        }
        .txt-over-hide {
            color: red;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display:block;
            width:650px;
        }
        .jump {
            /*float: right;*/
            border-radius: 5px !important;
            color: #3399ff !important;
            background-color: #fff !important;
            border-color: #3399ff;
            height: 40px;
            width: 100%;
        }
        .genTask {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #3399ff !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }
        .thClass{
            text-align: center;
            color: #fff;
        }
        .typeSelect{
            border-color: #3399ff;
            background-color: #3399ff;
        }

        .thumbnail-img {
            width: 50px;
            height: 45px;
            border: 4px solid #D6D6C1;
        }
        .selected-thumbnail-img{
            border: 4px solid #3399ff!important;
        }
        .collection-thumbnail-img{
            border: 4px solid #00B176;
        }

        /*里面的代码可以根据自己需求去进行更改*/
        /* 设置滚动条的样式 */
        ::-webkit-scrollbar {
            width:5px;
        }
        /* 滚动槽 */
        ::-webkit-scrollbar-track {
            -webkit-box-shadow:inset006pxrgba(0,0,0,0.3);
            border-radius:10px;
        }
        /* 滚动条滑块 */
        ::-webkit-scrollbar-thumb {
            border-radius:10px;
            background:rgba(0,0,0,0.1);
            -webkit-box-shadow:inset006pxrgba(0,0,0,0.5);
        }
        ::-webkit-scrollbar-thumb:window-inactive {
            background:rgba(255,0,0,0.4);
        }

        .ticket-information{
            height: 1050px;
            overflow-y: scroll;
        }

        .dataDisplayArea{
            width: 100%;
            height: 19%;
            border-radius: 10px !important;
            /*border: 1px solid #aca8a8;*/
            border: 1px solid #797979;
            cursor: pointer;
            margin-top: 10px;
            overflow: hidden;
        }
        .selected-dataDisplayArea{
            background-color: #91cae9;
        }
        .submit-dataDisplayArea{
            border: 3px solid #44db69 !important;
        }
        .change-dataDisplayArea{
            border: 3px solid red !important;
        }
        .dataDisplayArea>div{
            height: 20%;
            padding: 10px 0px 0px 0px !important;
        }
        .dataDisplayArea-head{
            background-color: #3399ff;
            /*border-radius: 10px 10px 0px 0px !important;*/
        }
        .dataDisplayArea-head-left{
            color: #ffffff;
            font-size: 15px;
            padding: 0px 0px 0px 5px;
        }
        .dataDisplayArea-head-right{
            text-align: right;
            padding: 0px !important;
        }
        .dataDisplayArea-head-img{
            width: 30px;
            height: 18px;
        }
        .dataDisplayArea-body-left{
            opacity: 0.7;
        }
        .dataDisplayArea-body-right{
            text-align: right;
        }

        .row{
            margin: 0px 0px;
        }
    </style>

    <script type="text/javascript">

        if ("yes" == "${urgentTaskMSG}") {
            alert("已有该类型的任务，请处理当前任务");
        }

        if ("no" == "${haveTask}") {
            if ("${msg}" != "") {
                alert("${msg}");
            }else {
                if ("${skipMsg}" != "") {
                    alert("没有需要处理的任务,跳过任务数为：${skipMsg}");
                }else {
                    alert("没有需要处理的任务");
                }
            }
        }

        if ("${errorMSG}" != "") {
            alert("${errorMSG}");
        }
        var viewer = null;


        /*影像类型改变的影像list*/
        let imageTypeChangeMap = new Map();

        /*哪些影像类型可以采集*/
        const collectedImgTypes = ['事故照片','其他照片','出险人身份证','医疗费用资料','残疾证明资料','理赔申请书','申请人身份证','结案通知书','被保人签名','银行卡信息','食品照片','就诊照片','就诊发票照片','订单详情页照片','纸质材料'];
        /*用于middleTable的影像类型展示*/
        let collectedImgTypesMap = new Map();
        collectedImgTypesMap.set('事故照片','事故');
        collectedImgTypesMap.set('其他照片','其他');
        collectedImgTypesMap.set('出险人身份证','出险人身份证');
        collectedImgTypesMap.set('医疗费用资料','医疗费用资料');
        collectedImgTypesMap.set('残疾证明资料','残疾证明资料');
        collectedImgTypesMap.set('理赔申请书','理赔申请书');
        collectedImgTypesMap.set('申请人身份证','申请人身份证');
        collectedImgTypesMap.set('结案通知书','结案通知书');
        collectedImgTypesMap.set('被保人签名','被保人签名');
        collectedImgTypesMap.set('银行卡信息','银行卡信息');
        collectedImgTypesMap.set('食品照片','食品照片');
        collectedImgTypesMap.set('就诊照片','就诊照片');
        collectedImgTypesMap.set('就诊发票照片','就诊发票照片');
        collectedImgTypesMap.set('订单详情页照片','订单详情页照片');
        collectedImgTypesMap.set('纸质材料','纸质材料');

        let attachTypeMap = new Map();




        //存放数据库已存在的发票信息
        let oldInviceData = new Map();

        //存放删除的数据库信息
        let deleteInviceData = new Map();

        //新添加的发票缓存信息
        let newInviceBufferData = new Map();

        //新添加的发票信息
        let newInviceData = new Map();

        $(function () {

            /*初始化时判断采集按钮是否展示*/
            $("#leftTable_tbody").find("tr").each(function () {
                /*如果影像类型不是医保结算单和医疗发票则隐藏采集按钮*/
                if (!collectedImgTypes.includes($(this).find("select").val())) {
                    $(this).find("button").hide();
                }
            });

            /*图片展示工具*/
            (function () {
                viewer = new Viewer({
                    activeId: null
                });
            })();

            //获取任务
            $('body').on('click', '.btn-gettask', function () {
                window.location.href="${ctx}/insuranceCaseController/claimVerify?type=1"
            });



            //需要采集的字段，名称，以及校验规则                  ----------------- 每个页面配置的信息可能不一样，字段名称必须和后台vo接收字段一致，时间类型注意format
            var collectField = getCollectField('${product.id}','${claimCaseSubject.subjectName}');
            //票据信息，展示字段
            const dataDisplayAreaShowFeild = ["receiptNo","receiptAmount","treatmentDateStr","leaveHospitalDateStr"];
            //初始化所要展示的输入框
            for (let field in collectField){
                let attr = collectField[field];
                let head = $('<div class="col-sm-12 ggLayer"></div>');
                let labelName = attr["labelName"];
                let valid = attr["blurValid"];
                head.append('<label class="control-label label-right label-color1">'+labelName+'</label>')
                switch (valid) {
                    case "stringCheck":
                    case "isTime":
                    case "isNumberAndDecimalPoint":
                        head.append('<input type="text" readonly="readonly" class="form-control inputClass inputStyle billCollection" id="'+field+'" ' +
                            'name="billCollection" data-attribute="limit"  data-valid="'+valid+'" placeholder="请输入"/>')
                        break;
                    case "isTrueFalse":
                        head.append('<div class="inputClass"><select disabled style="width:100%;" class="kp-select2 inputStyle form-control" id="'+field+'" name="billCollection" data-attribute="limit" data-valid="'+valid+'">' +
                            '<option value="1">是</option><option value="0">否</option></select></div>');
                        break;
                    default:
                        head.append('<input type="text" class="form-control inputClass inputStyle billCollection" id="'+field+'" ' +
                            'name="billCollection" data-attribute="limit" readonly="readonly" data-valid="none" placeholder="请输入"/>')
                }
                $("#dataCollectArea").append(head);
            }

            let check=function () {
                var _this = this;
                var val = _this.value;
                var valid = _this.getAttribute('data-valid');
                if (!checkService[valid](val)) {
                    $(_this).val("");
                    layer.msg("输入格式不对", {icon: 5, time: 1000});
                    $(_this).focus();
                    return;
                }

                if (_this.id === "treatmentDateStr" || _this.id === "leaveHospitalDateStr"){
                    //就诊/入院日期、出院日期
                    let collectionDate = val.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
                    let date = new Date(collectionDate);
                    let currentDate = new Date();
                    if(currentDate.getTime() < date.getTime()){
                        $(_this).val("");
                        layer.msg("日期大于当前时间", {icon: 5, time: 1000});
                        $(_this).focus();
                    }
                }
                if (_this.id === "receiptName") {
                    if($.trim(val) !== "" && val !== "${claimCase.treatName!''}"){
                        layer.msg("与出险人姓名不一致，请核实是否填写正确", {icon: 5, time: 1000});
                        return;
                    }
                }
            }

            //初始化票据信息
            <#--<#if claimCaseSubjectDetailList?exists && (claimCaseSubjectDetailList.size()>0)>
            <#list claimCaseSubjectDetailList as claimCaseSubjectDetail>
            let data = `<#if claimCaseSubjectDetail.leaveHospitalDate??>${claimCaseSubjectDetail.treatmentDate?string('yyyyMMdd')}-${claimCaseSubjectDetail.leaveHospitalDate?string('yyyyMMdd')}<#else >${claimCaseSubjectDetail.treatmentDate?string('yyyyMMdd')}</#if>`;
            let attachId = `#${claimCaseSubjectDetail.claimCaseAttachId}`;
            $('.ticket-information').append(generateDataDisplayArea($(attachId),{"id":`${claimCaseSubjectDetail.id}`,"ticketNumber":`${claimCaseSubjectDetail.receiptNo}`,"billAmount":`${claimCaseSubjectDetail.receiptAmount}`,"date":data}));
            if(!$(attachId).hasClass("collectionCompleted")){
                $(attachId).addClass("collectionCompleted");
            }
            </#list>
            </#if>-->

            //初始化票据信息并且将初始信息存放起来
            <#if claimCaseSubjectDetailListJsonArray?exists &&(claimCaseSubjectDetailListJsonArray?size>0)>
            <#list claimCaseSubjectDetailListJsonArray as claimCaseSubjectJsonStr>
            {let data = `<#if claimCaseSubjectJsonStr.leaveHospitalDate??>${claimCaseSubjectJsonStr.treatmentDateStr}-${claimCaseSubjectJsonStr.leaveHospitalDateStr}<#else >${claimCaseSubjectJsonStr.treatmentDateStr}</#if>`;
            let attachId = `#${claimCaseSubjectJsonStr.claimCaseAttachId}`;

            let showData = {}
            for(let index in dataDisplayAreaShowFeild){
                showData[dataDisplayAreaShowFeild[index]]=${claimCaseSubjectJsonStr}[dataDisplayAreaShowFeild[index]];
            }
            showData["id"]=`${claimCaseSubjectJsonStr.id}`;
            $('.ticket-information').append(generateDataDisplayArea($(attachId),showData));
            $('#' + `${claimCaseSubjectJsonStr.claimCaseAttachId}` + ' select').attr("disabled", "disabled");
            if(!$(attachId).hasClass("collectionCompleted")){
                $(attachId).addClass("collectionCompleted");
            }
            let oldData = {};
            for(let feild in collectField){
                oldData[feild]=${claimCaseSubjectJsonStr}[feild];
            }
            oldData["claimCaseAttachId"]=`${claimCaseSubjectJsonStr.claimCaseAttachId}`;
            oldData["receiptId"]=`${claimCaseSubjectJsonStr.id}`;
            oldInviceData.set(`${claimCaseSubjectJsonStr.id}`,oldData);}
            </#list>
            </#if>



            /*将当前订单的标签选中*/
            var claimCaseLabelList = [];
            <#if claimCaseLabelList?exists && (claimCaseLabelList?size > 0)>
            <#list claimCaseLabelList as label>
            claimCaseLabelList.push("${label.code}");
            </#list>
            </#if>



            /*获取焦点移除报错样式*/
            $("body").on("focus", "input,textarea,select,.ggLayer", function () {
                document.onkeydown = function (w) {
                }
            });

            /**
             * 生成票据信息
             *  */
            function generateDataDisplayArea(element,initData) {
                let attchId = $(element).attr('id');
                let imgType = $(element).find("select").val();
                imgTypeStr = collectedImgTypesMap.get(imgType);
                let imgSerialnumber = $(element).data('img-serialnumber');
                if(!imgSerialnumber){
                    imgSerialnumber ="0";
                }
                if(initData == undefined || initData == null){
                    initData = {"id":guid()}
                }


                let appendStr =
                    '<div id="dataDisplayArea-'+attchId+'" class="dataDisplayArea" data-img-id="'+attchId+'" data-receipt-id="'+initData.id+'" data-img-type="'+imgType+'"> ' +
                    '</div>';
                let content=$(appendStr);
                let head =$('<div class="row dataDisplayArea-head">' +
                    ' </div>');
                head.append('<div class="row" style="background-color: #3399ff;"> <div class="col-sm-9 dataDisplayArea-head-left">' + '(' + imgSerialnumber + ')'+ imgTypeStr  +
                    ' </div>' +
                    '<div class="col-sm-3 dataDisplayArea-head-right">' +
                    ' <img src="${ctx}/images/shanchu.svg" id="" class="dataDisplayArea-head-img">' +
                    '</div></div>');
                for(let index in dataDisplayAreaShowFeild){
                    let feildName=dataDisplayAreaShowFeild[index];
                    let initDatum = initData[feildName];
                    if(initDatum == undefined){
                        initDatum = "";
                    }
                    head.append(' <div class="row">' +
                        ' <div class="col-sm-12 dataDisplayArea-body-right '+feildName+'-display">' +initDatum+
                        '  </div>' +
                        '</div>')
                }
                return $('<div></div>').append(content.append(head)).html();
            }




            /*点击票据信息*/
            $('body').on('click','.dataDisplayArea',function () {
                let imgId = $(this).data('img-id');
                $("#"+imgId).trigger('click');

                //暂存采集数据
                let dataDisplayArea = $('.selected-dataDisplayArea');

                //设置样式
                $(".selected-dataDisplayArea").removeClass('selected-dataDisplayArea');
                $(this).addClass('selected-dataDisplayArea');
                let receiptId = $(this).data('receipt-id');

                //清空所有显示的采集input框值 删除关联影像的tbody
                $(".inputClass").val("");

                $("#dataCollectArea").off("blur","input[name='billCollection']");
                if(oldInviceData.has(receiptId) || newInviceBufferData.has(receiptId)){
                    let buffer = undefined;
                    buffer = oldInviceData.get(receiptId);
                    if(buffer == undefined){
                        buffer = newInviceBufferData.get(receiptId);
                    }
                    for(let feild in collectField){
                        $("#"+feild).val(buffer[feild]);
                    }
                }else {
                    for(let feild in collectField){
                        if(collectField[feild]["blurValid"]=="isNumberAndDecimalPoint"){
                            $("#"+feild).val("0");
                        }
                    }
                }
                $("#dataCollectArea").on('blur',"input[name='billCollection']",check);
            });



            <#if imgTypeList?exists && (imgTypeList.keySet()?size>0)>
            <#list imgTypeList.keySet() as code>
            attachTypeMap.set(`${code}`,"1");
            </#list>
            </#if>


            /**
             * 计算两个日期之间的天数
             * @param dateString1  开始日期 yyyy-MM-dd
             * @param dateString2  结束日期 yyyy-MM-dd
             * @returns {number} 如果日期相同 返回一天 开始日期大于结束日期，返回0
             */
            function getDaysBetween(dateString1,dateString2){
                var  startDate = Date.parse(dateString1);
                var  endDate = Date.parse(dateString2);
                if (startDate>endDate){
                    return 0;
                }
                if (startDate==endDate){
                    return 1;
                }
                diffDate = Math.abs(endDate - startDate); // 取相差毫秒数的绝对值
                var days= Math.floor(diffDate/ (1000 * 3600 * 24));
                return  days;
            }


            /*点击lefttable》tr*/
            $('body').on('click', '.listodifyStyle', function () {
                if($(this).hasClass('imageListChoose')){
                    return;
                }
                let _this = $(this);
                //设置当前选中外的tr的btn禁用
                let trs = $(_this).parents('tbody').find("tr");
                //设置tr背景和img边框样式
                let attchId = $(_this).data('claim-order-attach-id'); //获取当前影像id
                $(".imageListChoose").removeClass('imageListChoose');
                $('#' + attchId).addClass('imageListChoose');
                viewer.showImageByFileId(attchId);
                $('#thumbnail-'+attchId).trigger('click');
            });






            /*点击缩略图*/
            $('body').on('click','.thumbnail-img',function () {
                if($(this).hasClass('selected-thumbnail-img')){
                    return;
                }
                let attrId = $(this).data('fileid');
                $('.selected-thumbnail-img').removeClass('selected-thumbnail-img');
                $(this).addClass('selected-thumbnail-img');
                $("#" + attrId).trigger('click');
                let imgNumber = $(this).data('img-number');
                let offset = imgNumber*95-(1050-95)/2;
                $('#thumbnail').scrollTop(offset);
            });

            /**
             * 循环设置元素样式
             * @param elements  同类名的元素数组
             * @param styles  样式class
             * @param Boolean  添加或移除
             * */
            function setStyle(elements,style,Boolean){
                if (elements.length <= 0){
                    return;
                }
                if(Boolean) {
                    elements.forEach(element => {
                        element.addClass(style);
                    });
                    return;
                }
                elements.forEach(element => {
                    element.removeClass(style);
                });
            }

            /**
             * 判断input元素的值是否为空
             * @param className  input标签的class名称
             * @return elements  input标签的父类元素ggLayer
             * */
            function inputIsEmpty(className) {
                let elements = [];
                $('.'+className).each(function (){
                    if (!$(this).val() || $(this).val() === ''){
                        elements.push($(this).closest('.ggLayer'));
                    }
                });
                return elements;
            }

            /**
             * 获取本身或父级元素
             * @param className  类名
             * @param Boolean  获取本身还是父级
             * @param parentClassName  父级类名
             * */
            function convertToArray(className,Boolean,parentClassName){
                let elements = [];
                if(Boolean) {
                    $('.' + className).each(function () {
                        elements.push($(this).closest('.'+parentClassName));
                    });
                }else {
                    $('.' + className).each(function () {
                        elements.push($(this));
                    });
                }
                return elements;
            }



            /*初始化时选中第一个影像列表、缩略图*/
            $("tr[data-img-number='1']").trigger('click');
            $("img[data-img-number='1']").trigger('click');

            /*A/D切换影像*/
            $('body').on('click', '.viewer-list > li', function () {
                let attchId = $(this).find('img').attr('alt');
                $('#' + attchId).trigger('click');
            });

            //遍历点击存在的标签
            claimCaseLabelList.forEach(value => {
                $("#"+value).click();
            });


            //案件标签初始化
            <#if firstEventType?exists>
            $("div[label='accidentTypeMap']").find("input[value='${firstEventType}']").parent().addClass("checked");
            </#if>

            <#if eventRespConfirm?exists>
            $("div[label='accidentDutyMap']").find("input[value='${eventRespConfirm}']").parent().addClass("checked");
            </#if>

            <#if accidentType?exists>
            $("div[label='accidentNatureMap']").find("input[value='${accidentType}']").parent().addClass("checked");
            </#if>

            <#if knightlllegalItems?exists >
            <#list knightlllegalItems.split(",") as code>
            $("div[label='accidentViolationTypeMap']").find("input[value='${code}']").parent().addClass("checked");
            </#list>
            </#if>

            <#if exceptionLabel?exists >
            <#list exceptionLabel.split(",") as code>
            $("div[label='claimVerifyExceptionLabelMap']").find("input[value='${code}']").parent().addClass("checked");
            </#list>
            </#if>

            <#if callPolice?exists >
            <#list callPolice.split(",") as code>
            $("div[label='claimVerifyCallPoliceMap']").find("input[value='${code}']").parent().addClass("checked");
            </#list>
            </#if>







        });

        // 根据产品id和分项名称来展示有边框所要字段
        function getCollectField(productId,subjectName) {
            switch (subjectName) {

                case "人伤门诊":
                    return {
                        // "receiptNo":{"labelName":"发票号","blurValid":"stringCheck","submitValid":{"isNull":"false"}},
                        // "receiptName":{"labelName":"发票姓名","blurValid":"stringCheck","submitValid":{"isNull":"false"}},
                        "hospitalName":{"labelName":"医院","blurValid":"stringCheck","submitValid":{"isNull":"false"}},
                        "treatmentDateStr":{"labelName":"就诊日期","blurValid":"isTime","submitValid":{"isNull":"false"}},
                        // "receiptAmount":{"labelName":"发票总金额","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false"}},
                        // "cashPay":{"labelName":"现金支付","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false","sumTo":"receiptAmount"}},
                        // "accountPay":{"labelName":"账户支付","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false","sumTo":"receiptAmount"}},
                        // "planAsPay":{"labelName":"统筹支付","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false","sumTo":"receiptAmount"}},
                        // "additionalPay":{"labelName":"附加支付","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false","sumTo":"receiptAmount"}},
                        // "classifiedConceit":{"labelName":"分类自负","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false"}},
                        // "selfFunded":{"labelName":"自费","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false"}},
                        "negotiateAmount":{"labelName":"协商金额","blurValid":"isNumberAndDecimalPointNotDefault","submitValid":{"isNull":"false"}}
                    };
                    break;
                case "三者人伤门诊":
                    return {
                        // "receiptNo":{"labelName":"发票号","blurValid":"stringCheck","submitValid":{"isNull":"false"}},
                        "receiptName":{"labelName":"发票姓名","blurValid":"stringCheck","submitValid":{"isNull":"false"}},
                        "hospitalName":{"labelName":"医院","blurValid":"stringCheck","submitValid":{"isNull":"false"}},
                        "treatmentDateStr":{"labelName":"就诊日期","blurValid":"isTime","submitValid":{"isNull":"false"}},
                        // "receiptAmount":{"labelName":"发票总金额","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false"}},
                        // "cashPay":{"labelName":"现金支付","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false","sumTo":"receiptAmount"}},
                        // "accountPay":{"labelName":"账户支付","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false","sumTo":"receiptAmount"}},
                        // "planAsPay":{"labelName":"统筹支付","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false","sumTo":"receiptAmount"}},
                        // "additionalPay":{"labelName":"附加支付","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false","sumTo":"receiptAmount"}},
                        // "classifiedConceit":{"labelName":"分类自负","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false"}},
                        // "selfFunded":{"labelName":"自费","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false"}},
                        "negotiateAmount":{"labelName":"协商金额","blurValid":"isNumberAndDecimalPointNotDefault","submitValid":{"isNull":"false"}}
                    };
                    break;
                case "人伤住院":
                    return {
                        // "receiptNo":{"labelName":"发票号","blurValid":"stringCheck","submitValid":{"isNull":"false"}},
                        // "receiptName":{"labelName":"发票姓名","blurValid":"stringCheck","submitValid":{"isNull":"false"}},
                        "hospitalName":{"labelName":"医院","blurValid":"stringCheck","submitValid":{"isNull":"false"}},
                        "treatmentDateStr":{"labelName":"就诊/入院日期","blurValid":"isTime","submitValid":{"isNull":"false"}},
                        "leaveHospitalDateStr":{"labelName":"出院日期","blurValid":"isTime","submitValid":{"maxE":"treatmentDateStr","isNull":"false"}},
                        // "receiptAmount":{"labelName":"发票总金额","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false"}},
                        // "cashPay":{"labelName":"现金支付","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false","sumTo":"receiptAmount"}},
                        // "accountPay":{"labelName":"账户支付","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false","sumTo":"receiptAmount"}},
                        // "planAsPay":{"labelName":"统筹支付","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false","sumTo":"receiptAmount"}},
                        // "additionalPay":{"labelName":"附加支付","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false","sumTo":"receiptAmount"}},
                        // "classifiedConceit":{"labelName":"分类自负","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false"}},
                        // "selfFunded":{"labelName":"自费","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false"}},
                        "negotiateAmount":{"labelName":"协商金额","blurValid":"isNumberAndDecimalPointNotDefault","submitValid":{"isNull":"false"}}
                    };
                    break;
                case "三者人伤住院":
                    return {
                        // "receiptNo":{"labelName":"发票号","blurValid":"stringCheck","submitValid":{"isNull":"false"}},
                        "receiptName":{"labelName":"发票姓名","blurValid":"stringCheck","submitValid":{"isNull":"false"}},
                        "hospitalName":{"labelName":"医院","blurValid":"stringCheck","submitValid":{"isNull":"false"}},
                        "treatmentDateStr":{"labelName":"就诊/入院日期","blurValid":"isTime","submitValid":{"isNull":"false"}},
                        "leaveHospitalDateStr":{"labelName":"出院日期","blurValid":"isTime","submitValid":{"maxE":"treatmentDateStr","isNull":"false"}},
                        // "receiptAmount":{"labelName":"发票总金额","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false"}},
                        // "cashPay":{"labelName":"现金支付","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false","sumTo":"receiptAmount"}},
                        // "accountPay":{"labelName":"账户支付","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false","sumTo":"receiptAmount"}},
                        // "planAsPay":{"labelName":"统筹支付","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false","sumTo":"receiptAmount"}},
                        // "additionalPay":{"labelName":"附加支付","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false","sumTo":"receiptAmount"}},
                        // "classifiedConceit":{"labelName":"分类自负","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false"}},
                        // "selfFunded":{"labelName":"自费","blurValid":"isNumberAndDecimalPoint","submitValid":{"isNull":"false"}},
                        "negotiateAmount":{"labelName":"协商金额","blurValid":"isNumberAndDecimalPointNotDefault","submitValid":{"isNull":"false"}}
                    };
                    break;
                case "人伤误工费":
                    return {
                        "missedWorkDays":{"labelName":"误工天数","blurValid":"isDigits","submitValid":{"isNull":"false"}},
                        "negotiateAmount":{"labelName":"协商金额","blurValid":"isNumberAndDecimalPointNotDefault","submitValid":{"isNull":"true"}}
                    };
                    break;
                case "三者误工费":
                    return {
                        "receiptName":{"labelName":"发票姓名","blurValid":"stringCheck","submitValid":{"isNull":"false"}},
                        "missedWorkDays":{"labelName":"误工天数","blurValid":"isDigits","submitValid":{"isNull":"false"}},
                        "negotiateAmount":{"labelName":"协商金额","blurValid":"isNumberAndDecimalPointNotDefault","submitValid":{"isNull":"true"}}
                    };
                    break;
                case "人伤伤残":
                    return {
                        "disabilityLevel":{"labelName":"伤残等级","blurValid":"isDisabilityLevel","submitValid":{"isNull":"false"}},
                        "disabilityRecogTimeStr":{"labelName":"伤残认定时间","blurValid":"isTime","submitValid":{"isNull":"false"}},
                        "negotiateAmount":{"labelName":"协商金额","blurValid":"isNumberAndDecimalPointNotDefault","submitValid":{"isNull":"true"}}
                    };
                    break;
                case "三者人伤伤残":
                    return {
                        "receiptName":{"labelName":"发票姓名","blurValid":"stringCheck","submitValid":{"isNull":"false"}},
                        "disabilityLevel":{"labelName":"伤残等级","blurValid":"isDisabilityLevel","submitValid":{"isNull":"false"}},
                        "disabilityRecogTimeStr":{"labelName":"伤残认定时间","blurValid":"isTime","submitValid":{"isNull":"false"}},
                        "negotiateAmount":{"labelName":"协商金额","blurValid":"isNumberAndDecimalPointNotDefault","submitValid":{"isNull":"true"}}
                    };
                    break;
                case "人伤死亡":
                    return {
                        "deadSurplusQuota":{"labelName":"剩余保额","isDisabled":"1","blurValid":"isDeadSurplusQuota","submitValid":{"isNull":"true"}},
                        "negotiateAmount":{"labelName":"协商金额","blurValid":"isNumberAndDecimalPointNotDefault","submitValid":{"isNull":"true"}}
                    };
                case "三者人伤死亡":
                    return {
                        "deadSurplusQuota":{"labelName":"剩余保额","isDisabled":"1","blurValid":"isDeadSurplusQuota","submitValid":{"isNull":"true"}},
                        "receiptName":{"labelName":"姓名","blurValid":"stringCheck","submitValid":{"isNull":"false"}},
                        "negotiateAmount":{"labelName":"协商金额","blurValid":"isNumberAndDecimalPointNotDefault","submitValid":{"isNull":"true"}}
                    };
                    break;
                case "物损其他":
                case "物损机动车":
                case "物损非机动车":
                case "物损衣物":
                    //补充detail字段，物品名称、品牌名称
                    return {
                        "receiptName":{"labelName":"发票姓名","blurValid":"stringCheck","submitValid":{"isNull":"false"}},
                        "itemName":{"labelName":"物品名称","blurValid":"stringCheck","submitValid":{"isNull":"false"}},
                        "brandName":{"labelName":"物品品牌名称","blurValid":"stringCheck","submitValid":{"isNull":"false"}},
                        "negotiateAmount":{"labelName":"协商金额","blurValid":"isNumberAndDecimalPointNotDefault","submitValid":{"isNull":"false"}}
                    };
                    break;
                case "人伤护理费":
                    return {
                        "nursingDays":{"labelName":"护理天数","blurValid":"isDigits","submitValid":{"isNull":"false"}},
                        "negotiateAmount":{"labelName":"协商金额","blurValid":"isNumberAndDecimalPointNotDefault","submitValid":{"isNull":"true"}}
                    };
                    break;
                case "三者护理费":
                    return {
                        "receiptName":{"labelName":"姓名","blurValid":"stringCheck","submitValid":{"isNull":"false"}},
                        "nursingDays":{"labelName":"护理天数","blurValid":"isDigits","submitValid":{"isNull":"true"}},
                        "negotiateAmount":{"labelName":"协商金额","blurValid":"isNumberAndDecimalPointNotDefault","submitValid":{"isNull":"false"}}
                    };
                    break;
                case "人伤营养费":
                    return {
                        "usedAllowDays":{"labelName":"营养天数","blurValid":"isDigits","submitValid":{"isNull":"false"}},
                        "negotiateAmount":{"labelName":"协商金额","blurValid":"isNumberAndDecimalPointNotDefault","submitValid":{"isNull":"true"}}
                    };
                    break;
                case "三者营养费":
                    return {
                        "receiptName":{"labelName":"姓名","blurValid":"stringCheck","submitValid":{"isNull":"false"}},
                        "negotiateAmount":{"labelName":"协商金额","blurValid":"isNumberAndDecimalPointNotDefault","submitValid":{"isNull":"false"}}
                    };
                case "人伤狂犬费":
                    return {
                        "negotiateAmount":{"labelName":"协商金额","blurValid":"isNumberAndDecimalPointNotDefault","submitValid":{"isNull":"false"}}
                    };
                    break;
            }

        }


        /*map转obj*/
        function _strMapToObj(strMap){
            let obj= Object.create(null);
            for (let[k,v] of strMap) {
                obj[k] = v;
            }
            return obj;
        }

        /*map转换为json*/
        function _mapToJson(map) {
            return JSON.stringify(this._strMapToObj(map));
        }

        /*校验对象中属性是否有空值*/
        function ifObjIsNull(obj){
            // 判断一个对象下是否有空属性
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    if (obj[key] === null || obj[key] === '') {
                        return "你还有值未填请完善数据";
                    }
                }
            }
            return "";
        }

        /*添加input校验 给append增加的元素使用*/
        function inputBlur(b){
            let val = $(b).val();
            let valid = b.getAttribute('data-valid');
            if (!checkService[valid](val)) {
                $(b).val("");
                layer.msg("输入格式不对", {icon: 5, time: 1000});
                $(b).focus();
                return;
            }
        }


        /*展示选择的类型影像列表*/
        function gradeChange(){
            let grade = $("#typeSelect option:selected").val();
            $("#leftTable_tbody").find("tr").each(function () {
                let trId = $(this).attr("id");
                let val = $(this).find('td:eq(1)').find("select").val();
                if("全部类型" === grade || grade === val){
                    $(this).show();
                    $(".viewer-"+trId).show();
                }else{
                    $(this).hide();
                    $(".viewer-"+trId).hide();
                }
            });
        }

        /*影像类型改变*/
        function imageTypeChange(t){
            // 获取当前tr中的select框的值 与初始化tr的data-img-code比较 看值是否有变化 有变化则在 imageTypeChangeList 添加一条记录
            let beforeCode = $(t).data("before-code");
            let afterCode = $(t).val();
            let imgId = $(t).data("img-id");
            let serialNumber = $(t).data("serialnumber");
            /*比较改变后和改变前的影像类型是否一致*/
            if(afterCode !== beforeCode){
                let claimOrderAttach = {
                    imgId:imgId,//影像id
                    code:afterCode//影像code
                }
                imageTypeChangeMap.set(imgId,claimOrderAttach);
            }else {
                imageTypeChangeMap.delete(imgId);
            }

            /*是否展示采集按钮*/
            if (collectedImgTypes.includes($(t).val())) {
                $(t).parents('tr').find("button").show();
            }else {
                $(t).parents('tr').find("button").hide();
            }
            if($('.selected-dataDisplayArea').length > 0 ){
                $('.selected-dataDisplayArea').trigger('click');
            }
        }

        //生成uuid
        function guid() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                var r = Math.random() * 16 | 0,
                    v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        function seePlanName(planId) {
            if (typeof planId == 'undefined' || planId == '') {
                layer.msg('暂未数据！！！', {
                    icon: 2,
                    time: 2000
                });
                return;
            }

            var formData = new FormData();
            formData.append("planId", planId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/seePlanNameByPlanId",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        window.open(result.msg, "_blank");
                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

        }

        function seeCaseProcessReason(claimCaseId) {
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '查看案件流转',
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/getCaseProcessReason?claimCaseId=" + claimCaseId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        function getHistroyCaseInfo(baseUserId) {
            if (baseUserId == '') {
                layer.msg("暂无信息！！！",{icon: 2,time: 3000});
                return;
            }
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '查看历史案件',
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/getHistroyCaseInfo?baseUserId=" + baseUserId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }
    </script>
</head>


<body id="qc-Body" style="overflow-x:hidden">
<div class="container-fluid">
    <div class="row">
        <form id="inputForm" onsubmit="return false">
            <input id="claimCaseSubjectId" type="hidden" value="${claimCaseSubject.id}"/>
            <!-- 左部分 -->
            <div class="col-sm-10" style="background: none!important;padding-right: 0px !important;">
                <#--审核头-->
                <div class="row">
                    <#-- 按钮 -->
                    <div class="borderbottom col-sm-12" style="padding-left: 0px;padding-right: 0px;">

                        <div class="col-sm-2" style="padding-left: 0px;">
                            <div class="col-sm-12" style="padding-left: 0px;">
                                <button type="button" class="btn genTask btn-look" onclick="seePlanName('${product.id!''}')">
                                    查看产品方案
                                </button>
                            </div>
                        </div>

                        <div class="col-sm-2" style="padding-left: 0px;">
                            <button type="button" onclick="getHistroyCaseInfo('${claimCase.baseUserId}')"  class="btn genTask" >
                                历史案件
                            </button>
                        </div>

                        <div class="col-sm-2">
                            <#if haveTask=="yes">
                                <button type="button" class="btn genTask circulation_reasons" onclick="seeCaseProcessReason('${claimCase.id}')"> 查看案件流转原因</button>
                            </#if>
                        </div>

                        <div class="col-sm-6" style="padding-right: 0px;">

                        </div>

                    </div>
                    <#-- 订单信息和影像信息table -->
                    <div class="col-sm-2" style="background: none!important; padding-left: 0px;padding-top: 5px;">
                        <!-- 订单信息 -->
                        <div class="row" style="height: 40%">
                            <img src="${ctx}/images/u48.svg" align="left" style="margin-top: 3px;"/>
                            <span style="padding-left: 10px;font-size: 18px">&nbsp;&nbsp;订单信息</span>
                            <div class="applyScrollBar">
                                <div class="borderLeft row">
                                    <div class="col-sm-12 borderText">案件号：<br/>${claimCase.claimCaseNo!''}</div>
                                    <div class="col-sm-12 borderText">饿了么第三方案件号：<br/>${claimCase.thirdPartCaseNo!''}</div>
                                    <div class="col-sm-12 borderText">出险人：<br/>${claimCase.treatName!''}-${claimCase.treatIdNum!''}</div>
                                    <div class="col-sm-12 borderText">投保方案：<br/>${claimCase.productName!''}</div>
                                    <div class="col-sm-12 borderText">个人保单号：<br/>${claimCase.customerPolicyNo!''}</div>
                                    <div class="col-sm-12 borderText">保期：<br/>${(policyPerson.startDate?string("yyyy-MM-dd"))!''}至${(policyPerson.endDate?string("yyyy-MM-dd"))!''}</div>
                                </div>
                            </div>
                        </div>
                        <!-- 影像信息列表 -->
                        <div class="row" style="height:676px;<#if claimAttachList?exists && (claimAttachList?size gt 10)>overflow-y:scroll</#if>">
                            <div style="margin-top: 10px">
                                <img src="${ctx}/images/u48.svg" align="left" style="margin-top: 3px;"/>
                                <span style="padding-left: 10px;font-size: 18px">&nbsp;&nbsp;影像列表</span>
                                <div class="tableHistory">
                                    <table class="table table-bordered" id="leftTable">
                                        <thead>
                                        <tr style="background-color: #3399ff">
                                            <th class="tableText thClass" width="30%">序号(${(claimAttachList.size())!'-'})</th>
                                            <th class="tableText thClass" width="40%" style="cursor: pointer">
                                                <select class="typeSelect" id="typeSelect" onchange="gradeChange()">
                                                    <option value="全部类型">全部类型</option>
                                                    <#if imgTypeList?exists && (imgTypeList.keySet()?size>0)>
                                                        <#list imgTypeList.keySet() as code>
                                                            <option value="${code}">
                                                                ${imgTypeList.get(code)}
                                                            </option>
                                                        </#list>
                                                    </#if>

                                                </select>
                                            </th>
                                            <th class="tableText thClass" width="30%">功能</th>
                                        </tr>
                                        </thead>
                                        <tbody id="leftTable_tbody">
                                        <#if claimAttachList?exists && (claimAttachList?size gt 0)>
                                            <#list claimAttachList as claimCaseAttach>
                                                <tr id="${claimCaseAttach.id!'-'}" class="claimOrderAttachList listodifyStyle collectionCompleted"
                                                    data-claim-order-attach-id='${claimCaseAttach.id!'-'}' data-img-code='${claimCaseAttach.contentType}'
                                                    data-img-number='' data-img-type='${claimCaseAttach.baiduContentTypeStr}'
                                                    data-img-serialnumber='${claimCaseAttach.remark}'>
                                                    <td id="${claimCaseAttach.id!'-'}td" class="tableText" class="imgList" >${claimCaseAttach.remark}</td>
                                                    <td class="tableText">
                                                        <select style="border-color: #FFFFFF;"disabled="disabled" data-img-id="${claimCaseAttach.id!'-'}"
                                                                id="${claimCaseAttach.id!'-'}select" data-before-code='${claimCaseAttach.contentType}'
                                                                data-serialnumber='${claimCaseAttach.remark}' >
                                                            <#if imgTypeList?exists && (imgTypeList.keySet()?size>0)>
                                                                <#list imgTypeList.keySet() as code>
                                                                    <option value='${code}' <#if '${claimCaseAttach.contentType}' == '${code}'>selected</#if>>
                                                                        ${imgTypeList.get(code)}
                                                                    </option>
                                                                </#list>
                                                            </#if>
                                                        </select>
                                                    </td>
                                                    <td></td>
                                                </tr>
                                            </#list>
                                        <#else>
                                            <tr>
                                                <td colspan="3" class="text-danger text-center"> 暂无数据</td>
                                            </tr>
                                        </#if>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <#-- 展示图片区域以及采集数据tbale -->
                    <div class="col-sm-9" style="padding: 5px 0px 0px 0px; background: none;">
                        <div class="row">
                            <div class="col-sm-6" style="background: none;">
                                <span class="txt-over-hide" title="" id="labelRemark"></span>
                                <#if dataCollectionAuidTaskVo.hangReason ??>
                                    <span class="txt-over-hide" title="${((dataCollectionAuidTaskVo.hangReason)!'')?html}">挂起原因：${((dataCollectionAuidTaskVo.hangReason)!'')?html}</span>
                                </#if>
                            </div>
                            <div class="col-sm-4" style="background: none; font-size: 12px;">
                                <span style="float: right;font-size: 16px;!important">该订单采集影像数：<span id="receiptIndex" style="color: #1676FF">${(claimCaseSubjectDetailListJsonArray?size)!'0'}</span> /${(claimAttachList.size())!'0'}</span>
                            </div>
                        </div>
                        <div class="row" style="height: 1050px; overflow: hidden;">
                            <div class="col-sm-1" style="height: 1050px;background-color: rgba(226, 226, 226, 0.5);padding-left: 0px!important;padding-right: 10px!important; border-right: groove;">
                                <#if claimAttachList?exists && (claimAttachList?size>0)>
                                    <div id="thumbnail" style="height: 1050px;<#if (claimAttachList?size>11)>overflow-y: scroll;</#if>overflow-x:hidden">
                                        <ol style="padding-left:25px !important;/*padding-right: 15px !important;*/">
                                            <#list claimAttachList as attach>
                                                <li style="width:auto; height:auto; cursor:pointer;text-align: center;padding-top:5px">
                                                    <img id="thumbnail-${attach.id}" class="thumbnail-img"
                                                         data-fileid="${attach.id}" data-img-number=''
                                                         title="${attach.fileName}"
                                                         src="${attach.fileObjectId}" onerror="javascript:this.src='/a/job_done.png'"/>
                                                </li>
                                            </#list>
                                        </ol>
                                    </div>
                                </#if>
                            </div>
                            <div class="col-sm-11" style="height: 1300px;">
                                <ul id="images">
                                    <#list claimAttachList as attach>
                                        <li hidden="hidden">
                                            <img data-fileid="${attach.id}"
                                                 title="${attach.fileName}"
                                                 src="${attach.fileObjectId}"
                                                 alt="${attach.id}"/>
                                        </li>
                                    </#list>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-1" style="padding: 5px 0px 0px 15px">
                        <img src="${ctx}/images/u48.svg" align="left" style="margin-top: 3px;"/>
                        <span style="padding-left: 10px;font-size: 18px;">&nbsp;&nbsp;票据信息</span>
                        <div class="row ticket-information">

                        </div>
                    </div>
                </div>
            </div>
            <!-- 右部分 -->
            <div class="col-sm-2" style="background: none!important;padding-left: 0px;">
                <div class="row" style="height:350px;overflow-y: scroll">
                    <img class="marginLeft15" src="${ctx}/images/u48.svg" align="left" style="margin-top: 3px;"/>
                    <div style="margin-left: 5px;margin-bottom: 15px">
                        <span style="font-size: 16px">&nbsp;&nbsp;案件标签</span> <span style="font-size: 18px"></span>
                    </div>

                    <div style="margin-left: 10px" class="row" label="accidentTypeMap">
                        <div>事故类型：</div>
                        <#if accidentTypeMap?exists>
                            <#list accidentTypeMap.keySet() as object>
                                <div class="col-sm-4" style="margin-bottom: 0px;padding: 0px 0px 5px 0px;display: flex">
                                    <input  type="radio" name="accidentTypLabel" value="${accidentTypeMap.get(object).code}" style="cursor: pointer">${accidentTypeMap.get(object)}
                                </div>
                            </#list>
                        </#if>
                    </div>

                    <div style="margin-left: 10px" class="row" label="accidentDutyMap">
                        <div>事故责任：</div>
                        <#if accidentDutyMap?exists>
                            <#list accidentDutyMap.keySet() as object>
                                <div class="col-sm-4" style="margin-bottom: 0px;padding: 0px 0px 5px 0px;display: flex">
                                    <input type="radio" name="accidentDutyLabel"
                                           value="${accidentDutyMap.get(object).code}" style="cursor: pointer">${accidentDutyMap.get(object)}
                                </div>
                            </#list>
                        </#if>
                    </div>

                    <div style="margin-left: 10px" class="row" label="accidentNatureMap">
                        <div>事故性质：</div>
                        <#if accidentNatureMap?exists>
                            <#list accidentNatureMap.keySet() as object>
                                <div class="col-sm-4" style="margin-bottom: 0px;padding: 0px 0px 5px 0px;display: flex">
                                    <input type="radio" name="accidentNatureLabel" value="${accidentNatureMap.get(object).code}" style="cursor: pointer">${accidentNatureMap.get(object)}
                                </div>
                            </#list>
                        </#if>
                    </div>

                    <div style="margin-left: 10px" class="row" label="accidentViolationTypeMap">
                        <div>事故违规类型：</div>
                        <#if accidentViolationTypeMap?exists>
                            <#list accidentViolationTypeMap.keySet() as object>
                                <div class="col-sm-4" style="margin-bottom: 0px;padding: 0px 0px 5px 0px;display: flex">
                                    <input type="checkbox" name="accidentViolationTypeLabel" value="${accidentViolationTypeMap.get(object).code}" style="cursor: pointer">${accidentViolationTypeMap.get(object)}
                                </div>
                            </#list>
                        </#if>
                    </div>

                    <div style="margin-left: 10px" class="row" label="claimVerifyExceptionLabelMap">
                        <div>异常案件标签：</div>
                        <#if claimVerifyExceptionLabelMap?exists>
                            <#list claimVerifyExceptionLabelMap.keySet() as object>
                                <div class="col-sm-4" style="margin-bottom: 0px;padding: 0px 0px 5px 0px;display: flex">
                                    <input type="checkbox" name="claimVerifyExceptionLabel" value="${claimVerifyExceptionLabelMap.get(object).code}" style="cursor: pointer">${claimVerifyExceptionLabelMap.get(object)}
                                </div>
                            </#list>
                        </#if>
                    </div>

                    <div style="margin-left: 10px" class="row" label="claimVerifyCallPoliceMap">
                        <div>是否已报警：</div>
                        <#if claimVerifyCallPoliceMap?exists>
                            <#list claimVerifyCallPoliceMap.keySet() as object>
                                <div class="col-sm-4" style="margin-bottom: 0px;padding: 0px 0px 5px 0px;">
                                    <input type="radio" name="claimVerifyCallPolice"
                                           value="${claimVerifyCallPoliceMap.get(object).code}" style="cursor: pointer">${claimVerifyCallPoliceMap.get(object)}
                                </div>
                            </#list>
                        </#if>
                    </div>
                </div>
                <div class="row" style="height: 990px;overflow-y:scroll">
                    <img class="marginLeft15" src="${ctx}/images/u48.svg" align="left" style="margin-top: 3px;"/>
                    <div style="margin-left: 5px;margin-bottom: 15px">
                        <span style="font-size: 16px">&nbsp;&nbsp;票据采集 </span> <span style="font-size: 18px"></span>
                    </div>
                    <div id="dataCollectArea" class="row" style="padding-left: 10px">

                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

</body>
</html>
