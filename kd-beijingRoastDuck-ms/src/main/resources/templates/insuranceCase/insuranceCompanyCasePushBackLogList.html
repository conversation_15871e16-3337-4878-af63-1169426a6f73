<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            calculationScrollTop();

            $("table").on("click", "td:not(:last-child)", function(e) {
                var $input = $("<input>");
                $("body").append($input);
                $input.val($(this).html().trim()).select();
                document.execCommand("copy");
                $input.remove();
                layer.tips("复制成功", $(this), {icon: 1, time: 500, tips: 1});
            });

            $('.js-example-basic-multiple').select2();

            //导出按钮监控器
            $("#searchForm").on("click", ".exportBtn", function (e) {
                e.stopPropagation();
                e.preventDefault();
                var _this = this;
                $(_this).removeClass("exportBtn");
                //提交之后再把class加回来 防止重复提交
                postForm("${ctx}/downloadCenterController/downloadPiccBackCase");
                $(_this).addClass("exportBtn");
            });

            var postForm = function (url) {
                // 创建表单
                var formElement = document.createElement("form");
                formElement.action = url;
                formElement.method = "post";
                // 打开新标签
                formElement.target = '_blank';
                formElement.style.display = "none";
                formElement.appendChild(createInput("claimCaseNo", $.trim($("#claimCaseNo").val())));
                formElement.appendChild(createInput("applyName", $.trim($("#applyName").val())));
                formElement.appendChild(createInput("treatName", $.trim($("#treatName").val())));
                formElement.appendChild(createInput("status", $.trim($("#status").val())));
                formElement.appendChild(createInput("insCode", $.trim($("#insCode").val())));

                document.body.appendChild(formElement);
                formElement.submit();
                formElement.remove();
                return;
            }

            var createInput = function (name, value) {
                var inputElement = document.createElement("input");
                inputElement.type = "hidden";
                inputElement.name = name;
                if (value != null) {
                    inputElement.value = value;
                }
                return inputElement;
            }
        });




        var scrollTop;  // 定义滚动高度

        function page(n,s){
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        function caseDetail(claimCaseId) {
            window.location.href="${ctx}/claimCaseController/caseDetail?caseId="+claimCaseId;
        }

        // 影像件推送
        function piccCaseImg(claimCaseId) {
            layer.confirm("是否确认影像件补推？", {icon: 3, title: '温馨提示', offset: scrollTop}, function (index) {
                $.ajax({
                    url: "${ctx}/claimCaseController/piccCaseImg?claimCaseId=" + claimCaseId,
                    type: 'POST',
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            layer.msg(result.msg, {icon: 1, time: 2000, offset: scrollTop, shade: [0.1, '#000']}, function (index) {
                                window.location.reload();
                            });
                        } else {
                            layer.msg(result.msg, {icon: 2, time: 2000, offset: scrollTop, shade: [0.1, '#000']}, function (index) {
                                window.location.reload();
                            });
                        }
                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                });
            });
        }

        // 案件导入推送
        function piccCaseRePush(claimCaseId) {
            layer.confirm("是否确认案件导入推送？", {icon: 3, title: '温馨提示', offset: scrollTop}, function (index) {
                $.ajax({
                    url: "${ctx}/claimCaseController/piccCaseRePush?claimCaseId=" + claimCaseId,
                    type: 'POST',
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            layer.msg(result.msg, {icon: 1, time: 2000, offset: scrollTop, shade: [0.1, '#000']}, function (index) {
                                window.location.reload();
                            });
                        } else {
                            layer.msg(result.msg, {icon: 2, time: 2000, offset: scrollTop, shade: [0.1, '#000']}, function (index) {
                                window.location.reload();
                            });
                        }
                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                });
            });
        }


        // 计算滚动高度
        function calculationScrollTop() {
            var ifm = parent.document.getElementById("contentFrame");
            scrollTop = $(window.parent).scrollTop() + 150;
            if (!ifm) {
                scrollTop = 300;
            }
            return scrollTop + "px";
        }


    </script>
    <style>
        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
        }

        .form-active > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
        }

        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 22px;
            font-size: 16px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
        }

        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }

        .li-default:nth-of-type(2) {
            border-left: 1px solid #e7ecf1;
            border-right: 1px solid #e7ecf1;
        }

        .li-default:nth-of-type(3) {
            border-right: 1px solid #e7ecf1;
        }

        .li-blue {
            background: #0b94ea;
            color: #fff;
        }

        .labelGroup span{
            margin-left: 5px;
            margin-right: 5px;
            background-color: #1676FF;
            color: white;
            border-radius: 2px;
        }

        .applyTypeGroup span{
            margin-left: 5px;
            margin-right: 5px;
            background-color: #c9c8c8;
            border-radius: 2px;
        }
        .applyTypeGroup .AA001,.AA002,.AA003,.AA004{
            background-color: #1676ff !important;
            color: white;
        }
        .applyTypeGroup .AB001,.AB002,.AB003,.AB004{
            background-color: #ff6633 !important;
            color: white;
        }
        .applyTypeGroup .AC001,.AC002,.AC003,.AC004{
            background-color: #CA0000 !important;
            color: white;
        }

        .td-overflow {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 200px;
        }

        td:hover {
            cursor: pointer;
        }

        td > a , td > span {
            display: inline-block;
            margin: 3px;
        }

        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }

        .logListInfo {
            margin-bottom: 2%;
        }

        .logListInfo table {
            border: 1px solid #C2C2C2;
        }

        .logListInfo .detailsInfo {
            display: none;
            padding-left: 8%;
        }

        .minus {
            border: 1px solid black;
            border-radius: 50%;
            width: 13px;
            height: 13px;
            position: relative;
            margin-top: 3%;
        }

        .minus::before {
            content: '';
            position: absolute;
            left: 2px;
            top: 5px;
            width: 7px;
            border-top: 1px solid;
        }

        .logListInfo .detailsInfo > td {
            word-wrap:break-word;
            word-break:break-all;
            text-align: left;
        }
        .logListInfo .rowInfo:hover{
            cursor: pointer;
        }

        .label-title {
            font-size: 18px !important;
        }

        .table-scrollable {
            width: 100%;
            overflow-x: auto;
            overflow-y: hidden;
            border: 1px solid #e7ecf1;
            margin: 10px 0!important;
            table-layout: fixed;
        }
    </style>
</head>
<body>

<div id="payTable" style="display: none">
    <div class="block-show">
        <div class="form-group" style="margin-top: 20px;display: flex;align-items: center;">
            <label class="control-label col-sm-2 " style="padding-right: 0;text-align: right;">支付时间：</label>
            <div class="col-sm-8">
                <input type="text" class="form-control" name="paytime" id="paytime" autocomplete="off" placeholder="yyyy-MM-dd HH:mm:ss">
            </div>
        </div>
        <div class="form-group" style="margin-top: 20px;display: flex;align-items: center;">
            <label class="control-label col-sm-2 " style="padding-right: 0;text-align: right;">支付金额：</label>
            <div class="col-sm-8">
                <input type="text" class="form-control" name="payAmount" id="payAmount" autocomplete="off" placeholder="$.$$">
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>饿了么</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">保司驳回列表</span></li>
                </ul>
            </div>
            <div class="portlet-body">

                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal margin-bottom-15" action="${ctx}/insuranceCaseController/insuranceCompanyCasePushBackLogList"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">平台案件号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="claimCaseNo" id="claimCaseNo"
                                               value="${insuranceBackVo.claimCaseNo}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案人姓名：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="applyName" id="applyName"
                                               value="${insuranceBackVo.applyName}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人姓名：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatName" id="treatName"
                                               value="${insuranceBackVo.treatName}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">状态：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">

                                        <select class="form-control" name="status" id="status">
                                            <option value="">-请选择-</option>
                                            <#if piccBackStatusMap?? && (piccBackStatusMap.keySet()?size>0) >
                                                <#list piccBackStatusMap.keySet() as key>
                                                    <option value="${key}" <#if key == insuranceBackVo.status> selected </#if> >${piccBackStatusMap.get(key)}</option>
                                                </#list>
                                            </#if>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">保司标识：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">

                                        <select class="form-control" name="insCode" id="insCode">
                                            <option value="">-请选择-</option>
                                            <option value="RB" <#if insuranceBackVo.insCode == "RB">selected</#if>>人保</option>
                                            <option value="HMRB" <#if insuranceBackVo.insCode == "HMRB">selected</#if>>盒马人保</option>
                                        </select>
                                        </select>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group pull-right" style="margin: 0px 15px;">
                                    <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;">查询
                                    </button>
                                    <div class="btn-group pull-right" style="margin: 0px 15px;" >
                                        <button type="button" class="btn blue exportBtn" style="margin-bottom: 10px;">导出
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th width="10%">平台案件号</th>
                        <th width="7%">报案人姓名</th>
                        <th width="7%">出险人姓名</th>
                        <th width="7%">案件状态</th>
                        <th width="15%">驳回原因</th>
                        <th width="15%">驳回数据</th>
                        <th width="10%">驳回时间</th>
                        <th width="8%">操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as vo>
                        <tr>
                            <td title="${vo.claimCaseNo}">${vo.claimCaseNo}</td>
                            <td title="${vo.applyName}">${vo.applyName}</td>
                            <td title="${vo.treatName}">${vo.treatName}</td>
                            <td class="td-overflow" title="${claimCaseStatusMap.get(vo.status)!'--'}">${claimCaseStatusMap.get(vo.status)!'--'}</td>
                            <td class="td-overflow" title="${vo.remark!'--'}">${vo.remark!'--'}</td>
                            <td class="td-overflow" title="${vo.exInfo?html}">${vo.exInfo?html}</td>
                            <td title="${(vo.modifyTime?string('yyyy-MM-dd HH:mm:ss'))!''}">${(vo.modifyTime?string('yyyy-MM-dd HH:mm:ss'))!''} </td>
                            <#--功能-->
                            <td>
                                <a onclick="caseDetail('${vo.id}')">查看详情</a>
                                <#if vo.status == "abx15" || vo.status == "acx12" || vo.status == "acx13">
                                    <a onclick="piccCaseImg('${vo.id}')">影像补推</a>
                                    <a onclick="piccCaseRePush('${vo.id}')">案件重推</a>
                                </#if>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>