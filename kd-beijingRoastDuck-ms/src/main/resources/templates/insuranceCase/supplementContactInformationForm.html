<!--[if IE 8]>
<html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]>
<html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en" xmlns="http://www.w3.org/1999/html">
<!--<![endif]-->
<head>
    <meta charset="utf-8"/>
    <title>银行信息详情</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">

    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script src="${ctx}/a/idCardCheck.js" type="text/javascript"></script>
    <script type="text/javascript">
        $(function () {
            //校验
            var form = $('#inputForm');
            form.validate({
                ignore: "hidden",
                errorElement: 'span', //default input error message container
                errorClass: 'help-block help-block-error hidden_span font-span', // default input error message class
                focusInvalid: false, // do not focus the last invalid input
                rules: {
                    bankName: {
                        required: true
                    },
                    bankCard: {
                        required: true
                    },
                    bankAccount: {
                        required: true
                    },
                    bankInfoId: {
                        required: true
                    }
                },
                invalidHandler: function (event, validator) { //display error alert on form submit
                    App.scrollTo(form, 0);
                },

                highlight: function (element) { // hightlight error inputs
                    $(element).closest('.form-group').removeClass(
                        "has-success").addClass('has-error'); // set error class to the control group
                },

                unhighlight: function (element) { // revert the change done by hightlight

                },

                success: function (label, element) {
                    var icon = $(element).parent('.input-icon').children(
                        'i');
                    $(element).closest('.form-group').removeClass(
                        'has-error').addClass('has-success'); // set success class to the control group
                    icon.removeClass("fa-warning").addClass("fa-check");
                },

                submitHandler: function (form) {
                    $("#confirmBtn").prop('disabled', true);
                    var formData = new FormData($('#inputForm')[0]);
                    $.ajax({
                        url: "${ctx}/insuranceCaseController/updateBankcardLocation",
                        type: 'POST',
                        data: formData,
                        async: true,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            $("#confirmBtn").prop('disabled', false);
                            var result = eval("(" + data + ")");
                            if (result.ret === "-1") {
                                layer.msg(result.msg,
                                    {
                                        time: 2000
                                    });
                                return false;
                            }
                            parent.window.location.reload();
                            var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
                            parent.layer.close(index);
                        },
                        error: function (data) {
                            $("#confirmBtn").prop('disabled', false);
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    });
                }
            });

            //查询总行信息
            $("#totalBankSelect").select2({
                ajax: {
                    type: 'POST',
                    url: "${ctx}/insuranceCaseController/getTotalBankInfo",
                    dataType: 'json',
                    delay: 500,
                    contentType: "application/x-www-form-urlencoded; charset=utf-8",
                    data: function (params) {
                        return {
                            paramMsg: params.term
                        };
                    },
                    processResults: function (data) {
                        return {
                            results: data.bankList,
                        };
                    },
                },
                escapeMarkup: function (markup) {
                    return markup;
                },
                width: '100%',
                placeholder: "请输入总行名称查询...",
                allowClear: true,
                minimumInputLength: 2,
                minimumResultsForSearch: Infinity, // 隐藏搜索框
                templateResult: function (data) {
                    if (data.id != undefined) {
                        return "<div class='select2-result-repository__title'>" + data.bankName + "</div>";
                    }
                    return "";
                },
                templateSelection: function (data) {
                    if (data.id != "") {
                        $("#bankName").val(data.bankName);
                        $("#totalBankId").val(data.id);
                        $('#bankSubbranchSelect').prop('disabled', false);
                        return "<div class='select2-result-repository__title'>" + data.bankName + "</div>";
                    }
                    return "请输入总行名称查询...";
                }
            });

            var bankSubbranchInitName = "请输入支行名称查询...";
            <#if bankInfo??>
                bankSubbranchInitName = "${bankInfo.bankName}";
            </#if>

            $("#bankSubbranchSelect").select2({
                ajax: {
                    type: 'POST',
                    url: "${ctx}/insuranceCaseController/getBankInfo",
                    dataType: 'json',
                    delay: 500,
                    contentType: "application/x-www-form-urlencoded; charset=utf-8",
                    data: function (params) {
                        return {
                            totalBankId: $('#totalBankId').val(),
                            paramMsg: params.term
                        };
                    },
                    processResults: function (data) {
                        return {
                            results: data.bankList,
                        };
                    },
                },
                escapeMarkup: function (markup) {
                    return markup;
                },
                width: '100%',
                placeholder: "请输入支行名称查询...",
                allowClear: true,
                minimumInputLength: 2,
                minimumResultsForSearch: Infinity, // 隐藏搜索框
                templateResult: function (data) {
                    if (data.id != undefined) {
                        return "<div class='select2-result-repository__title'>" + data.bankName+"("+data.provinceName+"-"+data.cityName+")" + "</div>";
                    }
                    return "";
                },
                templateSelection: function (data) {
                    if (data.id != "") {
                        $("#bankSubName").val(data.bankName);
                        $("#bankInfoId").val(data.id);
                        return "<div class='select2-result-repository__title'>" + data.bankName+"("+data.provinceName+"-"+data.cityName+")"  + "</div>";
                    }

                    return bankSubbranchInitName;
                }
            });

        });

        //取消
        function doCancel() {
            var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
            parent.layer.close(index);
        }

    </script>
    <style>

    </style>
</head>
<body>
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="portlet light bordered">
                <div class="portlet-body form">
                    <form id="inputForm" class="form-horizontal form-row-seperated">
                        <input type="hidden" id="id" name="id" value="${claimCase.id}"/>
                        <div class="form-body">
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label" for="mobile"><span
                                                style="color:red">*</span>银行名称：</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="bankName" name="bankName" value="${claimCase.bankName!''}"
                                               />
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-3 control-label" for="mobile"><span
                                                style="color:red">*</span>账户名：</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="bankAccount" name="bankAccount" value="${claimCase.bankAccount!''}"
                                               />
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-3 control-label" for="mobile"><span
                                                style="color:red">*</span>银行卡号：</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="bankCard" name="bankCard" value="${claimCase.bankCard!''}"
                                               />
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="control-label col-sm-3">开户行：</label>
                                    <div class="col-sm-9">
                                        <select id="totalBankSelect" name="totalBankSelect"
                                                class="js-data-example-ajax">
                                        </select>
                                        <input id="totalBankId" name="totalBankId" type="hidden">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="control-label col-sm-3"><span
                                                class="required"> * </span>支行信息：</label>
                                    <div class="col-sm-9">
                                        <select id="bankSubbranchSelect" name="bankSubbranchSelect"
                                                class="js-data-example-ajax" disabled>
                                        </select>
                                        <input id="bankInfoId" name="bankInfoId" type="hidden" value="${claimCase.bankInfoId!''}">
                                        <input id="bankSubName" name="bankSubName" type="hidden" value="${bankInfo.bankName!''}">
                                    </div>
                                </div>
                            </div>
                            <div class="form-actions">
                                <div class="col-sm-12">
                                    <div class="btn-group pull-right">
                                        <button class="btn btn-success" id="confirmBtn" type="submit"
                                                style="margin-right: 20px">确认
                                        </button>
                                        <button class="btn" id="cancelBtn" type="button"
                                                style="margin-right:80px" onclick="doCancel()">取消
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- END FORM-->
            </div>
        </div>
    </div>
</div>
</body>

</html>