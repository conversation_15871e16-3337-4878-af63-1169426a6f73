<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <!-- Custom styles for this template -->
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <#--widget-->


    <script type="text/javascript">
        function iframeH() {
            var ifm = parent.document.getElementById("contentFrame");
            if (ifm) {
                var ifmH = $(ifm).height()
                var h = $(document.body).height() + 120;
                if (ifmH < h) {
                    parent.document.getElementById("contentFrame").height = h;
                }
            }
        }


        function deleteDuty(subjectId) {
            var formData = new FormData();
            formData.append("claimSubjectId", subjectId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/deleteDuty",
                type: 'POST',
                data: formData,
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layer.msg(
                            "删除成功！！",{icon:1,time:3000,shade: [0.5, '#000000']},
                            function () {
                                parent.window.location.reload();
                            }
                        );
                    } else {
                        layer.msg(result.msg,{icon: 2,time:-1,shade: [0.5, '#000000']})
                    }
                },
                error: function (data) {
                    console.log(data);
                }
            })
        }

        //创建责任
        function createDutys() {
            let chenckBoxItem = $("div[label='appySubjectMap']").find("span[class='checked']");
            let dutyStr="";
            $.each(chenckBoxItem,function (index,obj) {
                dutyStr+=","+$(this).find("input").val();
            });
            if(dutyStr==""){
                layer.msg("必须选择一个责任！！！",{icon:2,time:3000});
                return;
            }

            var formData = new FormData();
            formData.append("dutyStr", dutyStr.substring(1));
            formData.append("claimCaseId", '${claimCaseId}');
            $.ajax({
                url: "${ctx}/insuranceCaseController/createDutys",
                type: 'POST',
                data: formData,
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layer.msg(
                            "创建成功！！",{icon:1,time:3000,shade: [0.5, '#000000']},
                            function () {
                                parent.window.location.reload();
                            }
                        );
                    } else {
                        layer.msg(result.msg,{icon: 2,time:-1,shade: [0.5, '#000000']})
                    }
                },
                error: function (data) {
                    console.log(data);
                }
            })
        }

        function closeIframe() {
            console.log(window.name);
            var index = parent.layer.getFrameIndex(window.name); // 先得到当前 iframe 层的索引
            parent.layer.close(index); // 再执行关闭
        }
    </script>
    <style>
        .form-group {
            margin-bottom: 10px !important;
        }

        .widget-thumb .widget-thumb-heading {
            font-size: 16px !important;
        }

        .caption-subject {
            font-weight: 500 !important;
        }

        .btn-bule {
            color: #FFFFFF;
            background-color: #1676FF;
            border-color: #1676FF;
            margin-left: 3%;
        }

        .btn-bule:hover {
            color: #FFFFFF;
        }


        .btn-light-bule {
            color: #3662EC;
            background-color: #DEEBFF;
            border-color: #DEEBFF;
            margin-left: 3%;
        }

        .btn-light-bule:hover {
            color: #3662EC;
        }

        .container-head {
            border-bottom-width: 3px !important;
            padding-left: 10% !important;
            padding-right: 10% !important;
        }

        .container-boby {
            padding-left: 5% !important;
            padding-right: 5% !important;
        }

        .block-show {
            display: flex;
        }

        .block-head-label {
            margin-bottom: 1%;
            font-size: 20px;
        }

        .block-head-label a {
            font-size: 15px;
            margin-left: 10px;
        }

        .block-head-label span {
            font-size: 15px;
        }

        .block-border {
            border-top: 3px solid #EFEFEF;
            border-bottom: 3px solid #EFEFEF;
            border-left: 3px solid #FFFFFF;
            border-right: 3px solid #EFEFEF;
            margin-text-outline: 1.5%;
            margin-bottom: 2%;
        }

        .block-border .col-sm-4 {
            padding: 0px 1px 0px 0px !important;
        }

        .block-border .col-sm-8 {
            padding: 0px 1px 0px 0px !important;
        }

        .block-border .col-sm-12 {
            padding: 0px 1px 0px 0px !important;
        }

        .left-min-5 {
            width: 41.66666%;
            padding-bottom: 2.5%;
            background-color: #EFEFEF;
            padding-top: 2.5%;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-left-min-5 {
            width: 41.66666%;
            border-top: 3px solid #EFEFEF;
            border-bottom: 3px solid #FFFFFF;
        }

        .right-min-7 {
            width: 58.33334%;
            padding-bottom: 2.5%;
            background-color: white;
            padding-top: 2.5%;
            padding-left: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-right-min-7 {
            width: 58.33334%;
            border-top: 3px solid #FFFFFF;
            border-bottom: 3px solid #EFEFEF;
        }

        .left-mid-5 {
            width: 20.83333%;
            padding-bottom: 1.25%;
            background-color: #EFEFEF;
            padding-top: 1.25%;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-left-mid-5 {
            width: 20.83333%;
            border-top: 3px solid #EFEFEF;
            border-bottom: 3px solid #FFFFFF;
        }

        .right-mid-7 {
            width: 79.16667%;
            padding-bottom: 1.25%;
            background-color: #FFFFFF;
            padding-top: 1.25%;
            padding-left: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-right-mid-7 {
            width: 79.16667%;
            border-top: 3px solid #FFFFFF;
            border-bottom: 3px solid #EFEFEF;
        }


        .left-max-5 {
            width: 13.88888%;
            padding-bottom: 0.833%;
            background-color: #EFEFEF;
            padding-top: 0.833%;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-left-max-5 {
            width: 13.88888%;
            border-top: 3px solid #EFEFEF;
            border-bottom: 3px solid #FFFFFF;
        }

        .right-max-7 {
            width: 86.11112%;
            padding-bottom: 0.833%;
            background-color: #FFFFFF;
            padding-top: 0.833%;
            padding-left: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-right-max-7 {
            width: 86.11112%;
            border-top: 3px solid #FFFFFF;
            border-bottom: 3px solid #EFEFEF;
        }


        .attachInfo {
            border: 1px solid #D8D8D8;
            padding: 0px 3.5% 20px;
            margin-bottom: 2%;
        }

        .attachInfo label {
            margin-top: 20px;
        }

        .attachInfo img {
            width: 100%;
            height: 100%;
            border: 1px solid;
        }

        .attachInfo .col-sm-2 {
            margin-top: 20px !important;
        }

        .attachInfo .icon-attach {
            display: block;
            position: absolute;
            background-color: #D8D8D8;
            color: #709BF3;
            width: 20px;
            height: 25px;
            right: 15px;
            top: 0px;
            text-align: center;
            line-height: 25px;
            font-size: 10px;
        }

        .payDutyInfo {
            margin-bottom: 2%;
        }

        .payDutyInfo .col-sm-6 {
            display: block;
            height: 246px;
            padding: 0px 2.5%;
            margin-bottom: 20px;;
        }

        .payDutyInfo .notice {
            width: 100%;
            height: 100%;
            border-radius: 10px;
            color: white;
            padding: 2% 5% 2% 2%;
        }

        .notice-no-deal {
            background-color: red;
        }

        .notice-deal {
            background-color: blue;
        }

        .notice-hangup {
            background-color: orange;
        }

        .notice-default {
            background-color: darkgrey;
        }


        .logInfo {
            margin-bottom: 2%;
        }

        .logInfo button {
            margin-top: 20px;
            margin-right: 30px;
        }

        .logListInfo {
            margin-bottom: 2%;
        }

        .logListInfo table {
            border: 1px solid #C2C2C2;
        }

        .logListInfo .detailsInfo {
            display: none;
            padding-left: 8%;
        }

        .logListInfo .detailsInfo > td {
            word-wrap: break-word;
            word-break: break-all;
        }

        .logListInfo .rowInfo:hover {
            cursor: pointer;
        }

        .minus {
            border: 1px solid black;
            border-radius: 50%;
            width: 13px;
            height: 13px;
            position: relative;
            margin-top: 3%;
        }

        .minus::before {
            content: '';
            position: absolute;
            left: 2px;
            top: 5px;
            width: 7px;
            border-top: 1px solid;
        }

        #reSendMessageContainer .radio {
            margin-left: 0px;
        }

        .btn-orange {
            color: #FFFFFF;
            background-color: orange;
            border-color: orange;
            margin-left: 3%;
        }

        .btn-orange:hover {
            color: #FFFFFF;
        }

        .btn-grey {
            color: #FFFFFF;
            background-color: #7f7f7f;
            border-color: #7f7f7f;
            margin-left: 3%;
        }

        .claim_subject_area:hover {
            cursor: pointer;
        }
    </style>
</head>

<body>

<div class="row" >
    <div class="col-sm-12">
        <div class="row logListInfo" style="padding: 5% 10%">
            <div class="block-head-label">
                责任信息
            </div>
            <div class="col-sm-12">
                <table class="table">
                    <thead>
                    <tr>
                        <td width="20%"></td>
                        <td width="30%">责任</td>
                        <td width="30%">创建时间</td>
                        <td width="20%">操作</td>
                    </tr>
                    </thead>
                    <tbody>
                    <#if claimCaseSubjectList??>
                        <#list claimCaseSubjectList as info>
                            <tr class="rowInfo">
                                <td width="20%" align="center">
                                </td>
                                <td width="30%">${info.subjectName!'-'}</td>
                                <td width="30%">
                                    ${(info.createTime?string("yyyy-MM-dd HH:mm:ss"))!'-'}
                                </td>
                                <td width="20%">
                                    <a onclick="deleteDuty('${info.id}')">删除</a>
                                </td>
                            </tr>
                        </#list>
                    </#if>

                    </tbody>
                </table>
            </div>
            <div class="col-sm-12">
                <div style="margin-left: 10px" class="row" label="appySubjectMap">
                    <div>责任类型：</div>
                    <#if appySubjectMap?exists>
                        <#list appySubjectMap.keySet() as object>
                            <div class="col-sm-4" style="margin-bottom: 0px;padding: 0px 0px 5px 0px;display: flex">
                                <input type="checkbox" name="appySubjectBox" value="${appySubjectMap.get(object)}" style="cursor: pointer">${appySubjectMap.get(object)}
                            </div>
                        </#list>
                    </#if>
                </div>
                <button class="btn btn-orange pull-right" onclick="createDutys()">
                    创建责任
                </button>
            </div>
        </div>
    </div>
</div>
</div>
</body>
</html>