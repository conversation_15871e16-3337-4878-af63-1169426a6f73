<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <!-- Custom styles for this template -->
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <#--widget-->


    <script type="text/javascript">
        function iframeH() {
            var ifm = parent.document.getElementById("contentFrame");
            if (ifm) {
                var ifmH = $(ifm).height()
                var h = $(document.body).height() + 120;
                if (ifmH < h) {
                    parent.document.getElementById("contentFrame").height = h;
                }
            }
        }

        $(document).ready(function () {

            $(".logListInfo").on("click", ".rowInfo", function (e) {
                e.stopPropagation();
                console.log('hhhh')
                let icon = $(this).children(1).children(1);
                var iconClass = icon.attr("class");
                if (iconClass == 'icon-plus') {
                    icon.attr("class", "minus");
                    $(this).next().show();
                }
                if (iconClass == 'minus') {
                    icon.attr("class", "icon-plus");
                    $(this).next().hide();
                }
                iframeH();
            });

        });

        function seePersonDetail(policyPersonId) {
            //todo 等待晨哥接口
            window.open("${ctx}/personnelManagementController/personnelManagementDetail?policyPersonId="+policyPersonId);
        }
    </script>
    <style>
        .attachInfo label {
            margin-top: 20px;
        }

        .attachInfo img {
            width: 100%;
            height: 100%;
            border: 1px solid;
        }

        .attachInfo .col-sm-2 {
            margin-top: 20px !important;
        }

        .btn-bule {
            color: #FFFFFF;
            background-color: #1676FF;
            border-color: #1676FF;
            border-radius: 10px;
            margin: 2px;
        }

        .btn-bule:hover {
            color: #FFFFFF;
        }

        .attachInfo .icon-attach {
            display: block;
            position: absolute;
            background-color: #D8D8D8;
            color: #709BF3;
            width: 20px;
            height: 25px;
            right: 15px;
            top: 0px;
            text-align: center;
            line-height: 25px;
            font-size: 10px;
        }

        .payDutyInfo .col-sm-6 {
            display: block;
            height: 246px;
            padding: 0px 2.5%;
            margin-bottom: 20px;;
        }

        .payDutyInfo .notice {
            width: 100%;
            height: 100%;
            border-radius: 10px;
            color: white;
            padding: 2% 5% 2% 2%;
        }


        .logInfo button {
            margin-top: 20px;
            margin-right: 30px;
        }

        .logListInfo {
            margin-bottom: 2%;
        }

        .logListInfo table {
            border: 1px solid #C2C2C2;
        }

        .logListInfo .detailsInfo {
            display: none;
            padding-left: 8%;
        }

        .logListInfo .detailsInfo > td {
            word-wrap: break-word;
            word-break: break-all;
        }

        .logListInfo .rowInfo:hover {
            cursor: pointer;
        }


        #reSendMessageContainer .radio {
            margin-left: 0px;
        }

        td{
            text-align: center;
        }

        .block-head-label {
            margin-bottom: 1%;
            font-size: 20px;
            margin-left: 15px;
        }
    </style>
</head>

<body>

<div class="row" >
    <div class="col-sm-12">
        <div class="row logListInfo" style="padding: 3%">
            <div class="block-head-label">
                历史保单信息
            </div>
            <div class="col-sm-12">
                <table class="table">
                    <thead>
                    <tr>
                        <td>保单号</td>
                        <td>订单号</td>
                        <td>被保险人姓名</td>
                        <td>被保险人身份证号</td>
                        <td>保单起期</td>
                        <td>保单止期</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody>
                    <#if policyPersonList??>
                        <#list policyPersonList as policyPerson>
                            <tr class="rowInfo">
                                <td>
                                    ${policyPerson.policyNo!'-'}
                                </td>
                                <td>${policyPerson.customerPolicyNo!'-'}</td>
                                <td>
                                    ${policyPerson.name!'-'}
                                </td>
                                <td>
                                    ${policyPerson.idNumber!'-'}
                                </td>
                                <td>
                                    ${policyPerson.startDate?string["yyyy-MM-dd HH:mm:ss"]}
                                </td>
                                <td>
                                    ${policyPerson.endDate?string["yyyy-MM-dd HH:mm:ss"]}
                                </td>
                                <td>
                                    <button class="btn btn-bule" onclick="seePersonDetail('${policyPerson.id}')">查看详情</button>
                                </td>
                            </tr>
                        </#list>
                        <#else >
                        <tr>
                            <td colspan="7"><h2>暂无数据！！</h2></td>
                        </tr>
                    </#if>

                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
</div>
</body>
</html>