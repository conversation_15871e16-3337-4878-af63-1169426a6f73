<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>任务分配</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>

    <script>

        $(function() {

            // 预处理人员切换
            $("body").on("click", "#pretreatmentManager button", function() {
                $(this).addClass("active");
                $(this).siblings().removeClass("active");
            });

            // 特殊商人员切换
            $("body").on("click", "#specialManager button", function() {
                $(this).addClass("active");
                $(this).siblings().removeClass("active");
            });

            // 新职伤人员切换
            $("body").on("click", ".newJobHarmManager button", function() {
                $(this).addClass("active");
                $(this).siblings().removeClass("active");
            });

            // 出险类型
            $("#process").select2({
                placeholder: "请选择",
                width: null,
                data: [{id: '预处理', text: '预处理', selected: true}, {id: '特殊商', text: '特殊商'}, {id: '新职伤', text: '新职伤'}, {id: '处理岗', text: '处理岗'}]
            });

            var loadNum = 0;

            $("body").on("change", "#process", function() {
                var val = $(this).val();
                if (loadNum != 0) {
                    $(".active").removeClass("active");
                }
                loadNum++;
                switch (val) {
                    case "预处理":
                        $(".pretreatment-process").show().siblings().hide();
                        break;
                    case "特殊商":
                        $(".special-process").show().siblings().hide();
                        break;
                    case "新职伤":
                        $(".newJobHarm-process").show().siblings().hide();
                        break;
                    case "处理岗":
                        $(".goods-process").show().siblings().hide();
                        break;
                }
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.iframeAuto(index);
            });

            $("#process").val("${claimCase.caseProcess!'预处理'}").trigger("change");

            // 分配人员
            $("select[name='auditerId']").select2({
                placeholder: "请选择",
                width: null
            });

        })

        function push() {

            var dataMap = new Map();

            var process = $("#process").val();
            dataMap.set("process", process);
            dataMap.set("claimCaseId", "${claimCase.id}");
            dataMap.set("isNew", "${isNew}");

            switch (process) {
                case "预处理":
                    var managerId = $("#pretreatmentManager button.active").attr("data-managerId");
                    if (!managerId) {
                        layer.msg("请选择预处理人员", {icon: 2, time: 1500, shade: [0.1, '#000']});
                        return;
                    }
                    dataMap.set("pretreatmentAuditer", managerId);
                    break;
                case "特殊商":
                    var managerId = $("#specialManager button.active").attr("data-managerId");
                    if (!managerId) {
                        layer.msg("请选择特殊商处理人员", {icon: 2, time: 1500, shade: [0.1, '#000']});
                        return;
                    }
                    dataMap.set("pretreatmentAuditer", managerId);
                    break;
                case "新职伤":
                    var managerId = "";
                    var managerMap = new Map();
                    <#if newJobHarmManagerMap?? && (newJobHarmManagerMap.keySet()?size>0)>
                        <#list newJobHarmManagerMap.keySet() as type>
                            var auditerId = $(".newJobHarmManager button[data-type='${type}'].active").attr("data-managerId");
                            if (!auditerId) {
                                <#if type == 1>
                                    layer.msg("请选择新职伤的骑手人伤处理人员", {icon: 2, time: 1500, shade: [0.1, '#000']});
                                <#else >
                                    layer.msg("请选择新职伤的其他项目处理人员", {icon: 2, time: 1500, shade: [0.1, '#000']});
                                </#if>
                                return;
                            }
                           managerMap.set("${type}", auditerId);
                        </#list>
                    </#if>
                    if (managerMap.size == 0) {
                        layer.msg("当前暂无赔付对象", {icon: 2, time: 1500, shade: [0.1, '#000']});
                        return;
                    }
                    if(!managerMap.has("1")) {
                        managerMap.set("1", "");
                    }
                    if(!managerMap.has("2")) {
                        managerMap.set("2", "");
                    }
                    managerId = managerMap.get("1") + "," + managerMap.get("2");
                    dataMap.set("pretreatmentAuditer", managerId);
                    break;
                case "处理岗":
                    var bindInfo = [];

                    $.each($("tr[name='roleInfo']"), function (index, obj) {
                        let objectId = $(this).attr("code");
                        let auditerId = $.trim($(this).find("select[name='auditerId']").val());
                        if (auditerId) {
                            bindInfo.push({
                                "objectId": objectId,
                                "auditerId": auditerId
                            })
                        }
                    });
                    if (bindInfo.length != $("tr[name='roleInfo']").size() ) {
                        layer.msg("请完成所有赔付对象的分配", {icon: 2, time: 1500, shade: [0.1, '#000']});
                        return;
                    }
                    if (bindInfo.length == 0) {
                        layer.msg("当前暂无赔付对象", {icon: 2, time: 1500, shade: [0.1, '#000']});
                        return;
                    }
                    dataMap.set("bindInfo", bindInfo);
                    break;
                default:
                    layer.msg("案件流程错误", {icon: 2, time: 1500});
                    return;
            }

            layer.confirm("请再次确认是否分配完成？", {icon: 3}, function (index) {
                $.ajax({
                    url: "${ctx}/claimCaseDistributionController/bindAuditerV2",
                    type: 'POST',
                    data: mapToJson(dataMap),
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            layer.msg(result.msg, {icon: 1, time: 2000}, function(index) {
                                parent.window.location.reload();
                                layer.close(index);
                            });
                        } else {
                            layer.msg(result.msg, {
                                icon: 2,
                                time: 1500
                            });
                        }
                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                });
            });
        }

        // map转换JSON
        function mapToJson(map) {
            return JSON.stringify(Array.from(map).reduce((obj, [key, value]) => {
                obj[key] = value;
                return obj;
            }, {}));
        }
    </script>
    <style>
        .row {
            margin-top: 20px;
            margin-left: 20px;
            margin-right: 20px;
        }

        .btn-div {
            text-align: right;
            margin: 10px;
        }

        .btn-div > button {
            margin-right: 20px;
        }
        button.active {
            background: #0597FF;
            border-color: #ecebeb;
            color: #fff !important;
        }

        .objectListInfo {
            margin-bottom: 2%;
        }

        .objectListInfo table {
            border: 1px solid #C2C2C2;
        }

        .objectListInfo .detailsInfo {
            display: none;
            padding-left: 8%;
        }

        .objectListInfo .detailsInfo > td {
            word-wrap:break-word;
            word-break:break-all;
            text-align: left;
        }
        .objectListInfo .rowInfo:hover{
            cursor: pointer;
        }

        .label-title {
            font-size: 18px !important;
        }
    </style>
</head>
<body>
    <div>
        <div class="row">
            <div class="col-sm-12" style="margin-bottom: 20px;padding-left: 0px;padding-right: 0px;line-height: 34px;">
                <div class="col-sm-4" style="padding: 0px;">
                    <div class="col-sm-12">
                        案件号：${claimCase.claimCaseNo}
                    </div>
                </div>

                <div class="col-sm-4" style="padding: 0px;">
                    <div class="col-sm-12" style="padding: 0px;">
                        当前状态：${claimCaseStatusEumMap.get(claimCase.status)}
                    </div>
                </div>

                <div class="col-sm-4" style="padding: 0px;">
                    <div class="col-sm-3" style="padding: 0px;">
                        分配流程：
                    </div>
                    <div class="col-sm-9" style="padding: 0px;">
                        <select class="form-control " id="process">

                        </select>
                    </div>
                </div>

            </div>
        </div>
        <div class="row">

            <!--预处理流程-->
            <div class="col-sm-12 pretreatment-process" style="margin-bottom: 20px;padding-left: 0px;padding-right: 0px;">
                <div class="col-sm-12" style="margin-bottom: 10px;">
                    预处理人员(任务数)：
                </div>
                <div class="col-sm-12" style="margin-bottom: 10px;" id="pretreatmentManager">
                    <#if pretreatmentPersonMap?? && (pretreatmentPersonMap.keySet()?size>0)>
                        <#list pretreatmentPersonMap.keySet() as key>
                            <button class="margin-right-10 margin-bottom-10 btn <#if claimCase.pretreatmentAuditer?contains(key)>active</#if>" data-managerId="${key}" >${auditerMap.get(key)}</button>
                        </#list>
                    </#if>
                </div>
            </div>

            <!--特殊商流程-->
            <div class="col-sm-12 special-process" style="margin-bottom: 20px;padding-left: 0px;padding-right: 0px;">
                <div class="col-sm-12" style="margin-bottom: 10px;">
                    特殊商人员(任务数)：
                </div>
                <div class="col-sm-12" style="margin-bottom: 10px;" id="specialManager">
                    <#if specialPersonMap?? && (specialPersonMap.keySet()?size>0)>
                        <#list specialPersonMap.keySet() as key>
                            <button class="margin-right-10 margin-bottom-10 btn <#if claimCase.pretreatmentAuditer?contains(key)>active</#if>" data-managerId="${key}" >${auditerMap.get(key)}</button>
                        </#list>
                    </#if>
                </div>
            </div>

            <!--新职伤流程-->
            <div class="col-sm-12 newJobHarm-process" style="margin-bottom: 20px;padding-left: 0px;padding-right: 0px;">
                <#if claimCase.pretreatmentAuditer??>
                    <#assign auditerIndex = claimCase.pretreatmentAuditer?index_of(',')>
                    <#if auditerIndex == -1>
                        <#assign auditerIndex = 0>
                    </#if>
                </#if>
                <#if newJobHarmManagerMap?? && (newJobHarmManagerMap.keySet()?size>0)>
                    <#list newJobHarmManagerMap.keySet() as type>
                        <div class="col-sm-12" style="margin-bottom: 10px;">
                            <#if type == 1>
                                骑手人伤：
                                <#if claimCase.pretreatmentAuditer??>
                                    <#assign auditer = claimCase.pretreatmentAuditer?substring(0, auditerIndex)>
                                </#if>
                            <#elseif type == 2>
                                其他项目：
                                <#if claimCase.pretreatmentAuditer??>
                                    <#assign auditer = claimCase.pretreatmentAuditer?substring(auditerIndex + 1)>
                                </#if>
                            </#if>
                        </div>
                        <#assign newManagerMap = newJobHarmManagerMap.get(type)>
                        <div class="col-sm-12 newJobHarmManager" style="margin-bottom: 10px;">
                            <#if newManagerMap?? && (newManagerMap?size>0)>
                                <#list newManagerMap as key>
                                    <button class="margin-right-10 margin-bottom-10 btn <#if auditer == key>active</#if>" data-managerId="${key}" data-type="${type}">${auditerMap.get(key)}</button>
                                </#list>
                            </#if>
                        </div>
                    </#list>
                <#else>
                <div class="col-sm-12" style="margin-bottom: 10px;">
                    <h1 align="center">当前案件暂无赔付对象</h1>
                </div>
                </#if>
            </div>

            <!--处理岗流程-->
            <div class="col-sm-12 goods-process" style="margin-bottom: 20px;padding-left: 0px;padding-right: 0px;">
                <div class="col-sm-12" style="margin-bottom: 10px;">
                    处理岗人员(任务数)：
                </div>
                <div class="col-sm-12" style="margin-bottom: 10px;" id="goodsManager">
                    <#if goodsPersonMap?? && (goodsPersonMap.keySet()?size>0) >
                        <#list goodsPersonMap.keySet() as key>
                            <button class="margin-right-10 margin-bottom-10 btn " data-managerId="${key}" >${auditerMap.get(key)}</button>
                        </#list>
                    </#if>
                </div>

                <div class="col-sm-12 objectListInfo">
                    <div class="block-head-label col-sm-12" style="margin-bottom: 20px;padding: 0px;">
                        <h4 style="font-weight: bold;">赔付列表</h4>
                    </div>
                    <div class="col-sm-12" style="padding: 0px;">
                        <table class="table">
                            <thead>
                            <tr>
                                <th width="20%">类型</th>
                                <th width="20%">赔付对象名称</th>
                                <th width="20%">手机号</th>
                                <th width="15%">预估金额</th>
                                <th width="20%">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                                <#if claimCaseObjectList??>
                                <#list claimCaseObjectList as claimCaseObject>
                                    <tr class="rowInfo" name="roleInfo" code="${claimCaseObject.id}">
                                        <td width="20%">
                                            ${applyTypeNewEnumMap.get(claimCaseObject.type + '-' + claimCaseObject.category).msg}
                                            <#if claimCaseObject.insCode == "GY" >
                                                (国元)
                                            <#elseif claimCaseObject.insCode == "DD" >
                                                (大地)
                                            <#elseif claimCaseObject.insCode == "RB" >
                                                (人保)
                                            </#if>
                                        </td>
                                        <td width="20%">${claimCaseObject.name}</td>
                                        <td width="20%">${claimCaseObject.mobile}</td>
                                        <td width="15%">${claimCaseObject.estimatedApprovedMoney}</td>
                                        <td width="20%">
                                            <div style="padding-left: 0;">
                                                <select class="form-control " name="auditerId" style="height: 34px;">
                                                    <option value=" " selected>-请选择分配人员-</option>
                                                    <#if goodsPersonMap?? && (goodsPersonMap.keySet()?size>0) >
                                                        <#list goodsPersonMap.keySet() as key>
                                                            <option value="${key}" <#if claimCaseObject.auditer == key>selected</#if>>${goodsPersonMap.get(key)}</option>
                                                        </#list>
                                                    </#if>
                                                </select>
                                                <input type="hidden" name="auditer" value="${claimCaseObject.auditer}">
                                            </div>
                                        </td>
                                    </tr>
                                </#list>
                                </#if>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <div class="row">
        <div class="col-sm-12 btn-div" >
            <button class="btn btn-primary" onclick="push()">分配</button>
            <button class="btn btn-warning" onclick="parent.layer.closeAll();">取消</button>
        </div>
    </div>
</body>
</html>