<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>损失项目</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <script src="${ctx}/carousel/dataCollection/viewer.js"></script>
    <script src="${ctx}/carousel/dataCollection/viewermain.js"></script>
    <link href="${ctx}/carousel/dataCollection/viewer.css" rel="stylesheet">
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/metronic/global/scripts/app.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/layout.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/demo.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/jquery-multi-select/js/jquery.multi-select.js"
            type="text/javascript"></script>
    <script src="${ctx}/js/genProCity.js"></script>
    <script src="${ctx}/js/genInjury.js" type="text/javascript"></script>
    <script src="${ctx}/js/idCardCheck.js" type="text/javascript"></script>
    <style>

        .container-fluid {
            margin: 20px;
        }

        .clear-margin {
            margin: 0px;
        }

        .clear-padding {
            padding: 0px;
        }

        .chart-search {
            margin-bottom: 10px;
            display: none;
        }


        .search-channel-content {
            height: 100px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .search-ul-list {
            list-style-type: none;
            height: 20px;
            padding-left: 0px;
            color: black;
        }

        .channel {
            float: left;
            display: block;
        }

        .typeClick {
            height: 112px;
            margin-bottom: 10px;
            border: 2px solid black;
            border-radius: 5px !important;
            margin-left: 20px;
            cursor: pointer
        }

        .typeClickClick {
            height: 112px;
            margin-bottom: 10px;
            border: 2px solid #1676ff;
            background-color: #1676ff;
            color: white;
            border-radius: 5px !important;
            margin-left: 20px;
            cursor: pointer
        }

        .has-error {
            border-color: #ce0d0d !important;
            color: red !important;
        }

        .bckColor {
            background-color: rgba(51, 153, 241, 0.3);
        }

        .imageListChoose {
            /*background-color: #bad6ff !important;*/
            border: 5px solid #055ad7;
        }

        .collectionCompleted {
            background-color: #E8F2FF;
        }

        .collectionButton {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: transparent;
            border-radius: 6px !important;
            color: #1ABB00;
        }

        .collectionButtonClick {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00 !important;
            border-radius: 6px !important;
            color: #FFFFFF;
        }

        .collectionData:hover {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00;
            color: #FFFFFF;
        }

        .middleTable {
            border: 4px solid #1767ff !important;
        }

        .hangup {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #ce0d0d !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }

        .error {
            color: #ce0d0d !important;
        }

        .txt-over-hide {
            color: red;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: block;
            width: 650px;
        }

        .jump {
            /*float: right;*/
            border-radius: 5px !important;
            color: #3399ff !important;
            background-color: #fff !important;
            border-color: #3399ff;
            height: 40px;
            width: 100%;
        }

        .genTask {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #3399ff !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }

        .thClass {
            text-align: center;
            color: #fff;
        }

        .typeSelect {
            border-color: #3399ff;
            background-color: #3399ff;
        }

        .thumbnail-img {
            width: 50px;
            height: 45px;
            border: 4px solid #D6D6C1;
        }

        .selected-thumbnail-img {
            border: 4px solid #3399ff !important;
        }

        .collection-thumbnail-img {
            border: 4px solid #00B176;
        }

        /*里面的代码可以根据自己需求去进行更改*/
        /* 设置滚动条的样式 */
        ::-webkit-scrollbar {
            width: 5px;
        }

        /* 滚动槽 */
        ::-webkit-scrollbar-track {
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
            border-radius: 10px;
        }

        /* 滚动条滑块 */
        ::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
        }

        ::-webkit-scrollbar-thumb:window-inactive {
            background: rgba(255, 0, 0, 0.4);
        }

        .ticket-information {
            height: 1050px;
            overflow-y: scroll;
        }

        .dataDisplayArea {
            width: 100%;
            height: 19%;
            border-radius: 10px !important;
            /*border: 1px solid #aca8a8;*/
            border: 1px solid #797979;
            cursor: pointer;
            margin-top: 10px;
            overflow: hidden;
        }

        .selected-dataDisplayArea {
            background-color: #91cae9;
        }

        .submit-dataDisplayArea {
            border: 3px solid #44db69 !important;
        }

        .change-dataDisplayArea {
            border: 3px solid red !important;
        }

        .dataDisplayArea > div {
            height: 20%;
            padding: 10px 0px 0px 0px !important;
        }

        .dataDisplayArea-head {
            background-color: #3399ff;
            /*border-radius: 10px 10px 0px 0px !important;*/
        }

        .dataDisplayArea-head-left {
            color: #ffffff;
            font-size: 15px;
            padding: 0px 0px 0px 5px;
        }

        .dataDisplayArea-head-right {
            text-align: right;
            padding: 0px !important;
        }

        .dataDisplayArea-head-img {
            width: 30px;
            height: 18px;
        }

        .dataDisplayArea-body-left {
            opacity: 0.7;
        }

        .dataDisplayArea-body-right {
            text-align: right;
        }

        .row {
            margin: 0px 0px;
        }

        .subject-name {
            font-size: 20px;
        }

        .estimateInventoryDataBlue {
            background-color: #3581ee;
        }

        #enumMapByParentCodeInfo > button {
            margin: 5px 6px;
        }

        .line-center {
            display: inline-block;
            background-color: blue;
            border-radius: 50%;
            height: 30px;
            width: 30px;
            color: white;
            text-align: center;
            line-height: 30px;
        }

        .icon-plus {
            font-size: 30px;
            line-height: 30px;
        }

        .icon-plus:hover {
            cursor: pointer;
        }

        .select2-selection {
            background-color: #fff !important;
            padding: 6px 12px !important;
            height: 32px !important;
            font-size: 14px !important;
            line-height: 1.42857 !important;
            color: #4d6b8a !important;
        }

    </style>

    <script type="text/javascript">

        const proCityPickerData = new GenProCity().getValue();

        function save() {
            console.log($("#province option:selected").val())
            $.ajax({
                url: "${ctx}/dictController/insertSubsidiaryLabel?province="+$("#province option:selected").val()+"&city="+$("#city option:selected").val()+"&district="+$("#district option:selected").val()+"&name="+$("#name").val()+"&labelCode="+$("#labelCode").val(),
                type: 'POST',
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layer.msg(result.msg, {icon: 1, time: 1500}, function(index) {
                            parent.location.reload();
                            layer.close(index);
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1500}, function(index) {
                            layer.close(index);
                        });
                    }
                },
                error: function (data) {
                    alert(data);
                }
            });
        }

        $(function() {

            //初始化 省
            proCityPickerData.forEach(function (item, index) {
                var option = document.createElement("option");
                option.innerText = item['value'];
                option.value = item['id'];
                option.setAttribute("data-idx", index);
                $("#province").append(option);
                console.log(item['id'])
                console.log(item['value'])
            });

        })


        function provinceChange() {
            var provinceIdx = $("#province option:selected").attr("data-idx");
            console.log(provinceIdx)
            $("#city").html("<option value=''>-市-</option>");
            $("#district").html("<option value=''>-区-</option>");
            if (provinceIdx) {
                var cityList = proCityPickerData[provinceIdx].childs;
                console.log(cityList)
                cityList.forEach(function (item, index) {
                    var option = document.createElement("option");
                    option.innerText = item['value'];
                    option.value = item['id'];
                    option.setAttribute("data-idx", index);
                    $("#city").append(option);
                });
            }

        }

        function cityChange() {
            var provinceIdx = $("#province option:selected").attr("data-idx");
            var cityIdx = $("#city option:selected").attr("data-idx");
            $("#district").html("<option value=''>-区-</option>");
            if (cityIdx) {
                var districtList = proCityPickerData[provinceIdx].childs[cityIdx].childs;
                districtList.forEach(function (item, index) {
                    var option = document.createElement("option");
                    option.innerText = item['value'];
                    option.value = item['id'];
                    $("#district").append(option);
                });
            }
        }
    </script>
</head>


<body id="qc-Body" style="overflow-x:hidden;background: #fff;">
<div class="container-fluid">
    <div class="row">
        <form id="inputForm" onsubmit="return false" >
            <div class="row" style="display: flex;justify-content: space-evenly;">
                <div class="row" style="margin-bottom: 20px;" >
                    <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                        <div class="col-sm-3" style="text-align: right;line-height: 34px">子商名称</div>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" id="name" name="name" />
                        </div>
                    </div>
                    <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                        <div class="col-sm-3" style="text-align: right;line-height: 34px">标签Code</div>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" id="labelCode" name="labelCode" />
                        </div>
                    </div>
                    <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                        <div class="col-sm-3" style="text-align: right;line-height: 34px">省</div>
                        <div class="col-sm-8">
                            <select class="form-control" name="province" id="province" onchange="provinceChange()">
                                <option value="">-省-</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                        <div class="col-sm-3" style="text-align: right;line-height: 34px">市</div>
                        <div class="col-sm-8">
                            <select class="form-control" name="city" id="city" onchange="cityChange()">
                                <option value=''>-市-</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                        <div class="col-sm-3" style="text-align: right;line-height: 34px">区</div>
                        <div class="col-sm-8">
                            <select class="form-control" name="district" id="district">
                                <option value=''>-区-</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" style="text-align: center">
                <div class="col-sm-6">
                    <button class="btn btn-primary" onclick="save()" style="padding:5px 20px;font-size: 20px;">确定</button>
                </div>
                <div class="col-sm-6">
                    <button class="btn btn-primary" onclick="parent.layer.closeAll();" style="padding:5px 20px;font-size: 20px;">取消</button>
                </div>
            </div>
        </form>
    </div>
</div>
</body>
</html>
