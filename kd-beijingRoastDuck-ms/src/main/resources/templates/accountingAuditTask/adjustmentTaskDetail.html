<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <title>理算复核详情</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport" />
    <meta content="" name="description" />
    <meta content="" name="author" />

    <!-- 引入公共CSS -->
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/static/metronic/global/plugins/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/static/metronic/global/plugins/simple-line-icons/simple-line-icons.min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/static/metronic/global/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/static/metronic/global/plugins/bootstrap-switch/css/bootstrap-switch.min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/static/metronic/global/css/components.min.css" rel="stylesheet" id="style_components" type="text/css" />
    <link href="${ctx}/static/metronic/layouts/layout/css/layout.min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/static/metronic/layouts/layout/css/themes/darkblue.min.css" rel="stylesheet" type="text/css" id="style_color" />
    <link href="${ctx}/static/css/custom.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/carousel/dataCollection/viewer.css" rel="stylesheet">

    <script src="${ctx}/carousel/dataCollection/viewer.js"></script>
    <script src="${ctx}/carousel/dataCollection/viewermain.js"></script>
    <link href="${ctx}/carousel/dataCollection/viewer.css" rel="stylesheet">
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet" type="text/css"/>
    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/metronic/global/scripts/app.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/layout.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/demo.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/jquery-multi-select/js/jquery.multi-select.js" type="text/javascript"></script>

    <script type="text/javascript">

        /*var viewer = null;

        var rowNum = 0;*/


        $(function () {

            /*图片展示工具*/
            /*(function () {
                viewer = new Viewer({
                    activeId: null
                });
            })();*/



            /*/!*点击缩略图*!/
            $('body').on('click', '.thumbnail-img', function () {
                if ($(this).hasClass('selected-thumbnail-img')) {
                    return;
                }
                let attrId = $(this).data('fileid');
                $('.selected-thumbnail-img').removeClass('selected-thumbnail-img');
                $(this).addClass('selected-thumbnail-img');
                viewer.showImageByFileId(attrId);
                let imgNumber = $(this).data('img-number');
                let offset = imgNumber * 95 - (1050 - 95) / 2;
                $('#thumbnail').scrollTop(offset);
                $(".viewer-canvas").show();
            });


            /!*A/D切换影像*!/
            $('body').on('click', '.viewer-list > li', function () {
                let attchId = $(this).find('img').attr('alt');
                $('#thumbnail-' + attchId).trigger('click');
                $(".viewer-canvas").show();
            });





            // 为所有折叠菜单添加点击事件
            $('.nav-link[data-toggle="collapse"]').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation(); // 防止事件冒泡到父元素
                var target = $(this).data('target');
                $(target).toggleClass('in');

                // 检查是否使用的是Bootstrap 4+，如果是则使用'show'类
                if ($(target).hasClass('show')) {
                    $(target).removeClass('show');
                } else {
                    $(target).addClass('show');
                }

                var isExpanded = $(target).hasClass('in') || $(target).hasClass('show');
                $(this).attr('aria-expanded', isExpanded);
                $(this).find('i.glyphicon-chevron-right').css('transform', isExpanded ? 'rotate(90deg)' : '');
            });
            // 添加缩略图叉号点击事件
            $('body').on('click', '.thumbnail-close', function(e) {
                e.preventDefault();
                e.stopPropagation();
                var fileId = $(this).data('fileid');
                // 这里可以添加删除图片的逻辑
                console.log('Delete image with fileId: ' + fileId);
                $("#imgArea"+fileId).remove();
                $(".viewer-canvas").hide();
                // 例如：$(this).closest('li').remove();
            });

            // 菜单项点击事件
            $('.nav-list li ul li a').on('click', function(e) {
                e.preventDefault();
                // 这里可以添加点击菜单项后的处理逻辑
                var category = $(this).text().trim();
                console.log('Selected category: ' + category);
                // 可以添加加载对应分类图片的逻辑
            });*/



            //可拖动基本信息、查看日志
            const container = document.getElementById('floating-container');
            const boxes = {
                box1: document.getElementById('box1'),
                box2: document.getElementById('box2'),
                box3: document.getElementById('box3')
            };

            const controlButtons = document.querySelectorAll('.control-btn');
            let activeBox = boxes.box1;
            let isDragging = false;
            let offsetX, offsetY;

            // 切换活动浮框
            function setActiveBox(box) {
                activeBox.classList.remove('box-active');
                activeBox = box;
                activeBox.classList.add('box-active');
            }

            // 控制面板按钮事件
            controlButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    setActiveBox(boxes[this.dataset.target]);
                });
            });

            // 容器拖拽事件
            container.addEventListener('mousedown', function(e) {
                // 只有当点击的是容器或浮框时才触发拖拽
                if (e.target === container || e.target.classList.contains('draggable-box')) {
                    isDragging = true;
                    offsetX = e.clientX - container.getBoundingClientRect().left;
                    offsetY = e.clientY - container.getBoundingClientRect().top;

                    container.style.transition = 'none';
                    Array.from(container.children).forEach(box => {
                        box.classList.add('dragging');
                    });

                    e.preventDefault();
                }
            });

            // 鼠标移动事件
            document.addEventListener('mousemove', function(e) {
                if (!isDragging) return;

                let newLeft = e.clientX - offsetX;
                let newTop = e.clientY - offsetY;

                // 限制位置（防止移出屏幕）
                const maxLeft = window.innerWidth - container.offsetWidth;
                const maxTop = window.innerHeight - container.offsetHeight;

                newLeft = Math.max(0, Math.min(newLeft, maxLeft));
                newTop = Math.max(0, Math.min(newTop, maxTop));

                container.style.left = newLeft + 'px';
                container.style.top = newTop + 'px';
                container.style.right = 'auto';
            });

            // 鼠标释放事件
            document.addEventListener('mouseup', function(e) {
                if (!isDragging) return;
                isDragging = false;

                container.style.transition = 'all 0.3s ease';
                Array.from(container.children).forEach(box => {
                    box.classList.remove('dragging');
                });

                // 判断吸附到左侧还是右侧
                const containerRect = container.getBoundingClientRect();
                const screenCenter = window.innerWidth / 2;
                const containerCenter = containerRect.left + containerRect.width / 2;

                if (containerCenter < screenCenter) {
                    container.style.left = '20px';
                    container.style.right = 'auto';
                } else {
                    container.style.left = 'auto';
                    container.style.right = '20px';
                }
            });

            // 窗口大小变化时重新定位
            window.addEventListener('resize', function() {
                const containerRect = container.getBoundingClientRect();

                if (parseInt(container.style.left) > 0 || container.style.left === '') {
                    container.style.left = '20px';
                    container.style.right = 'auto';
                } else {
                    container.style.left = 'auto';
                    container.style.right = '20px';
                }

                const maxTop = window.innerHeight - container.offsetHeight;
                const currentTop = parseInt(container.style.top || containerRect.top);
                container.style.top = Math.max(0, Math.min(currentTop, maxTop)) + 'px';
            });

            // 点击浮框切换活动状态
            Object.values(boxes).forEach(box => {
                box.addEventListener('click', function() {
                    setActiveBox(this);
                });
            });
        });


        var scrollTop;

        function uploadImg(claimCaseId) {
            scrollTop = calculationScrollTop();
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '影像编辑',
                area: [openWindowWidth, '800px'],
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/claimCaseController/editAttachNew?claimCaseId=" + claimCaseId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        // 计算滚动高度
        function calculationScrollTop() {
            var ifm = parent.document.getElementById("contentFrame");
            scrollTop = $(window.parent).scrollTop() + 150;
            if (!ifm) {
                scrollTop = 300;
            }
            return scrollTop + "px";
        }

        jQuery(document).ready(function() {
            App.init();
        });

        // 开始理算处理处理
        function startNuclearAuditFromCost(taskId) {
            if (!taskId) {
                alert('任务ID不能为空');
                return;
            }

            if (confirm('确定要开始处理吗？')) {
                alert('处理功能开发中，敬请期待！');
            }
        }

        // 暂存功能
        function saveDraft(taskId) {
            if (!taskId) {
                alert('任务ID不能为空');
                return;
            }

            if (confirm('确定要暂存当前数据吗？')) {
                // 收集处理记录数据
                var paymentMethod = $('#paymentMethod').val();
                var documentComplete = $('input[name="documentComplete"]:checked').val();

                var allData = {
                    taskId: taskId,
                    paymentMethod: paymentMethod,
                    documentComplete: documentComplete
                };

                $.ajax({
                    url: '${ctx}/settlementAuditController/saveDraft',
                    type: 'POST',
                    data: allData,
                    dataType: 'json',
                    success: function(data) {
                        if (data.ret === 'success') {
                            alert('暂存成功');
                        } else {
                            alert('数据暂存失败：' + (data.msg || '未知错误'));
                        }
                    },
                    error: function() {
                        alert('网络请求失败，请稍后重试');
                    }
                });
            }
        }

        // 返回理算处理任务管理页面
        function goBack() {
            window.location.href = '${ctx}/settlementAuditController/settlementAuditList';
        }

        // 查看保单信息
        function viewPolicyInfo(taskId) {
            alert('保单信息功能开发中，敬请期待！');
        }

        // 查看理算处理信息
        function viewNuclearAuditData(taskId) {
            alert('查看理算处理信息功能开发中，敬请期待！');
        }

        // 查看影像资料
        function viewImageData(taskId) {
            alert('影像资料功能开发中，敬请期待！');
        }


        // 查看历史案件
        /*function viewHistoryCases() {
            layer.open({
                title: " ",
                type: 1,
                offset: 'auto',
                area: ['1200px', '700px'],
                content: $("#historyCase"), // 指定要弹出的div
                cancel: function(index, layero){
                    // 当关闭弹出层时，可以在这里添加一些逻辑
                    console.log('弹出层已关闭');
                }
            });
        }*/

        // 导出处理单
        function exportAuditReport(taskId) {
            alert('导出处理单功能开发中，敬请期待！');
        }

        function showBasicInformation(claimCaseId, claimCaseObjectId){
            layer.open({
                title: "查看基本信息",
                type: 2,
                offset: 'auto',
                area: ['1200px', '700px'],
                fixed: false,
                closeBtn: 1,
                maxmin: true,
                content: "${ctx}/insuranceNuclearAudit/showBasicInformation?claimCaseId=" + claimCaseId + "&claimCaseObjectId=" + claimCaseObjectId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        function showDetailLog(){
            layer.open({
                title: " ",
                type: 1,
                offset: 'auto',
                area: ['1200px', '700px'],
                content: $("#logArea"), // 指定要弹出的div
                cancel: function(index, layero){
                    // 当关闭弹出层时，可以在这里添加一些逻辑
                    console.log('弹出层已关闭');
                }
            });
        }

        function seeCaseDetail(claimCaseId) {
            window.open("${ctx}/insuranceCaseController/caseDetail?caseId="+claimCaseId);
        }

        function seeVerifyApplyBook(claimCaseId) {
            window.open("${ctx}/claimCaseController/viewClaimApplyBook?claimCaseId="+claimCaseId);
        }

        // 返回核损任务管理页面
        function submitBack(status) {
            let claimCaseObject = assembleData(status);
            if (claimCaseObject) {
                layer.prompt({
                    title: '意见备注：',
                    formType: 2, // 0-文本输入框（默认），1-密码框，2-多行文本框
                    value: '', // 默认内容
                    maxlength: 100, // 最大输入长度
                    offset: ['280px', ''],
                    area: ['auto', 'auto'], // 自定义宽高
                    btn: ['确定', '取消'],
                    yes: function (index, elem) {
                        var value = elem.find('.layui-layer-input').val().trim();
                        if (value === '') {
                            layer.msg('请输入意见备注', {
                                icon: 2,
                                offset: ['450px', ''],
                                time: 2000
                            });
                            return false; // 阻止关闭
                        }
                        claimCaseObject["reason"] = value;
                        // 关闭弹框
                        layer.close(index);

                        layer.confirm('确定需要回退吗？', {
                            icon: 3,
                            title: '提示',
                            btn: ['确定', '取消'],
                        }, function (index) {
                            $.ajax({
                                url: "${ctx}/claimCaseObjectController/auditRejectCarV2",
                                type: 'POST',
                                data: JSON.stringify(claimCaseObject),
                                async: true,
                                cache: false,
                                contentType: false,
                                processData: false,
                                success: function (data) {
                                    var result = eval("(" + data + ")");
                                    if (result.ret == "0") {
                                        layer.msg(result.msg || '回退成功', {
                                            icon: 1,
                                            offset: ['450px', ''],
                                            time: 2000,
                                            shade: [0.0001, '#000']
                                        }, function () {
                                            window.close();
                                        });
                                    } else {
                                        layer.msg(result.msg || '回退失败', {
                                            icon: 2,
                                            offset: ['450px', ''],
                                            time: 2000,
                                            shade: [0.0001, '#000']
                                        });
                                    }
                                },
                                error: function (data) {
                                    var result = eval("(" + data + ")");
                                    console.log(result.msg);
                                    layer.msg('网络请求失败，请稍后重试', {
                                        icon: 2,
                                        offset: ['450px', ''],
                                        time: 2000
                                    });
                                }
                            });

                            layer.close(index);

                        });

                    }
                });
            }
        }

        // 提交审核
        function submitAudit(status) {
            let claimCaseObject = assembleData(status);
            if (claimCaseObject) {
                layer.confirm('确定要提交核损评估吗？提交后将无法修改', {
                    icon: 3,
                    title: '提示',
                    btn: ['确定', '取消'],
                }, function (index) {
                    $.ajax({
                        url: "${ctx}/claimCaseObjectController/auditPassCarV2",
                        type: 'POST',
                        data: JSON.stringify(claimCaseObject),
                        async: true,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                layer.msg(result.msg || '保司核损操作成功', {
                                    icon: 1,
                                    time: 2000,
                                    shade: [0.0001, '#000']
                                }, function () {
                                    // 提交成功后返回任务列表页面
                                    window.location.href = "${ctx}/claimCaseObjectController/submitConfirm4BS?reportNo=${claimCase.claimCaseNo}&insCode=${claimCase.insCode}&checkCondition=${checkCondition}";
                                });
                            } else {
                                layer.msg(result.msg || '保司核损操作失败', {
                                    icon: 2,
                                    time: 2000,
                                    shade: [0.0001, '#000']
                                },function(){
                                    $("button[name='actionBtn']").removeAttr("disabled");
                                });
                            }

                        },
                        error: function (data) {
                            $("button[name='actionBtn']").removeAttr("disabled");
                            var result = eval("(" + data + ")");
                            console.log(result.msg);
                            layer.msg('网络请求失败，请稍后重试', {icon: 2});
                        }
                    });

                    layer.close(index);
                });
            }

        }

        // 查看定损信息
        function seeLossAssessment(){
            let claimCaseObject = assembleData(status);

            $.ajax({
                url: '${ctx}/claimCaseObject4CarController/seeLossAssessment',
                type: 'POST',
                data: JSON.stringify(claimCaseObject),
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0")  {
                        $("button[name='actionBtn']").removeAttr("disabled");
                        window.open(result.url);
                    }else {
                        $("button[name='actionBtn']").removeAttr("disabled");
                        alert(data.msg);
                    }
                },
                error: function (data) {
                    $("button[name='actionBtn']").removeAttr("disabled");
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

        //组装数据
        function assembleData(status) {


            $("button[name='actionBtn']").attr('disabled','1');


            let category = "${claimCaseObject.category}";

            let isError = false;
            let errorMsg = "";


            let object = {
                id: "${claimCaseObject.id}",
                claimCaseId: "${claimCaseObject.claimCaseId}",
                claimCaseNo: "${claimCaseObject.claimCaseNo}",
                type: "${claimCaseObject.type}",
                category: "${claimCaseObject.category}",
                status: status,
                verifyAmout: "${claimCaseObject.verifyAmout}"
            };


            if(isError){
                layer.msg(errorMsg, {icon: 2, time: 3000},function () {
                    $("button[name='actionBtn']").removeAttr("disabled");
                });
                return;
            }
            console.log(JSON.stringify(object));

            return object;
        }


        window.addEventListener('scroll', function() {
            const box = document.getElementById('stickyBox');
            if (window.scrollY > 200) { // 滚动超过200px时添加额外样式
                box.classList.add('fixed-style');
            } else {
                box.classList.remove('fixed-style');
            }
        });
    </script>
    <style>

        body {
            font-size: 12px;
            margin: 0;
            padding: 0;
        }

        #floating-container {
            position: fixed;
            right: 20px;
            top: 55%;
            z-index: 9999;
            transition: all 0.3s ease;
        }

        .draggable-box {
            width: 60px;
            height: 60px;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 6px !important;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            user-select: none;
            border: 1px solid rgba(0, 0, 0, 0.1);
            font-size: 12px;
            color: #666;
            text-align: center;
            line-height: 1.3;
            padding: 13px;
            box-sizing: border-box;
            margin-bottom: 8px;
            cursor: move;
            transition: all 0.3s ease; /* 添加过渡效果 */

        }

        /* 鼠标悬停效果 - 边框变亮 */
        .draggable-box:hover {
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1); /* 内阴影 */
            transform: scale(1.1);
        }

        /* 选中状态 - 边框变亮且更明显 */
        .box-active {
            background-color: #ffffff;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1); /* 内阴影 */
            transform: translateY(1px); /* 模拟按下 */
        }

        .container {
            display: flex;
            flex-direction: column;
            gap: 5px; /* 设置紧凑的间距 */
        }

        .compact {
            margin: 5px;
            padding: 5px;
        }

        .floating-button {
            text-align: center;
            font-size: 15px;
            padding: 2px 5px;
            color: gray;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 5px;
        }



        .floating-button:hover {
            transform: scale(1.1);
        }



        .receipt-account {
            border: 1px solid #eee;
            padding: 20px;
            border-radius: 4px;
            width: 800px;
        }
        .receipt-account h3 {
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .form-item {
            display: grid;
            /* 一行等分为3列 */
            grid-template-columns: repeat(3, 1fr);
            gap: 20px; /* 列之间的间距 */
        }
        .field {
            display: flex;
            align-items: center;
        }
        .field label {
            width: 100px;
            text-align: right;
            margin-right: 10px;
            color: #666;
        }
        .radio-group {
            display: flex;
            align-items: center;
        }
        .radio-group span {
            margin-right: 15px;
        }
        @media (min-width: 992px) {
            .page-content-wrapper .page-content {
                margin-left: 0px!important;
            }
        }

        /* 修复页面滚动问题 */
        body {
            /*padding-top: 70px !important; !* 为固定头部留出空间 *!*/
            overflow-y: auto !important; /* 确保垂直滚动 */
            height: auto !important;
        }

        .page-container {
            min-height: 100vh;
            overflow-y: auto;
        }

        .page-content-wrapper {
            overflow-y: auto;
            min-height: calc(100vh - 70px);
        }

        .page-content {
            padding-bottom: 50px; /* 底部留出空间 */
            overflow-y: auto;
        }

        .detail-form {
            background: #fff;
            padding: 20px;
            border-radius: 4px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .detail-form .form-group {
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }
        .detail-form .control-label {
            font-weight: bold;
            color: #333;
            text-align: right;
            padding-right: 10px;
            white-space: nowrap;
            min-width: 80px;
            width: 120px;
            flex: 0 0 120px;
        }
        .detail-form .form-control-static {
            padding: 8px 12px;
            margin-bottom: 0;
            min-height: 32px;
            border: 1px solid #e5e5e5;
            border-radius: 4px;
            background-color: #f9f9f9;
            color: #555;
            flex: 1;
            word-break: break-all;
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .section-title {
            border-left: 4px solid #3598dc;
            padding-left: 10px;
            margin: 20px 0 15px 0;
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        .amount-text {
            color: #e74c3c;
            font-weight: bold;
            font-size: 16px;
        }
        .page-header {
            background: #34495e;
            border-bottom: 1px solid #2c3e50;
        }
        .page-header .logo-default {
            color: white !important;
            font-size: 18px !important;
        }

        /* 费用表格样式 */
        .cost-table {
            margin-top: 10px;
        }
        .cost-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
            vertical-align: middle;
        }
        .cost-table td {
            text-align: right;
            vertical-align: middle;
        }
        .cost-table .cost-type-col {
            text-align: left;
        }
        .cost-table .amount {
            color: #000000;
            font-weight: bold;
        }

        /* 备注记录样式 */
        .remark-item {
            border-bottom: 1px solid #eee;
            padding: 15px 0;
        }
        .remark-item:last-child {
            border-bottom: none;
        }
        .remark-type {
            font-weight: bold;
            color: #007bff;
        }
        .remark-meta {
            color: #6c757d;
            font-size: 0.9em;
        }
        .remark-content {
            line-height: 1.6;
            color: #495057;
        }

        /* 确保portlet和其他组件不影响滚动 */
        .portlet {
            overflow: visible !important;
            height: auto !important;
            position: static !important;
            margin-bottom: 20px !important;
            display: block !important;
            visibility: visible !important;
        }

        .portlet .portlet-title {
            overflow: visible !important;
            height: auto !important;
            position: static !important;
            display: block !important;
            visibility: visible !important;
        }

        /* 理算处理按钮样式 */
        .nuclear-loss-btn {
            background-color: #cf3636 !important;
            border-color: #000000 !important;
            color: black !important;
            float: right;
            margin-top: 0px;
            margin-left: 15px;
            display: inline-block;
            font-size: 16px !important;
            padding: 8px 16px !important;
        }
        .nuclear-loss-btn:hover {
            background-color: #000000 !important;
            border-color: #000000 !important;
            color: white !important;
        }

        /* 确保按钮区域可见 */
        .btn {
            margin-right: 10px;
            min-width: 80px;
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: relative !important;
            z-index: 1000 !important;
        }

        /* 确保按钮容器可见 */
        .text-right {
            display: block !important;
            visibility: visible !important;
            overflow: visible !important;
            height: auto !important;
        }

        /* 确保操作按钮区域完全可见 */
        .row:last-child {
            display: block !important;
            visibility: visible !important;
            /*margin-bottom: 50px !important;*/
        }

        .page-sidebar {
            position: static !important;
            overflow: visible !important;
            height: auto !important;
        }

        .page-footer {
            position: static !important;
            overflow: visible !important;
            height: auto !important;
        }

        /* 面包屑导航样式 */
        .page-breadcrumb {
            padding: 0;
            margin: 0 0 25px 0;
            background-color: transparent;
            border-radius: 0;
        }
        .page-breadcrumb > li + li:before {
            content: ">";
            padding: 0 5px;
            color: #777;
        }

        /* 卡片样式优化 */
        .portlet {
            margin-bottom: 25px;
        }
        .portlet .portlet-title {
            padding: 15px 20px;
            min-height: 48px;
            border-bottom: 1px solid #eee;
            position: relative;
        }
        .portlet .portlet-title .caption {
            float: left;
            display: inline-block;
            font-size: 18px;
            line-height: 18px;
        }
        .portlet .portlet-body {
            padding: 2px;
        }

        .portlet.light .portlet-body {
            padding-top: 1px;
        }
        /* 确保按钮区域可见 */
        .portlet .portlet-body {
            min-height: 50px;
        }

        .btn {
            margin-right: 10px;
            min-width: 80px;
        }

        .btn:last-child {
            margin-right: 0;
        }

        /* 强制显示按钮容器 */
        .text-right {
            display: block !important;
            visibility: visible !important;
        }

        .genTask {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #3399ff !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }

        .thumbnail-img {
            width: 50px;
            height: 45px;
            border: 4px solid #D6D6C1;
        }

        .selected-thumbnail-img {
            border: 4px solid #3399ff !important;
        }

        /*里面的代码可以根据自己需求去进行更改*/
        /* 设置滚动条的样式 */
        ::-webkit-scrollbar {
            width: 5px;
        }

        /* 滚动槽 */
        ::-webkit-scrollbar-track {
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
            border-radius: 10px;
        }

        /* 滚动条滑块 */
        ::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
        }

        ::-webkit-scrollbar-thumb:window-inactive {
            background: rgba(255, 0, 0, 0.4);
        }

        .dataDisplayArea > div {
            height: 20%;
            padding: 10px 0px 0px 0px !important;
        }


        .row {
            margin: 0px 0px;
        }


        textarea[class='layui-layer-input'] {
            outline: 0 !important;
            width: 700px !important;
            height: 350px !important;
            padding-left: 10px !important;
            border: 1px solid #e6e6e6 !important;
            color: #333 !important;
            box-shadow: none !important;
        }

        button[name='actionBtn'] {
            color: white;
        }

        select {
            padding: 0px 12px !important;
        }

        div[name='dataCollectAreaRow'] input {
            padding: 0px 5px;
        }

        #thumbnail p {
            margin: 10px 0px 5px -15px;
            font-size: 10px;
            text-align: left;
        }

        .viewer-canvas h2 {
            z-index: 9999999;
            position: relative;
            left: 20px;
            font-weight: bold;
        }

        /* 侧边栏样式 */
        .sidebar-nav {
            width: 100%;
            padding: 10px 0;
            background-color: #f5f5f5;
            border-radius: 4px;
        }

        .nav-list {
            padding-left: 0;
            margin-bottom: 0;
            list-style: none;
        }

        .nav-item {
            border-bottom: 1px solid #e0e0e0;
        }

        .nav-link {
            display: block;
            padding: 8px 15px;
            color: #333;
            text-decoration: none;
        }

        .nav-link:hover {
            background-color: #e9e9e9;
        }

        .nav-link i {
            margin-right: 8px;
            transition: transform 0.3s ease;
        }

        .nav.collapse {
            display: none;
            padding-left: 20px;
        }

        .nav.collapse.in {
            display: block;
        }

        /* 展开状态的箭头旋转 */
        .nav-link[aria-expanded="true"] i.glyphicon-chevron-right {
            transform: rotate(90deg);
        }

        /* 数量标签样式 */
        .nav-link span.badge {
            margin-left: 5px;
            background-color: #777;
        }

        td.amount{
            text-align: right !important;
            vertical-align: middle !important;
        }
        td.cost-type-col {
            text-align: center !important;
            vertical-align: middle !important;
        }

        .center-box {
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            justify-content: center;
            align-items: center;

            /* 添加粘性定位 */
            position: sticky;
            top: 0; /* 固定在顶部 */
            z-index: 1000;

            /* 过渡动画 */
            transition: all 0.3s ease;
        }

        /* 滚动时添加的样式（可选） */
        .fixed-style {
            width: 100%;
            position: fixed;
            border-radius: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        /* 工具提示内容 */
        .tooltip-content {
            position: absolute;
            transform: translateX(-100%);
            padding: 10px;
            margin-bottom: 10px;
            background-color: #333;
            color: #fff;
            border-radius: 4px;
            font-size: 14px;
            line-height: 1.4;
            opacity: 0;
            visibility: hidden;
            z-index: 100;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            text-align: left;
        }

        /* 悬停时显示 */
        .icon-question:hover .tooltip-content {
            opacity: 1;
            visibility: visible;
        }
    </style>
</head>
<body class="page-content-white">



<!-- 页面容器 -->
<div class="page-container">
    <!-- 页面内容包装器 -->
    <div class="page-content-wrapper">
        <!-- 页面内容 -->
        <div class="page-content" style="margin-left: 0;">
            <!-- 面包屑导航 -->
            <div class="page-bar">
                <ul class="page-breadcrumb">
                    <li>
                        <a href="${ctx}/claimCaseObjectController/claimCaseObjectList4BSV2?comeFrom=3&status=4">理算复核</a>
                    </li>
                    <li>任务详情</li>
                </ul>
            </div>

            <!-- 案件信息和快捷操作栏 -->
            <div id="floating-container">
                <div>
                    <div id="box1" class="draggable-box box-active" onclick="showBasicInformation(`${claimCase.id!''}`, `${claimCaseObject.id!''}`)")>
                        基本信息
                    </div>
                </div>
                <div>
                    <div id="box2" class="draggable-box box-active" onclick="showDetailLog()">
                        查看日志
                    </div>
                </div>
                <!--<div>
                    <div id="box3" class="draggable-box box-active" onclick="viewHistoryCases()">
                        历史案件(${historyCaseSize!'0'})
                    </div>
                </div>-->
            </div>

            <!-- 理算处理费用合计 -->
            <div class="row center-box" id="stickyBox" style="margin-bottom: 20px; background-color: #f8f9fa; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6;">
                <div class="row">
                    <div class="col-md-8">
                        <div class="row" style="align-items: center; margin-bottom: 10px;">
                            <div class="col-md-10" style="display: flex; gap: 30px; align-items: center;">
                                <div class="form-group"  style="margin: 0;">
                                    <label class="control-label" style="font-weight: bold;">报案号:</label>
                                    <span style="font-size: 14px;">${claimCase.claimCaseNo!''}</span>
                                </div>
                                <div class="form-group" style="margin-bottom: 0;">
                                    <label class="control-label" style="font-weight: bold;">立案号:</label>
                                    <span style="font-size: 14px;">${claimCase.insuranceCaseNo!''}</span>
                                </div>
                                <div class="form-group" style="margin-bottom: 0;">
                                    <label class="control-label" style="font-weight: bold;">保单号:</label>
                                    <span style="font-size: 14px;">${policyNo!''}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4" style="min-width: 200px; padding-left: 0; padding-right: 0;">
                        <div class="pull-right" style="display: flex; flex-wrap: nowrap; min-width: 180px;">
                            <button type="button" class="btn btn-default btn-sm yellow" onclick="seeLossAssessment()">
                                查看定损信息
                            </button>
                            <button type="button" class="btn btn-default" onclick="submitBack('BAX37')" style="color: #f5f5f5; background-color: #afa7a7; border-color: #fff; margin-right: 10px; white-space: nowrap; flex-shrink: 0;">
                                退回
                            </button>
                            <button type="button" class="btn btn-primary" onclick="submitAudit('BAX36')" style="white-space: nowrap; flex-shrink: 0;">
                                通过
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <!-- 减少卡片内边距和外边距 -->
                        <div class="portlet light bordered" style="padding: 5px 10px; margin-bottom: 10px;">
                            <div class="portlet-title" style="margin-bottom: 5px;">
                                <div class="caption">
                                    <i class="icon-bar-chart font-dark"></i>
                                    <span class="caption-subject font-dark sbold uppercase">损失费用合计</span>
                                </div>
                            </div>
                            <!-- 最小化内容区域内边距 -->
                            <div class="portlet-body" style="padding: 5px 0;">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="portlet-body" style="padding: 0;">
                                            <#if costList?? && (costList?size > 0)>
                                            <div class="table-responsive">
                                                <!-- 表格紧凑样式：减少单元格边距和间距 -->
                                                <table class="table table-bordered cost-table" style="margin-bottom: 0; border-collapse: collapse;">
                                                    <thead>
                                                    <tr style="height: 30px;">
                                                        <th width="5%">类型</th>
                                                        <th width="10%">合计配件金额</th>
                                                        <th width="10%">配件管理费金额</th>
                                                        <th width="10%">合计残值金额</th>
                                                        <th width="10%">合计自付金额</th>
                                                        <th width="8%">合计工时金额</th>
                                                        <th width="10%">合计辅料金额</th>
                                                        <th width="8%">合计外修金额</th>
                                                        <th width="10%">合计施救费用金额</th>
                                                        <th width="10%">总合计(不含其他费用)</th>
                                                        <th width="15%">其他费用
                                                            <i class="icon-question">
                                                                <#if totalCostInfo?? && totalCostInfo != "">
                                                                <div class="tooltip-content">${totalCostInfo!''}</div>
                                                            </#if>
                                                            </i>
                                                        </th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <#list costList as cost>
                                                    <tr style="height: 28px;">
                                                        <td class="cost-type-col" style="padding: 4px 6px;">${cost.assessmentType!''}</td>
                                                        <td class="amount" style="padding: 4px 6px;">${(cost.totalPartsAmount!0)?string("0.00")}</td>
                                                        <td class="amount" style="padding: 4px 6px;">${(cost.partsManagementFee!0)?string("0.00")}</td>
                                                        <td class="amount" style="padding: 4px 6px;">${(cost.totalResidualValue!0)?string("0.00")}</td>
                                                        <td class="amount" style="padding: 4px 6px;">${(cost.totalOutOfPocket!0)?string("0.00")}</td>
                                                        <td class="amount" style="padding: 4px 6px;">${(cost.totalLaborAmount!0)?string("0.00")}</td>
                                                        <td class="amount" style="padding: 4px 6px;">${(cost.totalSuppliesAmount!0)?string("0.00")}</td>
                                                        <td class="amount" style="padding: 4px 6px;">${(cost.totalExternalRepair!0)?string("0.00")}</td>
                                                        <td class="amount" style="padding: 4px 6px;">${(cost.totalRescueFee!0)?string("0.00")}</td>
                                                        <td class="amount" style="padding: 4px 6px; font-weight: bold; color: #e74c3c;">${(cost.totalExcludingRescue!0)?string("0.00")}</td>
                                                        <td class="amount" style="padding: 4px 6px;"><#if cost.otherCostSum??>${cost.otherCostSum?number?string("0.00")}<#else>0.00</#if></td>
                                                    </tr>
                                                    </#list>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <#else>
                                            <div class="alert alert-info" style="padding: 8px 10px; margin-bottom: 0;">
                                                <i class="fa fa-info-circle"></i> 暂无费用明细数据
                                            </div>
                                        </#if>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

            <#--银行账户信息-->
                <div class="row">
                    <div class="col-md-12">
                        <div class="portlet light bordered">
                            <div class="portlet-title">
                                <div class="caption">
                                    <i class="icon-credit-card font-dark"></i>
                                    <span class="caption-subject font-dark sbold uppercase">收款账户信息</span>
                                </div>
                            </div>
                            <#if paymentList ?? && (paymentList?size > 0)>
                            <#list paymentList as objectPayment>
                                <div class="portlet-body">
                                    <div class="form-item">
                                        <div class="field">
                                            <span>账户性质：</span>
                                            <div class="radio-group">
                                                <#if objectPayment.payObjectType == "1">
                                                <span> 非自然人（企业）</span>
                                                <#else>
                                                <span> 自然人（个人）</span>
                                            </#if>
                                        </div>
                                    </div>
                                    <div class="field">
                                        <span>收款人名称：</span>
                                        <span>${objectPayment.name!'-'}</span>
                                    </div>
                                    <div class="field">
                                        <span>收款人证件类型：</span>
                                        <span>${objectPayment.idType}</span>
                                    </div>
                                </div>

                                <!-- 第二行：3个字段 -->
                                <div class="form-item">
                                    <div class="field">
                                        <span>收款人证件号码：</span>
                                        <span>${objectPayment.idNum}</span>
                                    </div>
                                    <div class="field">
                                        <span>证件有效期：</span>
                                        <span>${(objectPayment.idNumEndDate?string("yyyy-MM-dd"))!'长期有效'}</span>
                                    </div>
                                    <div class="field">
                                        <span>收款人联系电话：</span>
                                        <span>${objectPayment.mobile}</span>
                                    </div>
                                </div>

                                <!-- 第三行：3个字段 -->
                                <div class="form-item">
                                    <div class="field">
                                        <span>收款人银行名称：</span>
                                        <span>${objectPayment.bankName}</span>
                                    </div>
                                    <div class="field">
                                        <span>收款人银行支行：</span>
                                        <span>
                                                        <#assign bankInfoObj = bankInfoMap?eval >
                                                        <#assign bankInfo = bankInfoObj[objectPayment.bankInfoId?string] >
                                                        ${bankInfo.bankName!'-'}
                                                    </span>
                                    </div>
                                    <div class="field">
                                        <span>收款人开户名：</span>
                                        <span>${objectPayment.bankAccount}</span>
                                    </div>
                                </div>

                                <!-- 第四行：2个字段（最后一列留空） -->
                                <div class="form-item">
                                    <div class="field">
                                        <span>收款人银行账号：</span>
                                        <span>${objectPayment.bankCard}</span>
                                    </div>
                                    <div class="field">
                                        <span>应付损失金额：</span>
                                        <span>${objectPayment.payAmount}</span>
                                    </div>
                                    <div class="field"></div>
                                </div>
                            </div>
                        </#list>
                        </#if>
                    </div>
                </div>
            </div>
            <!-- 历史案件 -->
            <div id="historyCase" class="row" style="display: none">
                <div class="col-md-12">
                    <div class="portlet light bordered">
                        <div class="portlet-title">
                            <div class="caption">
                                <i class="icon-speech font-dark"></i>
                                <span class="caption-subject font-dark sbold uppercase">历史案件</span>
                            </div>
                        </div>
                        <div class="portlet-body">

                            <#if historyCaseList?? && (historyCaseList?size > 0)>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead>
                                    <tr>
                                        <th width="10%" class="text-center">报案人姓名</th>
                                        <th width="15%" class="text-center">报案人手机号</th>
                                        <th width="10%" class="text-center">出险人姓名</th>
                                        <th width="10%" class="text-center">案件类型</th>
                                        <th width="15%" class="text-center">报案时间</th>
                                        <th width="15%" class="text-center">出险时间</th>
                                        <th width="15%" class="text-center">事故信息描述</th>
                                        <th width="15%" class="text-center">功能</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <#list historyCaseList as case>
                                    <tr>
                                        <td class="text-center">${case.applyName!'-'}</td>
                                        <td class="text-center">${case.applyMobile!'-'}</td>
                                        <td class="text-center">${case.treatName!'-'}</td>
                                        <#if case.caseType??>
                                        <#if case.caseType == "YW">
                                        <td class="text-center">众包</td>
                                        <#elseif case.caseType == "GZ">
                                        <td class="text-center">雇主</td>
                                        <#else>
                                        <td class="text-center">超赔</td>
                                    </#if>
                                    <#else>
                                    <td class="text-center">-</td>
                                    </#if>
                                    <td class="text-center">
                                        <#if case.startDate??>
                                        ${case.startDate?string("yyyy-MM-dd HH:mm:ss")}
                                        <#else>
                                        -
                                    </#if>
                                    </td>
                                    <td class="text-center">
                                        <#if case.treatDate??>
                                        ${case.treatDate?string("yyyy-MM-dd HH:mm:ss")}
                                        <#else>
                                        -
                                    </#if>
                                    </td>
                                    <td class="text-left">${case.description!''}</td>
                                    <td class="text-center">
                                        <button class="btn btn-primary" onclick="seeCaseDetail('${claimCase.id}')">查看详情</button>
                                        <button class="btn btn-primary" onclick="seeVerifyApplyBook('${claimCase.id}')">理赔申请书</button>
                                    </td>
                                    </tr>
                                </#list>
                                </tbody>
                                </table>
                            </div>
                            <#else>
                            <div class="alert alert-info">
                                <i class="fa fa-info-circle"></i> 暂无历史案件
                            </div>
                        </#if>
                    </div>
                </div>
            </div>
            </div>
            <!-- 备注意见 -->
            <div id="logArea" class="row" style="display: none">
                <div class="col-md-12">
                    <div class="portlet light bordered">
                        <div class="portlet-title">
                            <div class="caption">
                                <i class="icon-speech font-dark"></i>
                                <span class="caption-subject font-dark sbold uppercase">查看日志</span>
                            </div>
                        </div>
                        <div class="portlet-body">

                            <#if logList?? && (logList?size > 0)>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead>
                                    <tr>
                                        <th width="15%" class="text-center">操作类型</th>
                                        <th width="15%" class="text-center">金额</th>
                                        <th width="15%" class="text-center">操作人</th>
                                        <th width="20%" class="text-center">操作时间</th>
                                        <th width="35%" class="text-center">处理意见</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <#list logList as log>
                                    <tr>
                                        <td class="text-center">${log.operatingMode!''}</td>
                                        <td class="text-right">
                                            <#if log.money??>
                                            ${log.money}
                                            <#else>
                                            -
                                        </#if>
                                        </td>
                                        <td class="text-center">${log.operator!''}</td>
                                        <td class="text-center">
                                            <#if log.operatorTime??>
                                            ${log.operatorTime}
                                            <#else>
                                            -
                                        </#if>
                                        </td>
                                        <td class="text-left">${log.dealOpinion!''}</td>
                                    </tr>
                                    </#list>
                                    </tbody>
                                </table>
                            </div>
                            <#else>
                            <div class="alert alert-info">
                                <i class="fa fa-info-circle"></i> 暂无日志记录
                            </div>
                        </#if>
                    </div>
                </div>
            </div>
            </div>
            <!-- 影像资料 -->
            <div class="row">
                <div class="col-md-12">
                    <div class="portlet light bordered">
                        <div class="portlet-title">
                            <div class="caption">
                                <i class="icon-picture font-dark"></i>
                                <span class="caption-subject font-dark sbold uppercase">影像资料</span>
                            </div>
                        </div>
                        <div class="portlet-body">
                            <#include "/attach/attachComponent.html">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>