<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">

    <script src="${ctx}/js/genProCity.js" type="text/javascript"></script>
    <script src="${ctx}/js/genVBrand.js" type="text/javascript"></script>
    <link href="${ctx}/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>

    <script type="text/javascript">

        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        $(document).ready(function () {

            $("#all-change").click(function () {
                var isAllChecked=false;

                if($('#all-change').is(':checked')){
                    console.log("已全选");
                    isAllChecked=true;
                }else {
                    console.log("已全部取消");
                    isAllChecked=false;
                }

                var flowIdList= [];
                $("input[name='isSendEmailBox']").each(function (idex,item) {
                    var flowId=$(item).attr("dataFlowId");
                    flowIdList.push(flowId);
                });

                if (isAllChecked) {
                    $("input[name='isSendEmailBox']").prop("checked", true).parent().addClass("checked");

                    $.each(flowIdList, function(index, value) {
                        document.getElementById(value+"separateValue").removeAttribute('disabled');
                        document.getElementById(value+"separateButton").removeAttribute('disabled');
                    });

                }else {
                    $("input[name='isSendEmailBox']").prop("checked", false).parent().removeClass("checked");
                    //取消全选时 恢复原始值
                    $.each(flowIdList, function(index, value) {
                        //三者物损
                        var thirdGoodsAmount = document.getElementById(value+"thirdGoodsAmount").title;
                        //三者车损
                        var thirdCarAmount = document.getElementById(value+"thirdCarAmount").title;
                        //骑手人伤
                        var knightPersonAmount = document.getElementById(value+"knightPersonAmount").title;
                        //三者人伤
                        var thirdPersonAmount = document.getElementById(value+"thirdPersonAmount").title;
                        document.getElementById(value+"thirdGoodsAmount").querySelector('input').value = parseFloat(thirdGoodsAmount);
                        document.getElementById(value+"thirdCarAmount").querySelector('input').value = parseFloat(thirdCarAmount);
                        document.getElementById(value+"knightPersonAmount").querySelector('input').value = parseFloat(knightPersonAmount);
                        document.getElementById(value+"thirdPersonAmount").querySelector('input').value = parseFloat(thirdPersonAmount);

                        var pushAmount = parseFloat(thirdGoodsAmount) + parseFloat(thirdCarAmount) + parseFloat(knightPersonAmount) + parseFloat(thirdPersonAmount);
                        document.getElementById(value+"pushAmount").querySelector('input').value = pushAmount;

                        document.getElementById(value+"separateValue").setAttribute('disabled', 'disabled');
                        document.getElementById(value+"separateButton").setAttribute('disabled', 'disabled');
                    });
                    //changeCount(flowIdList);
                }
            });

            var openWindowWidth = $(document).width() * 0.3 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 100 : $(window).height() / 5 - 20) + "px";

            var text = "${msg}";
            if(text == ""){
                return;
            }
            layer.confirm( text, {
                area: openWindowWidth,
                offset: offsetH,
                title: '提示',
                btn: ['确定'],
                success: function (layero, index) {
                }
            }, function (index){
                layer.close(index);
            });
        });

        //执行系数
        function executionCoefficient(obj) {
            var coefficient = document.getElementById("coefficient").value;
            //系数要求在 0.1-1.2 之间
            var num = parseFloat(coefficient);
            if (num >= 0.1 && num <= 1.2) {
                console.info("coefficient="+coefficient);
            }else{
                var openWindowWidth = $(document).width() * 0.3 + "px";
                var offsetH = ($(window).height() / 5 - 20 > 120 ? 100 : $(window).height() / 5 - 20) + "px";
                var text = "该系数值仅在0.1-1.2之间！";
                layer.confirm( text, {
                    area: openWindowWidth,
                    offset: offsetH,
                    title: '提示',
                    btn: ['确定'],
                    success: function (layero, index) {
                    }
                }, function (index){
                    layer.close(index);
                });
            }

            var flowIdList= [];
            $("input[name='isSendEmailBox']").each(function (idex,item) {
                if($(item).is(':checked')){
                    var flowId=$(item).attr("dataFlowId");
                    flowIdList.push(flowId);
                }
            });

            $.each(flowIdList, function(index, value) {
                //三者物损
                var thirdGoodsAmount = document.getElementById(value+"thirdGoodsAmount").querySelector('input').value;
                var thirdGoodsAmountMultiple = new BigDecimal("0");
                if (thirdGoodsAmount != undefined && thirdGoodsAmount.trim() != '') {
                    thirdGoodsAmountMultiple = thirdGoodsAmount * coefficient;
                    thirdGoodsAmountMultiple = parseFloat(thirdGoodsAmountMultiple).toFixed(2);
                }

                //三者车损
                var thirdCarAmount = document.getElementById(value+"thirdCarAmount").querySelector('input').value;
                var thirdCarAmountMultiple = new BigDecimal("0");
                if (thirdCarAmount != undefined && thirdCarAmount.trim() != '') {
                    thirdCarAmountMultiple = thirdCarAmount * coefficient;
                    thirdCarAmountMultiple = parseFloat(thirdCarAmountMultiple).toFixed(2);
                }

                //骑手人伤
                var knightPersonAmount = document.getElementById(value+"knightPersonAmount").querySelector('input').value;
                var knightPersonAmountMultiple = new BigDecimal("0");
                if (knightPersonAmount != undefined && knightPersonAmount.trim() != '') {
                    knightPersonAmountMultiple = knightPersonAmount * coefficient;
                    knightPersonAmountMultiple = parseFloat(knightPersonAmountMultiple).toFixed(2);
                }

                //三者人伤
                var thirdPersonAmount = document.getElementById(value+"thirdPersonAmount").querySelector('input').value;
                var thirdPersonAmountMultiple = new BigDecimal("0");
                if (knightPersonAmount != undefined && knightPersonAmount.trim() != '') {
                    thirdPersonAmountMultiple = thirdPersonAmount * coefficient;
                    thirdPersonAmountMultiple = parseFloat(thirdPersonAmountMultiple).toFixed(2);
                }

                document.getElementById(value+"thirdGoodsAmount").querySelector('input').value = thirdGoodsAmountMultiple;
                document.getElementById(value+"thirdCarAmount").querySelector('input').value = thirdCarAmountMultiple;
                document.getElementById(value+"knightPersonAmount").querySelector('input').value = knightPersonAmountMultiple;
                document.getElementById(value+"thirdPersonAmount").querySelector('input').value = thirdPersonAmountMultiple;

                var pushAmount = parseFloat(thirdGoodsAmountMultiple) + parseFloat(thirdCarAmountMultiple) + parseFloat(knightPersonAmountMultiple) + parseFloat(thirdPersonAmountMultiple);
                pushAmount = parseFloat(pushAmount).toFixed(2);
                document.getElementById(value+"pushAmount").querySelector('input').value = pushAmount;
            });
            changeCount(flowIdList);
        }

        //执行计算
        function performCalculation(obj) {
            var flowIdList= [];
            $("input[name='isSendEmailBox']").each(function (idex,item) {
                if($(item).is(':checked')){
                    var flowId=$(item).attr("dataFlowId");
                    flowIdList.push(flowId);
                }
            });
            if (flowIdList.length == 0) {
                var openWindowWidth = $(document).width() * 0.3 + "px";
                var offsetH = ($(window).height() / 5 - 20 > 120 ? 100 : $(window).height() / 5 - 20) + "px";
                var text = "当前无可计算的数据，请确认！";
                layer.confirm( text, {
                    area: openWindowWidth,
                    offset: offsetH,
                    title: '提示',
                    btn: ['确定'],
                    success: function (layero, index) {
                    }
                }, function (index){
                    layer.close(index);
                });
            }else{
                changeCount(flowIdList);
            }
        }

        function downDecidedAmount() {
            var result=confirm("是否下载历史已决金额数据");
            if(result){
                //下载已决金额数据
                window.open("${wotoomainuname}/downloadCenterV2Controller/downloadPushCase?insCode=HX&pushTo=2&isFinish=1");
            }
        }

        function downPendingAmount() {
            var result=confirm("是否下载历史未决金额数据");
            if(result){
                //下载未决金额数据
                window.open("${wotoomainuname}/downloadCenterV2Controller/downloadPushCase?insCode=HX&pushTo=2&isFinish=0");
            }
        }


        //单独执行系数
        function separateCoefficient(claimCaseNo) {
            var coefficient = document.getElementById(claimCaseNo+"separateValue").value;
            var flowIdList= [];
            $("input[name='isSendEmailBox']").each(function (idex,item) {
                if($(item).is(':checked')){
                    var flowId=$(item).attr("dataFlowId");
                    flowIdList.push(flowId);
                }
            });
            //系数要求在 0.1-1.2 之间
            var num = parseFloat(coefficient);
            if (num >= 0.1 && num <= 1.2) {
                //三者物损
                var thirdGoodsAmount = document.getElementById(claimCaseNo+"thirdGoodsAmount").querySelector('input').value;
                var thirdGoodsAmountMultiple = new BigDecimal("0");
                if (thirdGoodsAmount != undefined && thirdGoodsAmount.trim() != '') {
                    thirdGoodsAmountMultiple = thirdGoodsAmount * coefficient;
                    thirdGoodsAmountMultiple = parseFloat(thirdGoodsAmountMultiple).toFixed(2);
                }

                //三者车损
                var thirdCarAmount = document.getElementById(claimCaseNo+"thirdCarAmount").querySelector('input').value;
                var thirdCarAmountMultiple = new BigDecimal("0");
                if (thirdCarAmount != undefined && thirdCarAmount.trim() != '') {
                    thirdCarAmountMultiple = thirdCarAmount * coefficient;
                    thirdCarAmountMultiple = parseFloat(thirdCarAmountMultiple).toFixed(2);
                }

                //骑手人伤
                var knightPersonAmount = document.getElementById(claimCaseNo+"knightPersonAmount").querySelector('input').value;
                var knightPersonAmountMultiple = new BigDecimal("0");
                if (knightPersonAmount != undefined && knightPersonAmount.trim() != '') {
                    knightPersonAmountMultiple = knightPersonAmount * coefficient;
                    knightPersonAmountMultiple = parseFloat(knightPersonAmountMultiple).toFixed(2);
                }

                //三者人伤
                var thirdPersonAmount = document.getElementById(claimCaseNo+"thirdPersonAmount").querySelector('input').value;
                var thirdPersonAmountMultiple = new BigDecimal("0");
                if (knightPersonAmount != undefined && knightPersonAmount.trim() != '') {
                    thirdPersonAmountMultiple = thirdPersonAmount * coefficient;
                    thirdPersonAmountMultiple = parseFloat(thirdPersonAmountMultiple).toFixed(2);
                }

                document.getElementById(claimCaseNo+"thirdGoodsAmount").querySelector('input').value = thirdGoodsAmountMultiple;
                document.getElementById(claimCaseNo+"thirdCarAmount").querySelector('input').value = thirdCarAmountMultiple;
                document.getElementById(claimCaseNo+"knightPersonAmount").querySelector('input').value = knightPersonAmountMultiple;
                document.getElementById(claimCaseNo+"thirdPersonAmount").querySelector('input').value = thirdPersonAmountMultiple;

                var pushAmount = parseFloat(thirdGoodsAmountMultiple) + parseFloat(thirdCarAmountMultiple) + parseFloat(knightPersonAmountMultiple) + parseFloat(thirdPersonAmountMultiple);
                pushAmount = parseFloat(pushAmount).toFixed(2);
                document.getElementById(claimCaseNo+"pushAmount").querySelector('input').value = pushAmount;

            } else {
                var openWindowWidth = $(document).width() * 0.3 + "px";
                var offsetH = ($(window).height() / 5 - 20 > 120 ? 100 : $(window).height() / 5 - 20) + "px";
                var text = "该系数值仅在0.1-1.2之间！";
                layer.confirm( text, {
                    area: openWindowWidth,
                    offset: offsetH,
                    title: '提示',
                    btn: ['确定'],
                    success: function (layero, index) {
                    }
                }, function (index){
                    layer.close(index);
                });
            }
            changeCount(flowIdList);
        }

        //取消勾选恢复原始值
        function recoveryOriginal(claimCaseNo) {
            var checkbox = event.target;
            if (checkbox.checked) {
                document.getElementById(claimCaseNo+"separateValue").removeAttribute('disabled');
                document.getElementById(claimCaseNo+"separateButton").removeAttribute('disabled');
            }else{
                document.getElementById(claimCaseNo+"separateValue").setAttribute('disabled', 'disabled');
                document.getElementById(claimCaseNo+"separateButton").setAttribute('disabled', 'disabled');
                //三者物损
                var thirdGoodsAmount = document.getElementById(claimCaseNo+"thirdGoodsAmount").title;
                thirdGoodsAmount = parseFloat(thirdGoodsAmount).toFixed(2);
                //三者车损
                var thirdCarAmount = document.getElementById(claimCaseNo+"thirdCarAmount").title;
                thirdCarAmount = parseFloat(thirdCarAmount).toFixed(2);
                //骑手人伤
                var knightPersonAmount = document.getElementById(claimCaseNo+"knightPersonAmount").title;
                knightPersonAmount = parseFloat(knightPersonAmount).toFixed(2);
                //三者人伤
                var thirdPersonAmount = document.getElementById(claimCaseNo+"thirdPersonAmount").title;
                thirdPersonAmount = parseFloat(thirdPersonAmount).toFixed(2);

                //获取原始值
                document.getElementById(claimCaseNo+"thirdGoodsAmount").querySelector('input').value = thirdGoodsAmount;
                document.getElementById(claimCaseNo+"thirdCarAmount").querySelector('input').value = thirdCarAmount;
                document.getElementById(claimCaseNo+"knightPersonAmount").querySelector('input').value = knightPersonAmount;
                document.getElementById(claimCaseNo+"thirdPersonAmount").querySelector('input').value = thirdPersonAmount;

                var pushAmount = parseFloat(thirdGoodsAmount) + parseFloat(thirdCarAmount) + parseFloat(knightPersonAmount) + parseFloat(thirdPersonAmount);
                pushAmount = parseFloat(pushAmount).toFixed(2);

                document.getElementById(claimCaseNo+"pushAmount").querySelector('input').value = pushAmount;

            }
        }

        //三者物损金额
        function applyRecovery(obj,claimCaseNo) {
            //当前文本框内更改的值
            var thirdGoodsAmount = obj.value;
            var thirdCarAmount = document.getElementById(claimCaseNo+"thirdCarAmount").querySelector('input').value;
            thirdCarAmount = parseFloat(thirdCarAmount).toFixed(2);

            var knightPersonAmount = document.getElementById(claimCaseNo+"knightPersonAmount").querySelector('input').value;
            knightPersonAmount = parseFloat(knightPersonAmount).toFixed(2);

            var thirdPersonAmount = document.getElementById(claimCaseNo+"thirdPersonAmount").querySelector('input').value;
            thirdPersonAmount = parseFloat(thirdPersonAmount).toFixed(2);
            //三者物损
            document.getElementById(claimCaseNo+"thirdGoodsAmount").querySelector('input').value = thirdGoodsAmount;
            //三者车损
            document.getElementById(claimCaseNo+"thirdCarAmount").querySelector('input').value = thirdCarAmount;
            //骑手人伤
            document.getElementById(claimCaseNo+"knightPersonAmount").querySelector('input').value = knightPersonAmount;
            //三者人伤
            document.getElementById(claimCaseNo+"thirdPersonAmount").querySelector('input').value = thirdPersonAmount;

            var pushAmount = parseFloat(thirdGoodsAmount) + parseFloat(thirdCarAmount) + parseFloat(knightPersonAmount) + parseFloat(thirdPersonAmount);
            pushAmount = parseFloat(pushAmount).toFixed(2);
            //合计
            document.getElementById(claimCaseNo+"pushAmount").querySelector('input').value = pushAmount;
        }

        //三者车损金额
        function appraisalRecovery(obj,claimCaseNo) {
            var thirdGoodsAmount = document.getElementById(claimCaseNo+"thirdGoodsAmount").querySelector('input').value;
            thirdGoodsAmount = parseFloat(thirdGoodsAmount).toFixed(2);
            //当前文本框内更改的值
            var thirdCarAmount = obj.value;

            var knightPersonAmount = document.getElementById(claimCaseNo+"knightPersonAmount").querySelector('input').value;
            knightPersonAmount = parseFloat(knightPersonAmount).toFixed(2);

            var thirdPersonAmount = document.getElementById(claimCaseNo+"thirdPersonAmount").querySelector('input').value;
            thirdPersonAmount = parseFloat(thirdPersonAmount).toFixed(2);

            //三者物损
            document.getElementById(claimCaseNo+"thirdGoodsAmount").querySelector('input').value = thirdGoodsAmount;
            //三者车损
            document.getElementById(claimCaseNo+"thirdCarAmount").querySelector('input').value = thirdCarAmount;
            //骑手人伤
            document.getElementById(claimCaseNo+"knightPersonAmount").querySelector('input').value = knightPersonAmount;
            //三者人伤
            document.getElementById(claimCaseNo+"thirdPersonAmount").querySelector('input').value = thirdPersonAmount;

            var pushAmount = parseFloat(thirdGoodsAmount) + parseFloat(thirdCarAmount) + parseFloat(knightPersonAmount) + parseFloat(thirdPersonAmount);
            pushAmount = parseFloat(pushAmount).toFixed(2);
            //合计
            document.getElementById(claimCaseNo+"pushAmount").querySelector('input').value = pushAmount;
        }

        //骑手人伤金额
        function payRecovery(obj,claimCaseNo) {
            var thirdGoodsAmount = document.getElementById(claimCaseNo+"thirdGoodsAmount").querySelector('input').value;
            thirdGoodsAmount = parseFloat(thirdGoodsAmount).toFixed(2);

            var thirdCarAmount = document.getElementById(claimCaseNo+"thirdCarAmount").querySelector('input').value;
            thirdCarAmount = parseFloat(thirdCarAmount).toFixed(2);

            //当前文本框内更改的值
            var knightPersonAmount = obj.value;
            knightPersonAmount = parseFloat(knightPersonAmount).toFixed(2);

            var thirdPersonAmount = document.getElementById(claimCaseNo+"thirdPersonAmount").querySelector('input').value;
            thirdPersonAmount = parseFloat(thirdPersonAmount).toFixed(2);

            //三者物损
            document.getElementById(claimCaseNo+"thirdGoodsAmount").querySelector('input').value = thirdGoodsAmount;
            //三者车损
            document.getElementById(claimCaseNo+"thirdCarAmount").querySelector('input').value = thirdCarAmount;
            //骑手人伤
            document.getElementById(claimCaseNo+"knightPersonAmount").querySelector('input').value = knightPersonAmount;
            //三者人伤
            document.getElementById(claimCaseNo+"thirdPersonAmount").querySelector('input').value = thirdPersonAmount;
            var pushAmount = parseFloat(thirdGoodsAmount) + parseFloat(thirdCarAmount) + parseFloat(knightPersonAmount) + parseFloat(thirdPersonAmount);

            pushAmount = parseFloat(pushAmount).toFixed(2);
            //合计
            document.getElementById(claimCaseNo+"pushAmount").querySelector('input').value = pushAmount;
        }

        //三者人伤金额
        function paidRecovery(obj,claimCaseNo) {
            var thirdGoodsAmount = document.getElementById(claimCaseNo+"thirdGoodsAmount").querySelector('input').value;
            thirdGoodsAmount = parseFloat(thirdGoodsAmount).toFixed(2);

            var thirdCarAmount = document.getElementById(claimCaseNo+"thirdCarAmount").querySelector('input').value;
            thirdCarAmount = parseFloat(thirdCarAmount).toFixed(2);

            var knightPersonAmount = document.getElementById(claimCaseNo+"knightPersonAmount").querySelector('input').value;
            knightPersonAmount = parseFloat(knightPersonAmount).toFixed(2);
            //当前文本框内更改的值
            var thirdPersonAmount = obj.value;
            //三者物损
            document.getElementById(claimCaseNo+"thirdGoodsAmount").querySelector('input').value = thirdGoodsAmount;
            //三者车损
            document.getElementById(claimCaseNo+"thirdCarAmount").querySelector('input').value = thirdCarAmount;
            //骑手人伤
            document.getElementById(claimCaseNo+"knightPersonAmount").querySelector('input').value = knightPersonAmount;
            //三者人伤
            document.getElementById(claimCaseNo+"thirdPersonAmount").querySelector('input').value = thirdPersonAmount;

            var pushAmount = parseFloat(thirdGoodsAmount) + parseFloat(thirdCarAmount) + parseFloat(knightPersonAmount) + parseFloat(thirdPersonAmount);
            pushAmount = parseFloat(pushAmount).toFixed(2);
            //合计
            document.getElementById(claimCaseNo+"pushAmount").querySelector('input').value = pushAmount;
        }

        //计算未决预推金额
        function changeCount(flowIdList) {
            var count =  new BigDecimal("0");
            $.each(flowIdList, function(index, value) {
                var pushAmount = document.getElementById(value+"pushAmount").querySelector('input').value;
                count = count.add(new BigDecimal(pushAmount)).setScale(2, MathContext.ROUND_HALF_UP);
            });
            $("#count").html(count + "元");
            $("#count").attr("sumMoney", count + "元");
            changeDailyPayoutRatio(count);
        }

        //计算当日赔付率
        function changeDailyPayoutRatio(count) {
            //当日赔付率=（当日已决金额+未决推送金额）/当日保费
            var dayDecidedAmount = document.getElementById('dayDecidedAmount').innerText;
            var dayPolicyFee = document.getElementById('dayPolicyFee').innerText;
            var dailyPayoutRatio = count.add(new BigDecimal(dayDecidedAmount)).setScale(2, MathContext.ROUND_HALF_UP);
            if(dayPolicyFee == 0){
                dailyPayoutRatio = new BigDecimal("0");
            }else{
                dailyPayoutRatio = dailyPayoutRatio.divide(new BigDecimal(dayPolicyFee)).setScale(2, MathContext.ROUND_HALF_UP);
                dailyPayoutRatio = dailyPayoutRatio.multiply(new BigDecimal("100"));
            }

            $("#dailyPayoutRatio").html(dailyPayoutRatio + "%");
            $("#dailyPayoutRatio").attr("sumMoney", dailyPayoutRatio + "%");
        }


        function previousStep() {
            window.location.href = "${ctx}/unsettledClaimCasePushController/unsettledClaimCaseList?insCode=${claimCaseVo.insCode}&startDateStart=${claimCaseVo.startDateStart}";
        }

        function prompt(insCode) {
            var flowIdList= [];
            $("input[name='isSendEmailBox']:checked").each(function (idex,item) {
                var flowId=$(item).attr("dataFlowId");
                flowIdList.push(flowId);
            });
            var openWindowWidth = $(document).width() * 0.3 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 100 : $(window).height() / 5 - 20) + "px";

            //推送总金额
            var count =  new BigDecimal("0");

            var  pushVoList = [];
            if(flowIdList.length==0){
                var openWindowWidth = $(document).width() * 0.3 + "px";
                var offsetH = ($(window).height() / 5 - 20 > 120 ? 100 : $(window).height() / 5 - 20) + "px";
                var text = "当前无可推送数据，请确认！";
                layer.confirm( text, {
                    area: openWindowWidth,
                    offset: offsetH,
                    title: '提示',
                    btn: ['确定'],
                    success: function (layero, index) {
                    }
                }, function (index){
                    layer.close(index);
                });

            }
            $.each(flowIdList, function(index, value) {
                //报案号
                var claimCaseNoValue = value;
                //出险人姓名
                var treatNameValue = document.getElementById(value+"treatName").innerText;
                //案件状态
                var statusValue = document.getElementById(value+"status").innerText;
                //出险类型
                var applyTypeListValue = document.getElementById(value+"applyTypeList").innerText;
                //创建时间
                var createTimeValue = document.getElementById(value+"createTime").innerText;

                //三者物损
                let thirdGoodsAmountValue = document.getElementById(value+"thirdGoodsAmount").querySelector('input').value;
                //三者车损
                let thirdCarAmountValue = document.getElementById(value+"thirdCarAmount").querySelector('input').value;
                //骑手人伤
                let knightPersonAmountValue = document.getElementById(value+"knightPersonAmount").querySelector('input').value;
                //三者人伤
                let thirdPersonAmountValue = document.getElementById(value+"thirdPersonAmount").querySelector('input').value;
                //总金额
                let pushAmountValue = document.getElementById(value+"pushAmount").querySelector('input').value;

                count = count.add(new BigDecimal(pushAmountValue)).setScale(2, MathContext.ROUND_HALF_UP);
                thirdGoodsAmountValue = new BigDecimal(thirdGoodsAmountValue).setScale(2, MathContext.ROUND_HALF_UP);
                thirdCarAmountValue = new BigDecimal(thirdCarAmountValue).setScale(2, MathContext.ROUND_HALF_UP);
                knightPersonAmountValue = new BigDecimal(knightPersonAmountValue).setScale(2, MathContext.ROUND_HALF_UP);
                thirdPersonAmountValue = new BigDecimal(thirdPersonAmountValue).setScale(2, MathContext.ROUND_HALF_UP);
                pushAmountValue = new BigDecimal(pushAmountValue).setScale(2, MathContext.ROUND_HALF_UP);
                
                var submitObj = {
                    claimCaseNo: claimCaseNoValue,
                    treatName: treatNameValue,
                    status: statusValue,
                    applyTypeList: applyTypeListValue,
                    createTime: createTimeValue,
                    thirdGoodsAmount: parseFloat(thirdGoodsAmountValue).toFixed(2),
                    thirdCarAmount: parseFloat(thirdCarAmountValue).toFixed(2),
                    knightPersonAmount: parseFloat(knightPersonAmountValue).toFixed(2),
                    thirdPersonAmount: parseFloat(thirdPersonAmountValue).toFixed(2),
                    pushAmount: parseFloat(pushAmountValue).toFixed(2),
                };
                pushVoList.push(submitObj);
            });
            var pushVoJson = JSON.stringify(pushVoList);
            //当年赔付率
            var yearPaymentRate = document.getElementById('yearPaymentRate').innerText;

            //当日赔付率=（当日已决金额+未决推送金额）/当日保费
            var dayDecidedAmount = document.getElementById('dayDecidedAmount').innerText;//当日已决金额
            var dayPolicyFee = document.getElementById('dayPolicyFee').innerText;//当日保费
            var dailyPayoutRatio = count.add(new BigDecimal(dayDecidedAmount));

            if(dayPolicyFee == 0){
                dailyPayoutRatio = new BigDecimal("0");
            }else{
                dailyPayoutRatio = dailyPayoutRatio.divide(new BigDecimal(dayPolicyFee)).setScale(2, MathContext.ROUND_HALF_UP);
                dailyPayoutRatio = dailyPayoutRatio.multiply(new BigDecimal("100"));
            }

            $("#count").html(count + "元");
            $("#count").attr("sumMoney", count + "元");

            $("#dailyPayoutRatio").html(dailyPayoutRatio + "%");
            $("#dailyPayoutRatio").attr("sumMoney", dailyPayoutRatio + "%");

            if(count == 0){
                var text = "当前无可推送数据，请确认！";
                layer.confirm( text, {
                    area: openWindowWidth,
                    offset: offsetH,
                    title: '提示',
                    btn: ['确定'],
                    success: function (layero, index) {
                    }
                }, function (index){
                    layer.close(index);
                });
            }else{
                var text = "当前未决预推送金额为：" + count + "元，<br/>当前当日赔付率为：" + dailyPayoutRatio + "%，<br/>确认完毕请点击推送？";
                layer.confirm( text, {
                    area: openWindowWidth,
                    offset: offsetH,
                    title: '提示',
                    btn: ['确定','取消']
                }, function (index, layero){
                    confirmPush(pushVoJson,insCode,count,yearPaymentRate,dailyPayoutRatio,dayPolicyFee,dayDecidedAmount);

                },function (index){
                    layer.close(index);
                });
            }
        }

        function confirmPush(pushVoJson,insCode,count,yearPaymentRate,dailyPayoutRatio,dayPolicyFee,dayDecidedAmount) {

            var formData = new FormData();
            formData.append("pushVoJson", pushVoJson);
            //保司标识
            formData.append("insCode", insCode);
            //推送总金额
            formData.append("systemAmount", count);
            //当年赔付率
            formData.append("yearPaymentRate", yearPaymentRate);
            //当日赔付率
            formData.append("dailyPayoutRatio", dailyPayoutRatio);
            //当日保费
            formData.append("dayPolicyFee", dayPolicyFee);
            //当日已决金额
            formData.append("dayDecidedAmount", dayDecidedAmount);
            layer.msg("数据推送中，请稍候", {icon: 16, time: 10000, shade: [0.1, '#000']});
            $.ajax({
                url: "${ctx}/unsettledClaimCasePushController/unsettledClaimCaseConfirmPush",
                type: 'POST',
                data:  formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    layer.closeAll("loading");
                    var result = eval("(" + data + ")");
                    if (result.ret === "0") {
                        layer.msg(result.msg,
                            {
                                time: 2000
                            });
                        window.history.back();
                        return false;
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

        function next(obj){
            var confirmationMessage = "该功能会清空已操作数据,请确认当前页面是否有操作数据!!!";
            if (confirm(confirmationMessage)) {
                return true;
            } else {
                return false;
            }
        }

    </script>
    <style>

        .layui-layer-msg {
            z-index: 1989101411 !important;
        }

        /*loader需要的遮罩层css start */
        #screen {
            height: 100%;
            width: 100%;
            z-index: 198910141;
            position: fixed;
            background: rgba(0, 0, 0, 0.3);
            top: 0;
        }

        #screen #screenLoading {
            margin: 0 auto;
            top: 70%;
            transform: translateY(-50%);
            background: greenyellow;
        }

        /*loader需要的遮罩层css end*/

        .clear-padding {
            padding: 0px !important;
        }

        .form-group {
            margin-bottom: 10px !important;
        }

        .widget-thumb .widget-thumb-heading {
            font-size: 16px !important;
        }

        .caption-subject {
            font-weight: 500 !important;
        }

        .btn-bule {
            color: #FFFFFF;
            background-color: #1676FF;
            border-color: #1676FF;
            margin-left: 3%;
        }

        .btn-bule:hover {
            color: #FFFFFF;
        }

        .btn-light-bule {
            color: #3662EC;
            background-color: #DEEBFF;
            border-color: #DEEBFF;
            margin-left: 3%;
        }

        .btn-light-bule:hover {
            color: #3662EC;
        }

        .container-head {
            border-bottom-width: 3px !important;
            padding-left: 5% !important;
            padding-right: 5% !important;
        }

        .container-boby {
            padding-left: 5% !important;
            padding-right: 5% !important;
        }

        .block-show {
            display: flex;
        }

        .block-head-label {
            margin-bottom: 1%;
            font-size: 20px;
        }

        .block-head-label a {
            font-size: 15px;
            margin-left: 10px;
        }

        .block-head-label span {
            font-size: 15px;
        }

        .block-border {
            border-top: 3px solid #EFEFEF;
            border-bottom: 3px solid #EFEFEF;
            border-left: 3px solid #FFFFFF;
            border-right: 3px solid #EFEFEF;
            margin-text-outline: 1.5%;
            margin-bottom: 2%;
        }

        .block-border .col-sm-4 {
            padding: 0px 1px 0px 0px !important;
        }

        .block-border .col-sm-8 {
            padding: 0px 1px 0px 0px !important;
        }

        .block-border .col-sm-12 {
            padding: 0px 1px 0px 0px !important;
        }

        .left-min-5 {
            width: 41.66666%;
            padding-bottom: 2.5%;
            background-color: #EFEFEF;
            padding-top: 2.5%;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-left-min-5 {
            width: 41.66666%;
            border-top: 3px solid #EFEFEF;
            border-bottom: 3px solid #FFFFFF;
        }

        .right-min-7 {
            width: 58.33334%;
            padding-bottom: 2.5%;
            background-color: white;
            padding-top: 2.5%;
            padding-left: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-right-min-7 {
            width: 58.33334%;
            border-top: 3px solid #FFFFFF;
            border-bottom: 3px solid #EFEFEF;
        }

        .left-mid-5 {
            width: 20.83333%;
            padding-bottom: 1.25%;
            background-color: #EFEFEF;
            padding-top: 1.25%;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-left-mid-5 {
            width: 20.83333%;
            border-top: 3px solid #EFEFEF;
            border-bottom: 3px solid #FFFFFF;
        }

        .right-mid-7 {
            width: 79.16667%;
            padding-bottom: 1.25%;
            background-color: #FFFFFF;
            padding-top: 1.25%;
            padding-left: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-right-mid-7 {
            width: 79.16667%;
            border-top: 3px solid #FFFFFF;
            border-bottom: 3px solid #EFEFEF;
        }


        .left-max-5 {
            width: 13.88888%;
            padding-bottom: 0.833%;
            background-color: #EFEFEF;
            padding-top: 0.833%;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-left-max-5 {
            width: 13.88888%;
            border-top: 3px solid #EFEFEF;
            border-bottom: 3px solid #FFFFFF;
        }

        .right-max-7 {
            width: 86.11112%;
            padding-bottom: 0.833%;
            background-color: #FFFFFF;
            padding-top: 0.833%;
            padding-left: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-right-max-7 {
            width: 86.11112%;
            border-top: 3px solid #FFFFFF;
            border-bottom: 3px solid #EFEFEF;
        }


        .attachInfo {
            border: 1px solid #D8D8D8;
            padding: 0px 3.5% 20px;
            margin-bottom: 2%;
        }

        .attachInfo label {
            margin-top: 20px;
        }

        .attachInfo img {
            width: 100%;
            height: 100%;
            border: 1px solid;
        }

        .attachInfo .col-sm-2 {
            margin-top: 20px !important;
        }

        .attachInfo .icon-attach {
            display: block;
            position: absolute;
            background-color: #D8D8D8;
            color: #709BF3;
            width: 20px;
            height: 25px;
            right: 1px;
            top: 1px;
            text-align: center;
            line-height: 25px;
            font-size: 10px;
        }

        .payDutyInfo {
            margin-bottom: 2%;
        }

        .payDutyInfo .col-sm-5 {
            display: block;
            margin-bottom: 20px;;
            font-weight: bold;
            font-size: 17px;
            height: 210px;
        }

        .payDutyInfo .notice {
            width: 100%;
            height: 100%;
            border-radius: 10px;
            color: white;
            padding: 3%;
            border: 1px solid #000;
        }

        .notice-no-deal {
            background-color: #EE3232;
        }

        .notice-deal {
            background-color: #3662EC;
        }

        .notice-hangup {
            background-color: #F49929;
        }

        .notice-default {
            background-color: darkgrey;
        }


        .logInfo {
            margin-bottom: 2%;
        }

        .logInfo button {
            margin-top: 20px;
            margin-right: 30px;
        }

        .logListInfo {
            margin-bottom: 2%;
        }

        .logListInfo table {
            border: 1px solid #C2C2C2;
        }

        .logListInfo .detailsInfo {
            display: none;
            padding-left: 8%;
        }

        .minus {
            border: 1px solid black;
            border-radius: 50%;
            width: 13px;
            height: 13px;
            position: relative;
            margin-top: 3%;
        }

        .minus::before {
            content: '';
            position: absolute;
            left: 2px;
            top: 5px;
            width: 7px;
            border-top: 1px solid;
        }

        #reSendMessageContainer .radio {
            margin-left: 0px;
        }

        .logListInfo .detailsInfo > td {
            word-wrap: break-word;
            word-break: break-all;
            text-align: left;
        }

        .logListInfo .rowInfo:hover {
            cursor: pointer;
        }

        .label-title {
            font-size: 22px !important;
        }

        .subject-span {
            display: inline-block;
            margin-right: 40px;
            margin-top: 12px;
        }

        .subject-text-overflow {
            display: inline-block;
            max-width: 50%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            vertical-align: bottom;
        }

        .span-type {
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin: 3px;
            padding: 2px;
        }

        .dutyInfoArea th,
        .dutyInfoArea td {
            text-align: center;
            border-top: 1px solid #C2C2C2 !important;
            border-bottom: 1px solid #C2C2C2 !important;
        }

        td > a {
            display: inline-block;
            margin: 3px;
        }
    </style>
</head>
<body>

<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>新职业平台</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">未决推送</span></li>
                </ul>
            </div>
            <div class="portlet-body container-boby">
                <div class="row">
                    <div class="row block-border">
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="全年赔付率">全年赔付率：</div>
                                    <div class="right-min-7" style="color: red">
                                        <span id="yearPaymentRate">${(pdsProposalReq.yearPaymentRate?string('0.00'))!''}</span>
                                        <span>%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="全年保费">全年保费：</div>
                                    <div class="right-min-7">
                                        <span id="yearPolicyFee">${pdsProposalReq.yearPolicyFee!'0'}</span><span>元</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="历史已决金额" ><a onclick="downDecidedAmount()">历史已决金额：</a></div>
                                    <div class="right-min-7">
                                        <span id="decidedAmount">${pdsProposalReq.decidedAmount!'0'}</span><span>元</span>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <#--                        下划线-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="历史未决金额" ><a onclick="downPendingAmount()">历史未决金额：</a></div>
                                    <div class="right-min-7">
                                        <span id="pendingAmount">${pdsProposalReq.pendingAmount!'0'}</span><span>元</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="当日赔付率">当日赔付率：</div>
                                    <div class="right-min-7" style="color: red">
                                        <span id="dailyPayoutRatio" sumMoney="${(pdsProposalReq.dailyPayoutRatio?string('0.00'))!''}">${(pdsProposalReq.dailyPayoutRatio?string('0.00'))!''}</span>

                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="当日保费">当日保费：</div>
                                    <div class="right-min-7">
                                        <span id="dayPolicyFee">${pdsProposalReq.dayPolicyFee!'0'}</span><span>元</span>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <#--                        下划线-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div>
                                <div class="col-sm-4">
                                    <div class="block-show">
                                        <div class="left-min-5" title="当日已决金额">当日已决金额：</div>
                                        <div class="right-min-7" >
                                            <span id="dayDecidedAmount">${pdsProposalReq.dayDecidedAmount!'0'}</span><span>元</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-8">
                                    <div class="block-show">
                                        <div class="left-mid-5" title="未决预推金额">未决预推金额：</div>
                                        <div class="right-mid-7" >
                                            <span id="count" sumMoney="${(count?string('0.00'))!''}">${(count?string('0.00'))!''}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<form id="searchForm" action="${ctx}/unsettledClaimCasePushController/unsettledClaimCasePush"
      class="form-horizontal" enctype="multipart/form-data" onsubmit="return next(this)">
    <input id="pageNum" name="pageNum" type="hidden" value="${page.pageNum}"/>
    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
    <input type="hidden" name="insCode" id="insCode" value="${claimCaseVo.insCode}">
    <div>
        <div class="col-sm-2">
            <div class="form-group">
                <input type="text" class="form-control" name="coefficient" id="coefficient"
                       placeholder="请输入0.1-1.2系数值"/>
            </div>
        </div>
        <div class="col-sm-8">
            <button type="button" class="btn green" onclick="executionCoefficient(this)">执行系数</button>
            <button type="button" class="btn green" style="margin-left: 20px;"
                    onclick="previousStep()">上一步
            </button>
            <button type="button" class="btn btn-info" style="margin-left: 20px;"
                    onclick="prompt('${claimCaseVo.insCode}')">确认
            </button>
            <button type="button" class="btn green" style="margin-left: 20px;"
                    onclick="performCalculation(this)">执行计算
            </button>
        </div>
    </div>
</form>

<table class="table table-striped table-bordered table-hover table-header-fixed">
    <thead>
    <tr>
        <th width="6%">
            <span>
                <input type="checkbox" id="all-change">全选
            </span>
        </th>
        <th width="6%">报案号</th>
        <th width="6%">出险人姓名</th>
        <th width="6%">三者物损金额</th>
        <th width="6%">三者车损金额</th>
        <th width="6%">骑手人伤金额</th>
        <th width="6%">三者人伤金额</th>
        <th width="6%">总金额</th>
        <th width="6%">案件状态</th>
        <th width="8%">系数</th>
    </tr>
    </thead>
    <tbody>
    <#list page.list as claimCase>
    <tr id="${claimCase.claimCaseNo}">
        <td><input value="${claimCase.claimCaseNo}" type="checkbox" name="isSendEmailBox" dataFlowId="${claimCase.claimCaseNo}" onclick="recoveryOriginal('${claimCase.claimCaseNo}')"></td>
        <td>${claimCase.claimCaseNo}</td>
        <td id="${claimCase.claimCaseNo}treatName">${claimCase.treatName}</td>
        <td id="${claimCase.claimCaseNo}thirdGoodsAmount" title="${claimCase.thirdGoodsAmount!'0'}">
            <input type="text" class="form-control" value="${claimCase.thirdGoodsAmount!'0'}" onchange="applyRecovery(this,'${claimCase.claimCaseNo}')"<#if claimCase.thirdGoodsAmount == null>disabled</#if>>
        </td>
        <td id="${claimCase.claimCaseNo}thirdCarAmount" title="${claimCase.thirdCarAmount!'0'}">
            <input type="text" class="form-control" value="${claimCase.thirdCarAmount!'0'}" onchange="appraisalRecovery(this,'${claimCase.claimCaseNo}')"<#if claimCase.thirdCarAmount == null>disabled</#if>>
        </td>
        <td id="${claimCase.claimCaseNo}knightPersonAmount" title="${claimCase.knightPersonAmount!'0'}">
            <input type="text" class="form-control" value="${claimCase.knightPersonAmount!'0'}" onchange="payRecovery(this,'${claimCase.claimCaseNo}')" <#if claimCase.knightPersonAmount == null>disabled</#if>>
        </td>
        <td id="${claimCase.claimCaseNo}thirdPersonAmount" title="${claimCase.thirdPersonAmount!'0'}">
            <input type="text" class="form-control" value="${claimCase.thirdPersonAmount!'0'}" onchange="paidRecovery(this,'${claimCase.claimCaseNo}')" <#if claimCase.thirdPersonAmount == null>disabled</#if>>
        </td>
        <td id="${claimCase.claimCaseNo}pushAmount" title="${claimCase.pushAmount!'0'}">
            <input type="text" class="form-control"  value="${claimCase.pushAmount!'0'}" disabled/>
        </td>
        <td id="${claimCase.claimCaseNo}status" class="td-overflow" title="${claimCaseStatusEumMap.get(claimCase.status).msg}">
            ${claimCaseStatusEumMap.get(claimCase.status).msg}
        </td>
        <td class="td-overflow">
            <div class="col-sm-6" style="padding-left: 0;" >
                <input type="text" class="form-control" name="coefficient" id="${claimCase.claimCaseNo}separateValue" disabled/>
            </div>
            <button type="button" class="btn green" onclick="separateCoefficient(('${claimCase.claimCaseNo}'))" id="${claimCase.claimCaseNo}separateButton" disabled>执行</button>
        </td>
        <td hidden id="${claimCase.claimCaseNo}applyTypeList">${claimCase.applyTypeList}</td>
        <td hidden id="${claimCase.claimCaseNo}createTime">${claimCase.createTime?string('yyyy-MM-dd HH:mm:ss')!'--'}</td>
    </tr>
    </#list>
    </tbody>
</table>

<div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
    <div class="modal-dialog">
        <img src="${ctx}/images/load.gif">
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>
