<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/layui/css/layui.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>

    <script src="${ctx}/metronic/layouts/layout4/scripts/layout.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layui/layui.js" type="text/javascript"></script>

    <script type="text/javascript">
        $(document).ready(function () {

            $("#all-change").click(function () {
                var isAllChecked=false;

                if($('#all-change').is(':checked')){
                    isAllChecked=true;
                }else {
                    isAllChecked=false;
                }

                if (isAllChecked) {
                    $("input[name='isSendEmailBox']").prop("checked", true).parent().addClass("checked");
                }else {
                    $("input[name='isSendEmailBox']").prop("checked", false).parent().removeClass("checked");
                }
            });

        });

        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }


        function startPush(insCode) {
            var startDateStart = $("#startDateStart").val();
            if(startDateStart !== null && startDateStart !== '' && startDateStart !== undefined){
                window.location.href = "${ctx}/unsettledClaimCasePushController/unsettledClaimCasePush?insCode="+insCode+"&startDateStart="+startDateStart;
            }
        }


    </script>
    <style>

        .select2-dropdown {
            z-index: 19891099 !important;
        }

        /*loader需要的遮罩层css start */
        #screen {
            height: 100%;
            width: 100%;
            z-index: 198910141;
            position: fixed;
            background: rgba(0,0,0,0.3);
            top:0;
        }

        #screen #screenLoading{
            margin: 0 auto;
            top: 40%;
            transform: translateY(-50%);
            background: greenyellow;
        }
        /*loader需要的遮罩层css end*/

        .clear-padding {
            padding: 0px !important;
        }

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
            border-right: 0;
            overflow: hidden;
            vertical-align: bottom;
            padding: 0px;
            display: inline-block;
        }
        .form-active > div > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
            display: flex;
        }
        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 70px 6px 30px;
            font-size: 17px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
            border-right: 1px solid #e7ecf1;
        }
        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }
        .li-blue {
            background: #0b94ea ;
            color: #fff;
        }
        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }
        td > a {
            display: inline-block;
            margin: 3px;
        }

        .td-overflow {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 100px;
        }
        .labelGroup span{
            margin-left: 5px;
            margin-right: 5px;
            background-color: #1676FF;
            color: white;
            border-radius: 2px;
        }

        .btn-bule {
            color: #FFFFFF;
            background-color: #1676FF;
            border-color: #1676FF;
            margin-left: 3%;
        }

        input[type="radio"] {
            position: relative !important;
            margin: 0px !important;
        }

        .row-other-class {
            margin: 20px;
            display: flex;
            align-items: center;
        }
        a{
            color: #337ab7;
        }

    </style>
</head>
<body>

<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>新职业平台</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">SK未决推送</span></li>
                </ul>
            </div>
            <div class="portlet-body">

                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal"
                      action="${ctx}/unsettledClaimCasePushController/unsettledClaimCaseList"
                      method="post" enctype="multipart/form-data">
                    <input id="pageNum" name="pageNum" type="hidden" value="${page.pageNum}"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <input type="hidden" name="insCode" id="insCode" value="${claimCaseVo.insCode}">

                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">创建时间：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control form-control-inline date-picker" data-date-format="yyyy-mm-dd"  name="startDateStart" id="startDateStart"
                                               value="${claimCaseVo.startDateStart}" placeholder="请选择创建时间"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group pull-right" style="margin-bottom: 10px;margin-right: 30px;">
                                    <button id="query" type="submit" class="btn btn-info" style="margin-bottom: 10px;">查询</button>
                                    <button type="button" class="btn green" style="margin-left: 10px;" onclick="startPush('${claimCaseVo.insCode}')">下一步</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th>报案号</th>
                        <th>创建时间</th>
                        <th>出险人姓名</th>
                        <th>出险类型</th>
                        <th>三者物损金额</th>
                        <th>三者车损金额</th>
                        <th>骑手人伤金额</th>
                        <th>三者人伤金额</th>
                        <th>案件状态</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as claimCase>
                    <tr>
                        <td>${claimCase.claimCaseNo}</td>
                        <td>${claimCase.createTime?string('yyyy-MM-dd HH:mm:ss')!'--'}</td>
                        <td>${claimCase.treatName}</td>
                        <td>
                            <#list claimCase.applyTypeList as applyType>
                                <#if applyType?contains("1-1")>
                                    <span class="span-type">骑手人伤</span>
                                </#if>
                                <#if applyType?contains("2-1")>
                                    <span class="span-type">三者人伤</span>
                                </#if>
                                <#if applyType?contains("2-2")>
                                    <span class="span-type">三者物损</span>
                                </#if>
                                <#if applyType?contains("2-3")>
                                    <span class="span-type">三者车损</span>
                                </#if>
                            </#list>
                        </td>
                        <td>${claimCase.thirdGoodsAmount!'--'}</td>
                        <td>${claimCase.thirdCarAmount!'--'}</td>
                        <td>${claimCase.knightPersonAmount!'--'}</td>
                        <td>${claimCase.thirdPersonAmount!'--'}</td>
                    <!--<td>${claimCase.totalAmount}</td><span class="span-type">${applyType}</span>-->

                        <td class="td-overflow" title="${claimCaseStatusEumMap.get(claimCase.status).msg}">
                            ${claimCaseStatusEumMap.get(claimCase.status).msg}
                        </td>
                    </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>