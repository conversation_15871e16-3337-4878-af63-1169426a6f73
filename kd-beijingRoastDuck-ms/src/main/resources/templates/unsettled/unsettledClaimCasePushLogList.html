<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        $(document).ready(function () {
        });

        const loader =  new Loaders({style:"rectangle"});

        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        function pushLogDetail(id) {
            window.location.href = "${ctx}/unsettledClaimCasePushController/claimCasePushOtherBatchLogDetail?id=" + id;
        }


        // 计算滚动高度
        function calculationScrollTop() {
            var ifm = parent.document.getElementById("contentFrame");
            scrollTop = $(window.parent).scrollTop() + 150;
            if (!ifm) {
                scrollTop = 300;
            }
            return scrollTop + "px";
        }


    </script>
    <style>

        .select2-results__group {
            font-size: 17px !important;
            color: #000 !important;
            line-height: 20px !important;
            padding: 8px 10px !important;
        }

        .select2-container--bootstrap .select2-results__option .select2-results__option {
            padding: 6px 20px !important;
        }

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
        }

        .form-active > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
        }

        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 22px;
            font-size: 16px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
        }

        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }

        .li-default:nth-of-type(2) {
            border-left: 1px solid #e7ecf1;
            border-right: 1px solid #e7ecf1;
        }

        .li-default:nth-of-type(3) {
            border-right: 1px solid #e7ecf1;
        }

        .li-blue {
            background: #0b94ea;
            color: #fff;
        }

        .labelGroup span {
            margin-left: 5px;
            margin-right: 5px;
            background-color: #1676FF;
            color: white;
            border-radius: 2px;
        }

        .applyTypeGroup span {
            margin-left: 5px;
            margin-right: 5px;
            background-color: #c9c8c8;
            border-radius: 2px;
        }

        .applyTypeGroup .AA001, .AA002, .AA003, .AA004 {
            background-color: #1676ff !important;
            color: white;
        }

        .applyTypeGroup .AB001, .AB002, .AB003, .AB004 {
            background-color: #ff6633 !important;
            color: white;
        }

        .applyTypeGroup .AC001, .AC002, .AC003, .AC004 {
            background-color: #CA0000 !important;
            color: white;
        }

        .td-overflow {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 200px;
        }

        td:hover {
            cursor: pointer;
        }

        td > a, td > span {
            display: inline-block;
            margin: 3px;
        }

        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin: 3px;
            padding: 2px;
        }

        .logListInfo {
            margin-bottom: 2%;
        }

        .logListInfo table {
            border: 1px solid #C2C2C2;
        }

        .logListInfo .detailsInfo {
            display: none;
            padding-left: 8%;
        }

        .minus {
            border: 1px solid black;
            border-radius: 50%;
            width: 13px;
            height: 13px;
            position: relative;
            margin-top: 3%;
        }

        .minus::before {
            content: '';
            position: absolute;
            left: 2px;
            top: 5px;
            width: 7px;
            border-top: 1px solid;
        }

        .logListInfo .detailsInfo > td {
            word-wrap: break-word;
            word-break: break-all;
            text-align: left;
        }

        .logListInfo .rowInfo:hover {
            cursor: pointer;
        }

        .label-title {
            font-size: 18px !important;
        }
    </style>
</head>
<body>

<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>饿了么</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">SK未决推送日志</span></li>
                </ul>
            </div>
            <div class="portlet-body">

                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal"
                      action="${ctx}/unsettledClaimCasePushController/claimCasePushOtherBatchLogList"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">推送日期：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control form-control-inline date-picker"
                                               data-date-format="yyyy-mm-dd" name="pushDataStr" id="pushDataStr"
                                               value="${pushDataStr}" placeholder="请选择推送日期"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group pull-right" style="margin-bottom: 10px;">
                                    <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;">查询
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th>日期</th>
                        <th>预推案件数</th>
                        <th>成功案件数</th>
                        <th>成功金额</th>
                        <th>全年赔付率</th>
                        <th>当日赔付率</th>
                        <th>功能</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as vo>
                        <tr>
                            <td>${(vo.pushDate?string('yyyy-MM-dd HH:mm:ss'))!'--'}</td>
                            <td>${vo.pushNum}</td>
                            <td>${vo.successNum}</td>
                            <td>${vo.successPushAmount}</td>
                            <td>${vo.yearpPaymentRate}<span>%</span></td>
                            <td>${vo.dayPaymentRate}<span>%</span></td>
                            <td>
                                <a onclick="pushLogDetail('${vo.id}')">推送清单</a>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>