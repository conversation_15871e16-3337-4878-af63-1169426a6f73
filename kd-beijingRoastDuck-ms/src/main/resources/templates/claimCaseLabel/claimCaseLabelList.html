<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/layui/css/theme.css" rel="stylesheet" type="text/css" />

    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet"
        type="text/css" />

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>

    <link href="${ctx}/metronic/layui/css/layui.css" rel="stylesheet" type="text/css" />
    <script src="${ctx}/metronic/layui/layui.js" type="text/javascript"></script>

    <script type="text/javascript">
        const loader = new Loaders({ style: "rectangle" });

        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        $(document).ready(function () {
            // 其他select2初始化
            $("#applyType").select2({
                placeholder: "请选择",
                width: null
            });
            $("#insCode").select2({
                placeholder: "请选择",
                allowClear: true,
                width: null
            });
            $("#status").select2({
                placeholder: "请选择",
                allowClear: true,
                width: null
            });
            var nowInsCode = [];
            <#if claimCaseLabelVo.insCode?exists>
                <#list claimCaseLabelVo.insCode?split(",") as code>
                nowInsCode.push("${code}");
            </#list>
            console.log(nowInsCode);
            $("#insCode").val(nowInsCode).trigger('change');
            </#if >
            // 业务标签 select2初始化（labelMap）
            var labelList = [];
            <#if labelMap?? && (labelMap ? size > 0) >
                <#list labelMap.keySet() as key >
                    labelList.push({ id: '${key}', text: '${labelMap.get(key).msg}' });
                </#list >
            </#if >
                $("#label").select2({
                    placeholder: "请选择",
                    width: '100%',
                    data: labelList,
                    allowClear: true
                });
            // 标签回显
            $("#label").val("${claimCaseLabelVo.label!''}").trigger("change");

            $("input[type=checkbox]").hide();
            $(".checker span").css("background-image", "none");

        });

        function changeInsCode(obj) {
            var insCode = $(obj).val();
            console.log(insCode);
            $("#insCode").val(insCode);
        }

        // 新增重置表单函数
        function resetForm() {
            // 重置表单
            $('#searchForm')[0].reset();
            // select2控件需要手动清空
            $('#label').val('').trigger('change');
            $('#insCode').val(null).trigger('change');
            $('#status').val('').trigger('change');
        }

        // 滑动按钮状态更新函数
        function updateStatus(id, status) {
            console.log("id:", id, " status:", status);

            // 1. 获取当前行的生效时间（通过data-id匹配）
            let currentEffectiveTime = "";
            // 查找所有生效时间单元格，筛选出与当前id匹配的
            const timeCells = document.querySelectorAll('.effective-time-td');
            timeCells.forEach(cell => {
                if (cell.getAttribute('data-id') === id) {
                    currentEffectiveTime = cell.textContent.trim(); // 得到当前生效时间文本
                }
            });
            console.log("当前生效时间:", currentEffectiveTime);

            let newTime;
            if (status === 1) {
                newTime = currentEffectiveTime;
            }

            // 调用后端接口更新状态
            layer.msg("提交中..", {
                icon: 16,
                time: -1,
                shade: [0.3, '#000']
            });
            $.ajax({
                url: `${ctx}/claimCaseLabelController/updateLabelStatus?id=` + id + '&status=' + status + '&newTime=' + newTime,
                type: 'POST',
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (res) {
                    var result = eval("(" + res + ")");
                    if (result.ret == "0") {
                        layer.msg(result.msg, {
                            icon: 1,
                            time: 2000,
                            shade: [0.1, '#000']
                        }, function () {
                            location.reload();
                        });

                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 2000,
                            shade: [0.1, '#000']
                        });
                    }
                },
                error: function () {
                    layer.msg('请求失败，请稍后重试', { icon: 2, time: 2000 });
                }
            });
        }

        // 新增推送标签按钮事件
        function addLabel() {
            layer.open({
                type: 2,
                title: '新增推送标签配置',
                content: `${ctx}/claimCaseLabelController/saveLabel`,
                area: ['80%', '80%'],
                success: function (res) {
                }
            });
        }

        // 编辑标签按钮事件
        function editLabel(id) {
            console.log(id)
            layer.open({
                type: 2,
                title: '编辑推送标签配置',
                content: `${ctx}/claimCaseLabelController/editLabel?id=` + id,
                area: ['80%', '80%'],
                success: function (res) {
                }
            });
        }

        function deleteLabel(id, status) {
            layer.confirm('确定删除？删除后将无法找回配置', {
                icon: 3,
                title: '提示',
                btn: ['确定', '取消'] // 按钮
            }, function (index) {
                // 用户点击了“确定”
                $.ajax({
                    url: `${ctx}/claimCaseLabelController/updateLabelStatus?id=` + id + '&status=' + status,
                    type: 'POST',
                    success: function (res) {
                        var result = eval("(" + res + ")");
                        if (result.ret == "0") {
                            layer.msg(result.msg, {
                                icon: 1,
                                time: 2000,
                                shade: [0.0001, '#000']
                            }, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(result.msg, {
                                icon: 2,
                                time: 2000,
                                shade: [0.0001, '#000']
                            });
                        }
                    },
                    error: function () {
                        layer.msg('请求失败，请稍后重试', { icon: 2 });
                    }
                });
                layer.close(index); // 关闭确认框
            });
        }

        layui.use('form', function () {
            var form = layui.form;
            var layer = layui.layer;
            // checkbox 事件
            form.on('switch(checkbox-filter)', function (data) {
                var elem = data.elem; // 获得 checkbox 原始 DOM 对象
                var checked = elem.checked == true ? 1 : 0; // 获得 checkbox 选中状态
                var id = $(elem).attr("data-id"); // 获得 checkbox 值
                updateStatus(id, checked);
            });
        });
    </script>
    <style>
        #screen #screenLoading {
            margin: 0 auto;
            top: 40%;
            transform: translateY(-50%);
            background: greenyellow;
        }

        .form-active>div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
            border-right: 0;
            overflow: hidden;
            vertical-align: bottom;
            padding: 0px;
            display: inline-block;
        }

        .form-active>div>ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
            display: flex;
        }

        td>a {
            display: inline-block;
            margin-right: 10px;
        }

        .labelGroup span {
            margin-left: 5px;
            margin-right: 5px;
            background-color: #1676FF;
            color: white;
            border-radius: 2px;
        }

        /* 新增：表单横向不换行样式 */
        .form-group.flex-nowrap {
            display: flex;
            flex-wrap: nowrap;
        }

        .form-group.flex-nowrap label {
            margin-bottom: 0;
            white-space: nowrap;
            flex: 0 0 auto;
        }

        .form-group.flex-nowrap .form-control {
            flex: 1 1 0%;
            min-width: 0;
        }

        /* 表格表头和内容居中 */
        table th,
        table td {
            text-align: center;
            vertical-align: middle;
        }

        /* 操作列内容左对齐 */
        table td:last-child {
            text-align: left;
        }

        /* 最后修改原因 - 截断显示样式 */
        .td-modifyReason {
            cursor: help;
            /* 提示用户可悬浮查看完整内容 */
        }

        /* 按钮颜色 */
        .layui-form-onswitch {
            background-color: #1676FF !important;
            border-color: #1676FF !important;
        }
    </style>
</head>

<body>
    <div class="row">
        <div class="col-sm-12">
            <div class="portlet light portlet-fit bordered">
                <div class="portlet-title">
                    <ul class="page-breadcrumb breadcrumb">
                        <li><span>新职业平台</span> <i class="fa fa-circle"></i></li>
                        <li><span class="active">推送标签配置</span></li>
                    </ul>
                </div>
                <div class="portlet-body">
                    <!-- BEGIN FORM-->
                    <form id="searchForm" class="form-horizontal"
                        action="${ctx}/claimCaseLabelController/claimCaseLabelList" method="post">
                        <input id="pageNum" name="pageNum" type="hidden" value="1" />
                        <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}" />
                        <div class="form-body">
                            <div class="row" style="margin-left:0;margin-right:0;">
                                <div class="col-sm-3">
                                    <div class="form-group ">
                                        <label class="control-label col-sm-3" style="padding-right: 0;">标签名称：</label>
                                        <div class="col-sm-8" style="padding-left: 0;">
                                            <select style="width: 100%;" id="label" name="label" class="form-control">
                                                <option value="">请选择</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-3">
                                    <div class="form-group">
                                        <label class="control-label col-sm-3" style="padding-right: 0">推送保司：</label>
                                        <div class="col-sm-8" style="padding-left: 0;">
                                            <select class="form-control select2-multiple" name="insCode" id="insCode"
                                                multiple onchange="changeInsCode(this)" style="width: 200px">
                                                <#if insCodeMap?exists>
                                                    <#list insCodeMap.keySet() as key>
                                                        <option value="${key}">${insCodeMap.get(key)}</option>
                                                    </#list>
                                                </#if>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-3">
                                    <div class="form-group">
                                        <label class="control-label col-sm-5"
                                            style="padding-right: 0; white-space: nowrap;">是否推送：</label>
                                        <div class="col-sm-7" style="padding-left: 0;">
                                            <select class="form-control" name="status" id="status" value=""
                                                style="width: 100px;">
                                                <option value="">请选择</option>
                                                <option value="1" <#if claimCaseLabelVo.status==1>selected</#if>>是
                                                </option>
                                                <option value="0" <#if claimCaseLabelVo.status==0>selected</#if>>否
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-3" style="text-align: right;">
                                    <button id="query" type="submit" class="btn green">查询</button>
                                    <button type="button" class="btn default" style="margin-left: 10px;"
                                        onclick="resetForm()">重置
                                    </button>
                                </div>
                            </div>
                            <div class="row" style="margin-bottom: 10px;">
                                <div class="col-sm-2" style="padding-left: 35px">
                                    <button type="button" class="btn blue" style="width: 120px;"
                                        onclick="addLabel()">新增推送标签
                                    </button>
                                </div>
                                <div class="col-sm-10"
                                    style="text-align: right; display: flex; align-items: center; justify-content: flex-end;">
                                    <span style="color: #9e9b9b; margin-right: 20px;">*列表操作开关开启默认次日00:00生效，关闭立即生效</span>
                                    <button type="submit" class="btn btn-default">刷新数据</button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <table class="table table-striped table-bordered table-hover table-header-fixed layui-form">
                        <thead>
                            <tr>
                                <th width="3%">序号</th>
                                <th width="5%">标签名称</th>
                                <th width="10%">推送保司</th>
                                <th width="6%">是否推送</th>
                                <th width="6%">估损金额推送比例</th>
                                <th width="7%">配置生效时间</th>
                                <th width="5%">最后修改人</th>
                                <th width="7%">最后修改时间</th>
                                <th width="7%">最后修改原因</th>
                                <th width="10%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <#list page.list as vo>
                                <tr>
                                    <td title="">${vo_index + 1}</td>
                                    <td title="">${vo.label}</td>

                                    <td title="">
                                        <#assign codeArr=vo.insCode?split(",")>
                                            <#list codeArr as code>
                                                <#if insCodeMap[code]??>
                                                    ${insCodeMap[code]}<#if code_has_next>;</#if>
                                                    <#else>
                                                        ${code}<#if code_has_next>;</#if>
                                                </#if>
                                            </#list>
                                    </td>
                                    <td title="">
                                        <#if vo.status??>
                                            <#if vo.status==0>
                                                否
                                                <#elseif vo.status==1>
                                                    是
                                            </#if>
                                        </#if>
                                    </td>
                                    <td title="">${vo.proportion}%</td>
                                    <td title="" class="effective-time-td" data-id="${vo.id}">
                                        <#if vo.effectiveTime??>
                                            ${vo.effectiveTime?string["yyyy-MM-dd HH:mm"]}
                                            <#else>
                                                &nbsp;
                                        </#if>
                                    </td>
                                    <td title="">${managerMap.get(vo.modifier)}</td>
                                    <td title="">
                                        <#if vo.modifyTime??>
                                            ${vo.modifyTime?string["yyyy-MM-dd HH:mm"]}
                                            <#else>
                                                &nbsp;
                                        </#if>
                                    </td>
                                    <td title="${vo.modifyReason}" id="td-modifyReason">
                                        <#if vo.modifyReason?length gt 6>
                                            ${vo.modifyReason?substring(0,6)}...
                                            <#else>
                                                ${vo.modifyReason!''}
                                        </#if>
                                    </td>
                                    <td style="display: flex;align-items: center;justify-content: center;">
                                        <a style="color: #c4bb47;" onclick="editLabel('${vo.id}')">编辑</a>
                                        <#if vo.status==0>
                                            <a style="color: #f80015;" onclick="deleteLabel('${vo.id}', -1)">删除</a>
                                        </#if>
                                        <div style="display: inline-block;width: 50px;height: 25px;">
                                            <input type="checkbox" name="openSwitch" <#if vo.status==1>checked</#if>
                                            lay-skin="switch" lay-filter="checkbox-filter" data-id="${vo.id}">

                                        </div>
                                        <#if vo.status==1>停用<#else>启用</#if>
                                    </td>
                                </tr>
                            </#list>
                        </tbody>
                    </table>
                    <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                        <div class="modal-dialog">
                            <img src="${ctx}/images/load.gif">
                        </div>
                    </div>
                </div>
                <!-- END FORM-->
            </div>
        </div>
    </div>
    <@sc.pagination page=page />
</body>

</html>