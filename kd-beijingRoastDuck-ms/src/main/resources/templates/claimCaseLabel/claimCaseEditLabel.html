<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>编辑标签</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/layui/css/layui.css" rel="stylesheet" type="text/css" />
    <script src="${ctx}/metronic/layui/layui.js" type="text/javascript"></script>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css" />
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet"
        type="text/css" />
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <style>
        .layui-form-label {
            width: 170px;
        }

        .required-label::before {
            content: '*';
            color: red;
            margin-right: 5px;
        }

        .layui-input-block {
            margin-left: 170px;
        }

        .layui-input-static {
            min-height: 38px;
            line-height: 38px;
            background: #f5f5f5;
            border-radius: 2px;
            padding: 0 15px;
            color: #555;
            border: 1px solid #e6e6e6;
            font-size: 14px;
            box-sizing: border-box;
            word-break: break-all;
            white-space: normal;
        }

        .form-container {
            margin: 30px 40px 0 40px;
        }

        /* 确保Select2下拉框正确显示 */
        .select2-container {
            z-index: 100;
        }

        /* 提高Layui下拉框层级（覆盖Select2） */
        .layui-form-select dl {
            z-index: 9999 !important;
        }

        /* 只隐藏"推送保司"区域的Layui冲突元素 */
        #insCodeDiv .layui-form-select {
            display: none !important;
        }

        /* 显示Select2创建的元素 */
        .select2-container {
            display: block !important;
        }

        /* 为Select2容器添加与Layui输入框一致的样式 */
        .select2-container .select2-selection--multiple {
            min-height: 38px;
            border: 1px solid #e6e6e6;
            border-radius: 2px;
            padding: 2px 5px;
        }

        #status+.layui-form-select {
            width: 320px !important;
        }

        .layui-form-switch.layui-form-onswitch {
            background-color: #1676FF !important;
            /* 开启时蓝色 */
            border-color: #1676FF !important;
        }
    </style>
</head>

<body>
    <div class="form-container">
        <form class="layui-form" id="editLabelForm" action="${ctx}/claimCaseLabelController/updateLabel" method="post">
            <input type="hidden" name="id" value="${claimLabelPushConfig.id!''}">
            <div class="layui-form-item">
                <label class="layui-form-label">标签名称</label>
                <div class="layui-input-block">
                    <div class="layui-input-static" style="width: 320px;">
                        ${claimLabelPushConfig.label!''}
                    </div>
                </div>
            </div>
            <input type="hidden" name="label" value="${claimLabelPushConfig.label!''}">
            <div class="layui-form-item">
                <label class="layui-form-label">是否推送：</label>
                <div class="layui-input-block">
                    <span id="yesOrNoText" style="margin-right: 10px; margin-top: 8px; display: inline-block;"> <#if claimLabelPushConfig.status==1>是<#else>否</#if> </span>
                    <div style="display: inline-block;width: 50px;height: 25px; vertical-align: middle;">
                        <input type="checkbox" name="openSwitch" lay-skin="switch" lay-filter="checkbox-filter" <#if claimLabelPushConfig.status==1>checked</#if>>
                    </div>
                    <span id="switchStatusText" style="margin-top: 8px; display: inline-block;">
                        <#if claimLabelPushConfig.status==1>停用<#else>启用</#if>
                    </span>
                </div>
            </div>
            <div class="layui-form-item" id="insCodeDiv">
                <label class="layui-form-label required-label">推送保司：</label>
                <div class="layui-input-block" style="width: 320px;">
                    <select id="insCode" name="insCode" class="form-control select2-multiple" multiple
                        style="width: 280px; display: none;" data-no-render>
                        <#assign insCodeArr=(claimLabelPushConfig.insCode!'')?split(',')>
                            <#if insCodeMap?exists>
                                <#list insCodeMap.keySet() as key>
                                    <option value="${key}" <#if insCodeArr?seq_contains(key)>selected
                            </#if>>${insCodeMap.get(key)}</option>
                            </#list>
                            </#if>
                    </select>
                </div>
            </div>
            <div class="layui-form-item" id="proportionDiv">
                <label class="layui-form-label required-label">估损金额推送比例：</label>
                <div class="layui-input-block">
                    <input type="text" name="proportion" min="0" max="100" placeholder="请输入推送比例" class="layui-input"
                        value="${claimLabelPushConfig.proportion!''}" lay-verify="proportionInt"
                        style="display: inline-block; width: 320px;"> %
                    <p style="color: #999; font-size: 12px;">*估损金额推送比例配置生效后对已推送案件无影响</p>
                </div>
            </div>
            <!-- 配置生效时间 -->
            <div class="layui-form-item" id="effectiveTimeDiv">
                <label class="layui-form-label required-label">配置生效时间：</label>
                <div class="layui-input-block">
                    <input type="text" name="effectiveTime" id="effectiveTime" placeholder="请选择配置生效时间"
                        class="layui-input" value="${claimLabelPushConfig.effectiveTime?string['yyyy-MM-dd HH:mm']!''}"
                        style="width: 320px;">
                </div>
            </div>
            <!-- 配置修改原因 -->
            <div class="layui-form-item">
                <label class="layui-form-label required-label">配置修改原因：</label>
                <div class="layui-input-block">
                    <textarea name="modifyReason" maxlength="100" placeholder="请输入配置修改原因，最多100字"
                        class="layui-textarea"></textarea>
                    <span style="color: #999; font-size: 12px;">最多100字</span>
                </div>
            </div>
            <!-- 最后修改人 -->
            <div class="layui-form-item">
                <label class="layui-form-label">最后修改人：</label>
                <div class="layui-input-block">
                    <div class="layui-input-static" style="max-width:300px;">
                        ${claimLabelPushConfig.modifier!''}
                    </div>
                </div>
            </div>
            <!-- 最后修改时间 -->
            <div class="layui-form-item">
                <label class="layui-form-label">最后修改时间：</label>
                <div class="layui-input-block">
                    <div class="layui-input-static" style="max-width:300px;">
                        ${(claimLabelPushConfig.modifyTime?string['yyyy-MM-dd HH:mm:ss'])!''}
                    </div>
                </div>
            </div>
            <!-- 最后修改原因 -->
            <div class="layui-form-item">
                <label class="layui-form-label">最后修改原因：</label>
                <div class="layui-input-block" style="padding-top: 7px; text-align: left;">
                    <div class="layui-input-static"
                        style="max-width:300px; word-break: break-all; white-space: normal;">
                        ${claimLabelPushConfig.modifyReason!''}
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="submitEditLabel">保存</button>
                    <button type="button" class="layui-btn layui-btn-primary" id="resetFormBtn">重置</button>
                </div>
            </div>
        </form>
    </div>
    <script>
        layui.use(['form', 'laydate'], function () {
            var form = layui.form;
            var laydate = layui.laydate;
            form.render();

            // 初始化select2
            $(function () {
                // 初始化多选Select2
                var $insCode = $('#insCode').select2({
                    placeholder: '请选择',
                    allowClear: true,
                    width: '100%'
                });

                $("input[type=checkbox]").hide();
                $(".checker span").css("background-image", "none");

                // 监听Select2选择变化事件
                $insCode.on('change', function () {
                    var selectedValues = $(this).val();
                    // 调试日志，确认值是否正确获取
                    console.log('选中的保司值:', selectedValues);
                });

                // 页面加载时触发一次change事件，确保初始值正确显示
                setTimeout(function () {
                    $insCode.trigger('change');
                }, 100);

            });

            // 监听switch切换
            form.on('switch(checkbox-filter)', function (data) {
                togglePushFields(data.elem.checked);
                // 新增：切换“是/否”显示
                $('#yesOrNoText').text(data.elem.checked ? '是' : '否');
            });

            // 页面初始化时根据开关状态显示/隐藏
            // 用setTimeout确保在Layui渲染后执行
            setTimeout(function () {
                var checked = $('input[name="openSwitch"]').prop('checked');
                togglePushFields(checked);
                // 新增：初始化“是/否”显示
                $('#yesOrNoText').text(checked ? '是' : '否');
            }, 0);

            function togglePushFields(checked) {
                if (checked) {
                    $('#insCodeDiv').show();
                    $('#proportionDiv').show();
                    $('#effectiveTimeDiv').show();
                    $('#switchStatusText').text('停用');
                } else {
                    $('#insCodeDiv').hide();
                    $('#proportionDiv').hide();
                    $('#effectiveTimeDiv').hide();
                    $('#switchStatusText').text('启用');
                }
            }

            // 初始化时间选择器
            // 计算次日00:00字符串
            function getNowStr() {
                var now = new Date();
                var yyyy = now.getFullYear();
                var mm = ('0' + (now.getMonth() + 1)).slice(-2);
                var dd = ('0' + now.getDate()).slice(-2);
                var hh = ('0' + now.getHours()).slice(-2);
                var min = ('0' + now.getMinutes()).slice(-2);
                return yyyy + '-' + mm + '-' + dd + ' ' + hh + ':' + min;
            }

            laydate.render({
                elem: '#effectiveTime',
                type: 'datetime',
                format: 'yyyy-MM-dd HH:mm',
                showBottom: true,
                btns: ['clear', 'confirm'],
                min: getNowStr(), // 关键：设置最小可选时间为当前时间
                done: function (value, date, endDate) {
                    // 用户选择后校验
                    var minTime = new Date(getNowStr().replace(/-/g, '/'));
                    var inputTime = new Date(value.replace(/-/g, '/'));
                    if (inputTime < minTime) {
                        layer.msg('配置生效时间不能早于当前时间');
                        $('#effectiveTime').val('');
                    }
                },
                ready: function (date) {
                    setTimeout(function () {
                        $('.laydate-time-list li:last-child dd').each(function () {
                            if ($(this).text() === '00') {
                                $(this).click();
                            }
                        });
                    }, 10);
                }
            });

            // 自定义推送比例校验
            form.verify({
                proportionInt: function (value, item) {
                    // 先判断是否为空（覆盖 required 校验的提示，统一错误文案）
                    if (!value.trim()) {
                        return '推送比例不能为空';
                    }
                    // 再判断是否为 0-100 的整数
                    if (!/^\d+$/.test(value) || value < 0 || value > 100) {
                        return '推送比例只能为0-100的整数';
                    }
                }
            });

            // 表单提交处理
            form.on('submit(submitEditLabel)', function (data) {

                // 手动触发表单校验
                if (!form.validate()) {
                    return false; // 阻止提交
                }

                var insCodeArr = $('#insCode').val();
                data.field.insCode = insCodeArr ? insCodeArr.join(',') : '';

                // 补全秒为00
                var $input = $('#effectiveTime');
                var val = $input.val();
                var submitVal = val;
                if (val && !/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(val)) {
                    submitVal = val + ':00';
                }
                data.field.effectiveTime = submitVal;

                // 调试输出
                console.log('最终提交的effectiveTime:', data.field.effectiveTime);
                console.log('保司：', data.field.insCode);
                // 校验逻辑
                let errMsg = "";
                let pass = true;
                let dataJson = data.field;
                var isOpen = $('input[name="openSwitch"]').prop('checked');
                if (isOpen) {
                    // 用 newInsCode 校验，而不是 dataJson.insCode
                    if (!dataJson.insCode) {
                        errMsg += "推送保司不能为空<br>";
                        pass = false;
                    }
                    if (!dataJson.proportion) {
                        errMsg += "估损金额推送比例不能为空<br>";
                        pass = false;
                    }
                    if (!dataJson.effectiveTime) {
                        errMsg += "配置生效时间不能为空<br>";
                        pass = false;
                    }
                }
                if (!dataJson.modifyReason) {
                    errMsg += "配置修改原因不能为空<br>";
                    pass = false;
                }
                if (!pass) {
                    layer.msg(errMsg, { icon: 0 });
                    return false;
                }
                data.field.status = $('input[name="openSwitch"]').prop('checked') ? 1 : 0;
                console.log(isOpen);
                // 判断是否推送状态，弹出确认提示
                var content;
                if (isOpen) {
                    var showTime = data.field.effectiveTime ? data.field.effectiveTime : '次日00:00';
                    content = '保存成功的配置将于' + showTime + '生效，生效后将对全部未推送过的标签案件进行推送';
                } else {
                    content = '标签推送关闭保存后将立即生效';
                }
                console.log(data.field.effectiveTime)
                layer.open({
                    title: " ",
                    content: content,
                    btn: ['确定保存', '取消保存'],
                    yes: function (index, layero) {
                        // 用户点击确定，执行AJAX保存
                        $.ajax({
                            url: `${ctx}/claimCaseLabelController/updateLabel`,
                            type: 'POST',
                            async: true,
                            cache: false,
                            data: JSON.stringify(data.field),
                            dataType: 'json',
                            contentType: 'application/json;charset=utf-8',
                            success: function (res) {
                                if (res.ret == "0") {
                                    layer.msg(res.msg, {
                                        icon: 1,
                                        time: 1000,
                                        shade: [0.0001, '#000']
                                    }, function () {
                                        if (window.parent) {
                                            window.parent.location.reload();
                                            var frameIndex = parent.layer.getFrameIndex(window.name);
                                            parent.layer.close(frameIndex);
                                        }
                                    });
                                } else {
                                    layer.msg(res.msg, {
                                        icon: 2,
                                        time: 2000,
                                        shade: [0.0001, '#000']
                                    });
                                }
                            },
                            error: function () {
                                layer.msg('请求失败', {
                                    icon: 2,
                                    time: 2000,
                                    shade: [0.0001, '#000']
                                });
                            }
                        });
                        layer.close(index); // 关闭确认弹窗
                    }
                    // 取消按钮什么都不做
                });

                return false; // 阻止表单直接提交
            });

            // 切换显示隐藏函数
            // window.togglePushFields = function () { // This line is removed as per the new_code
            //     var status = $('#status').val();
            //     if (status === '1') {
            //         $('#insCodeDiv').show();
            //         $('#proportionDiv').show();
            //         $('#effectiveTimeDiv').show();
            //     } else {
            //         $('#insCodeDiv').hide();
            //         $('#proportionDiv').hide();
            //         $('#effectiveTimeDiv').hide();
            //     }
            // }
        });

        // 页面加载时保存初始表单值
        $(function () {
            var originData = {};
            $('#editLabelForm').find('input,select,textarea').each(function () {
                var name = $(this).attr('name');
                if (name) {
                    if ($(this).is('select[multiple]')) {
                        originData[name] = $(this).val() ? $(this).val().join(',') : '';
                    } else {
                        originData[name] = $(this).val();
                    }
                }
            });
            window._originFormData = originData;
        });
        // 点击重置按钮还原表单
        $('#resetFormBtn').on('click', function () {
            if (window._originFormData) {
                for (var key in window._originFormData) {
                    var val = window._originFormData[key];
                    var $el = $('[name="' + key + '"]');
                    if ($el.length) {
                        if ($el.is('select[multiple]')) {
                            $el.val(val ? val.split(',') : []).trigger('change');
                        } else if ($el.is('select')) {
                            $el.val(val).trigger('change');
                        } else {
                            $el.val(val);
                        }
                    }
                }
                // select2特殊处理
                if (window._originFormData.insCode) {
                    $('#insCode').val(window._originFormData.insCode.split(',')).trigger('change');
                } else {
                    $('#insCode').val([]).trigger('change');
                }
            }
        });

        // 计算次日00:00字符串
        function getTomorrowZero() {
            var now = new Date();
            now.setDate(now.getDate() + 1);
            now.setHours(0, 0, 0, 0);
            var yyyy = now.getFullYear();
            var mm = ('0' + (now.getMonth() + 1)).slice(-2);
            var dd = ('0' + now.getDate()).slice(-2);
            return yyyy + '-' + mm + '-' + dd + ' 00:00';
        }

        // 标记是否是自动设置，避免死循环
        var isAutoSetEffectiveTime = false;

        // 监听表单变更
        $('#editLabelForm').on('change input', 'input,select,textarea', function (e) {
            // 跳过生效时间自身的自动设置
            if ($(this).attr('name') === 'effectiveTime' && isAutoSetEffectiveTime) {
                isAutoSetEffectiveTime = false;
                return;
            }
            // 检查是否有除modifyReason以外的字段变更
            var changedOther = false;
            $('#editLabelForm').find('input,select,textarea').each(function () {
                var name = $(this).attr('name');
                if (!name || name === 'modifyReason') return;
                var originVal = window._originFormData ? window._originFormData[name] : undefined;
                var nowVal;
                if ($(this).is('select[multiple]')) {
                    nowVal = $(this).val() ? $(this).val().join(',') : '';
                } else {
                    nowVal = $(this).val();
                }
                if (originVal !== undefined && nowVal !== originVal) {
                    changedOther = true;
                    return false; // break
                }
            });
            if (changedOther) {
                // 只有非“配置修改原因”字段变更时，才自动设置生效时间为次日00:00
                isAutoSetEffectiveTime = true;
                $('#effectiveTime').val(getTomorrowZero());
            }
        });

        function closeLayer() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }
    </script>
</body>

</html>