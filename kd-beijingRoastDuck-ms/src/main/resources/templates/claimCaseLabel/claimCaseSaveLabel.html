<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>新增推送标签配置</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/layui/css/layui.css" rel="stylesheet" type="text/css"/>
    <script src="${ctx}/metronic/layui/layui.js" type="text/javascript"></script>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>

    <style>
        .layui-form-label {
            width: 170px;
        }

        .required-label::before {
            content: '*';
            color: red;
            margin-right: 5px;
        }

        .layui-input-block {
            margin-left: 170px;
        }

        .layui-input-static {
            min-height: 38px;
            line-height: 38px;
            background: #f5f5f5;
            border-radius: 2px;
            padding: 0 15px;
            color: #555;
            border: 1px solid #e6e6e6;
            font-size: 14px;
            box-sizing: border-box;
        }

        .form-container {
            margin: 30px 40px 0 40px;
        }

        .select2-container {
            z-index: 100;
        }

        .layui-form-select dl {
            z-index: 9999 !important;
        }

        #insCodeDiv .layui-form-select {
            display: none !important;
        }

        .select2-container {
            display: block !important;
        }

        .select2-container .select2-selection--multiple {
            min-height: 38px;
            border: 1px solid #e6e6e6;
            border-radius: 2px;
            padding: 2px 5px;
        }

        #status + .layui-form-select {
            width: 320px !important;
        }

        /* 按钮颜色 */
        .layui-form-onswitch {
            background-color: #1676FF !important;
            border-color: #1676FF !important;
        }

        .layui-switch-btn {
            display: inline-block;
            width: 50px;
            height: 25px;
            padding-top: 2px;
            margin-right: 5px;
        }

    </style>
</head>

<body>
<div class="form-container">
    <form class="layui-form" id="saveLabelForm" action="${ctx}/claimCaseLabelController/saveLabel" method="post">
        <div class="layui-form-item">
            <label class="layui-form-label required-label">标签名称：</label>
            <div class="layui-input-block" style="width: 320px;">
                <select name="label" lay-verify="required" lay-search>
                    <option value="">请选择</option>
                    <#if labelMap?? && (labelMap?size > 0)>
                      <#list labelMap.keySet() as key>
                        <option value="${key}">${labelMap.get(key).msg}</option>
                      </#list>
                    </#if>
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">是否推送：</label>
            <div class="layui-input-block" style="display: flex;align-items: center;">
                <span class="layui-form-mid">是</span>
                <div class="layui-switch-btn">
                    <input type="checkbox" id="status" name="status" value="1" disabled checked lay-skin="switch"
                           lay-filter="checkbox-filter">
                </div>
                <span class="layui-form-mid">停用</span>
            </div>
        </div>

        <div class="layui-form-item" id="insCodeDiv">
            <label class="layui-form-label required-label">推送保司：</label>
            <div class="layui-input-block" style="width: 320px; ">
                <select id="insCode" name="insCode" class="form-control select2-multiple" multiple
                        data-placeholder="请选择推送保司" style="width: 100%;" lay-ignore>
                    <#if insCodeMap?exists>
                      <#list insCodeMap.keySet() as key>
                        <option value="${key}">${insCodeMap.get(key)}</option>
                      </#list>
                    </#if>
                </select>
            </div>
        </div>

        <div class="layui-form-item" id="proportionDiv">
            <label class="layui-form-label required-label">估损金额推送比例：</label>
            <div class="layui-input-block">
                <input type="text" name="proportion" min="0" max="100" placeholder="请输入推送比例" class="layui-input"
                       lay-verify="proportionInt" style="display: inline-block; width: 320px;"> %
                <p style="color: #999; font-size: 12px;">*估损金额推送比例配置生效后对已推送案件无影响</p>
            </div>
        </div>

        <div class="layui-form-item" id="effectiveTimeDiv">
            <label class="layui-form-label required-label">配置生效时间：</label>
            <div class="layui-input-block">
                <input type="text" name="effectiveTime" id="effectiveTime" style="width: 320px;"
                       placeholder="请选择配置生效时间" class="layui-input" lay-verify="required">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label required-label">配置修改原因：</label>
            <div class="layui-input-block">
                <textarea name="modifyReason" class="layui-textarea" lay-verify="required"
                          maxlength="100" placeholder="请输入配置修改原因，最多100字"></textarea>
                <span class="layui-form-mid" style="color: #999;">最多100字</span>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">最后修改人：</label>
            <div class="layui-input-block">
                <div class="layui-input-static" style="max-width:300px;">-</div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">最后修改时间：</label>
            <div class="layui-input-block">
                <div class="layui-input-static" style="max-width:300px;">-</div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">最后修改原因：</label>
            <div class="layui-input-block" style="padding-top: 7px; text-align: left;">
                <div class="layui-input-static" style="max-width:300px;">-</div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="submitSaveLabel">保存</button>
                <button type="button" class="layui-btn layui-btn-primary" id="resetFormBtn">重置</button>
            </div>
        </div>
    </form>
</div>

<script>
    layui.use(['form', 'laydate', 'layer'], function () {
        var form = layui.form;
        var laydate = layui.laydate;
        var layer = layui.layer;

        // 计算次日00:00的默认时间
        function getDefaultEffectiveTime() {
            var now = new Date();
            var tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 加 1 天
            // 设置时间为 00:00:00
            tomorrow.setHours(0, 0, 0, 0);

            var year = tomorrow.getFullYear();
            var month = (tomorrow.getMonth() + 1).toString().padStart(2, '0'); // 月份从 0 开始，要 +1 并补零
            var day = tomorrow.getDate().toString().padStart(2, '0');
            var hour = tomorrow.getHours().toString().padStart(2, '0');
            var minute = tomorrow.getMinutes().toString().padStart(2, '0');

            // 拼接成 yyyy-MM-dd HH:mm 格式
            return year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
        }

        // 初始化 Select2
        $(function () {
            // 初始化多选Select2
            var $insCode = $('#insCode').select2({
                placeholder: '请选择',
                allowClear: true,
                width: '100%'
            });

            $("input[type=checkbox]").hide();
            $(".checker span").css("background-image", "none");
        });
        // 渲染 Layui 表单
        form.render(null, 'saveLabelForm');

        // 初始化时间选择器（默认填充次日00:00，支持手动修改）
        var defaultTime = getDefaultEffectiveTime();

        // 初始化时间选择器
        laydate.render({
            elem: '#effectiveTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm',
            value: defaultTime, // 设置默认值
            btns: ['clear', 'confirm'], // 只显示“清空”和“确定”按钮（去掉“now”）
            // 不设置min，保证历史数据能显示
            done: function (value, date, endDate) {
                // 用户选择后校验
                var now = new Date();
                var inputTime = new Date(value.replace(/-/g, '/'));
                if (inputTime < now) {
                    layer.msg('配置生效时间不能早于当前时间');
                    $('#effectiveTime').val(defaultTime); // 恢复默认值
                }
            },
            ready: function (date) {
                setTimeout(function () {
                    $('.laydate-time-list li:last-child dd').each(function () {
                        if ($(this).text() === '00') {
                            $(this).click();
                        }
                    });
                }, 10);
            }
        });
        $('#effectiveTime').val(defaultTime); // 回显默认值

        // 自定义推送比例校验
        form.verify({
            proportionInt: function (value, item) {
                // 再判断是否为 0-100 的整数
                if (!/^\d+$/.test(value) || value < 0 || value > 100) {
                    return '估损金额推送比例只能为0-100的整数';
                }
            }
        });

        // 表单提交逻辑
        form.on('submit(submitSaveLabel)', function (data) {

            // 手动触发表单校验
            if (!form.validate()) {
                return false; // 阻止提交
            }

            // select2多选转为逗号分隔字符串
            var insCodeArr = $('#insCode').val();
            var newInsCode = insCodeArr ? insCodeArr.join(',') : '';
            // 比较初始值和当前值
            var originInsCode = window._originFormData && window._originFormData.insCode ? window._originFormData.insCode : '';
            if (newInsCode !== originInsCode) {
                data.field.insCode = newInsCode;
            } else {
                data.field.insCode = '';
            }

            // 校验逻辑
            let errMsg = "";
            let pass = true;
            let dataJson = data.field;

            // 用 newInsCode 校验，而不是 dataJson.insCode
            if (!newInsCode) {
                errMsg += "推送保司不能为空<br>";
                pass = false;
            }
            if (!dataJson.proportion) {
                errMsg += "估损金额推送比例不能为空<br>";
                pass = false;
            }
            if (!dataJson.effectiveTime) {
                errMsg += "配置生效时间不能为空<br>";
                pass = false;
            }
            if (!dataJson.modifyReason) {
                errMsg += "配置修改原因不能为空<br>";
                pass = false;
            }
            if (!pass) {
                layer.msg(errMsg, {icon: 0, time: 3000});
                return false;
            }

            // status 值（固定为 1）
            dataJson.status = 1;
            // 判断是否推送状态，弹出确认提示
            var title, content;
            if (dataJson.status == 1) {
                title = ' ';
                var showTime = dataJson.effectiveTime ? dataJson.effectiveTime : '次日00:00';
                content = '保存成功的配置将于' + showTime + '生效，生效后将对全部未推送过的标签案件进行推送';
            } else {
                title = ' ';
                content = '标签推送关闭保存后将立即生效';
            }

            // 补全秒为00
            var $input = $('#effectiveTime');
            var val = $input.val();
            if (val && !/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(val)) {
                val = val + ':00';
                $input.val(val); // 赋值回input
            }
            // 关键：同步到data.field
            dataJson.effectiveTime = $input.val();

            // 确认弹窗逻辑
            layer.open({
                title: title,
                content: content,
                btn: ['确定保存', '取消保存'],
                yes: function (index) {
                    $.ajax({
                        url: "${ctx}/claimCaseLabelController/addLabel",
                        type: 'POST',
                        data: JSON.stringify(data.field),
                        contentType: 'application/json;charset=utf-8',
                        dataType: 'json',
                        success: function (res) {
                            if (res.ret === "0") {
                                layer.msg("保存成功", {icon: 1, time: 2000}, function () {
                                    var frameIndex = parent.layer.getFrameIndex(window.name);
                                    parent.layer.close(frameIndex);
                                    parent.location.reload();
                                });
                            } else if (res.ret === "-1") {
                                layer.msg(res.msg, {icon: 2, time: 3000});
                            } else {
                                layer.msg('保存失败：' + res.msg, {icon: 2, time: 2000});
                            }
                        },
                        error: function () {
                            layer.msg('请求失败，请重试', {icon: 2, time: 2000});
                        }
                    });
                },
                btn2: function (index) {
                    // 取消保存时，重置配置生效时间为默认值
                    $('#effectiveTime').val(defaultTime);
                }
            });
            return false;
        });

        // 点击重置按钮还原表单
        $('#resetFormBtn').on('click', function () {
            if (window._originFormData) {
                for (var key in window._originFormData) {
                    var val = window._originFormData[key];
                    var $el = $('[name="' + key + '"]');
                    if ($el.length) {
                        if ($el.is('select[multiple]')) {
                            $el.val(val ? val.split(',') : []).trigger('change');
                        } else if ($el.is('select')) {
                            $el.val(val).trigger('change');
                        } else {
                            $el.val(val);
                        }
                    }
                }
                // select2特殊处理
                if (window._originFormData.insCode) {
                    $('#insCode').val(window._originFormData.insCode.split(',')).trigger('change');
                } else {
                    $('#insCode').val([]).trigger('change');
                }
            }

            $('#saveLabelForm')[0].reset();
            $('#insCode').val(null).trigger('change');
            form.render();
        });
    });
</script>
</body>

</html>