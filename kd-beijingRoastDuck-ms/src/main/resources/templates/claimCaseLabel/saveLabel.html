<!--[if IE 8]> <html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
<!--<![endif]-->
<head>
  <meta charset="UTF-8">
  <title>新增推送标签配置</title>
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta content="width=device-width, initial-scale=1" name="viewport" />
  <meta content="" name="description" />
  <meta content="" name="author" />
  <#include "/common/cssResource.html">
  <#include "/common/jsResource.html">
  <script type="text/javascript">

    layui.use(['form','layer','laydate'],function (){
      let form = layui.form;
      let layer = layui.layer;
      let laydate = layui.laydate;

      // 重新渲染表单
      form.render();

      laydate.render({
        elem: '#effectiveTime',
        type: 'datetime',
        // 限制最小时间为次日00:00
        min: function() {
          var tomorrow = new Date();
          tomorrow.setDate(tomorrow.getDate() + 1);
          return tomorrow;
        }
      });

      //表单提交校验
      form.on('submit(saveLabel)',function(data){
        console.log('表单数据：', data.field)
        console.log('保司数据：', data.field.insCode);
        let errMsg = "";
        let pass = true;
        let dataJson = data.field;

        if (!dataJson.label) {
          errMsg += "请选择标签名称<br>";
          pass = false;
        }
        if (!dataJson.insCode || dataJson.insCode.length === 0) {
          errMsg += "请选择推送保司<br>";
          pass = false;
        }
        // 估损金额比例是否为整数
        if (dataJson.proportion && !/^\d+$/.test(dataJson.proportion)) {
          errMsg += "估损金额推送比例仅支持整数输入<br>";
          pass = false;
        }
        // 生效时间是否为次日及以后
        if (!dataJson.effectiveTime) {
          errMsg += "配置生效时间需为次日 00:00 及以后<br>";
          pass = false;
        } else {
          let selectedDate = new Date(dataJson.effectiveTime);
          let tomorrow = new Date();
          tomorrow.setDate(tomorrow.getDate() + 1);
          tomorrow.setHours(0, 0, 0, 0);

          if (selectedDate < tomorrow) {
            errMsg += "配置生效时间需为次日 00:00 及以后<br>";
            pass = false;
          }
        }

        if (!dataJson.modifyReason) {
          errMsg += "请输入配置修改原因<br>";
          pass = false;
        } else if (dataJson.modifyReason.length > 100){
          errMsg += "配置修改原因最多支持输入100个字符，允许中英文和 ，。（）等普通符号<br>";
          pass = false;
        }

        // 校验失败提示
        if (!pass) {
          layer.msg(errMsg, {icon: 2, time: 3000});
          return false;
        }

        //保存
        $.ajax({
          url: "${ctx}/claimCaseLabelController/saveLabel",
          type: 'POST',
          data: JSON.stringify(dataJson),
          contentType: 'application/json;charset=utf-8',
          success: function (res) {
            if (res.success) {
              layer.msg("保存成功", {icon: 1,time: 2000}, function(){
                // 关闭弹窗并刷新列表页
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
                parent.location.reload(); // 刷新父页面列表
              });
            } else {
              layer.msg('保存失败：' + res.msg, {icon: 2,time: 2000}, function(){

              });
            }
          },
          error: function (error) {
            layer.msg("请求失败，请重试", {icon: 2, time: 2000});
            console.error("AJAX请求错误:", error);
          }
        });
        return false; // 阻止表单默认提交
      });

      // 初始化多选下拉框
      //form.render('select');

      // 监听多选下拉框的变化事件
      form.on('select(insCode)', function(data){
        console.log('选中的保司ID：', data.value);
        console.log('选中的保司文本：', data.elem[data.elem.selectedIndex].text);
      });
    });

  </script>
  <style>
    /* 调整整体表单间距等样式 */
    .layui-form {
      padding: 20px;
    }
    .layui-form-item {
      margin-bottom: 15px;
      display: flex;
      align-items: center;
    }
    .layui-form-label {
      width: 120px;
      text-align: right;
      padding-right: 10px;
    }
    .layui-input-inline {
      flex: 1;
      margin: 0 10px;
    }
    .layui-word-aux {
      color: #999;
      font-size: 12px;
    }
    /* 调整开关按钮样式 */
    .layui-switch {
      margin-top: 0;
    }
    /* 调整按钮区域样式 */
    .layui-form-item:last-child {
      justify-content: center;
    }
    .layui-btn {
      margin: 0 5px;
    }
    .layui-btn-blue {
      background-color: #007bff;
      border-color: #007bff;
      color: #fff;
    }
    .layui-btn-blue:hover {
      background-color: #0056b3;
      border-color: #0056b3;
    }
  </style>
</head>
<body>
  <div id="caseStatusReturn">
    <form class="layui-form layui-form-pane" action="" style="padding: 10px" id="newLabelForm">
      <div class="layui-form-item">
        <label class="layui-form-label">*标签名称：</label>
        <div class="layui-input-inline">
          <select name="label" lay-verify="required" lay-search>
            <option value="">请选择</option>
            <#if labelMap?? && (labelMap?size > 0)>
            <#list labelMap.keySet() as key>
            <option value="${key}">${labelMap.get(key).msg}</option>
          </#list>
        </#if>
        </select>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">是否推送</label>
      <div class="layui-input-inline">
        <input type="checkbox" name="status" lay-skin="switch" lay-text="是|停用" checked>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">*推送保司：</label>
      <div class="layui-input-inline">
        <select  name="insCode" lay-verify="required" lay-filter="insCode" multiple>
          <option value="">请选择</option>
          <#if insCodeMap?exists>
          <#list insCodeMap.keySet() as key>
          <option value="${key}">${insCodeMap.get(key)}</option>
        </#list>
      </#if>
      </select>
    </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">*估损金额推送比例：</label>
      <div class="layui-input-inline">
        <input type="number" name="proportion"  lay-verify="required|number"
               placeholder="请输入整数比例" pattern="\d+" title="仅支持整数输入">
      </div>
      <span class="layui-form-mid">%</span>
      <span class="layui-text-em">*估损金额推送比例配置生效后对已推送案件无影响</span>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">*配置生效时间：</label>
      <div class="layui-input-inline">
        <input type="datetime-local" name="effectiveTime" lay-verify="required" id="effectiveTime">
      </div>
    </div>
    <div class="layui-form-item layui-form-text">
      <label class="layui-form-label">*配置修改原因：</label>
      <div class="layui-input-block">
                    <textarea name="modifyReason" lay-verify="required"
                              placeholder="请输入内容（支持中英文和 ，。（）等符号，最多100字）" maxlength="100"></textarea>
        <div class="layui-word-aux">0/100</div>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">最后修改人：</label>
      <div class="layui-input-inline">
        <input type="text" name="lastModifier" value="-" disabled>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">最后修改时间：</label>
      <div class="layui-input-inline">
        <input type="text" name="lastModifier" value="-" disabled>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">最后修改原因：</label>
      <div class="layui-input-inline">
        <input type="text" name="lastModifier" value="-" disabled>
      </div>
    </div>
    <div class="layui-form-item">
      <div class="layui-input-block" style="text-align: center;">
        <button class="layui-btn layui-btn-primary" type="reset">重置</button>
        <button class="layui-btn" lay-submit lay-filter="saveLabel">保存</button>
      </div>
    </div>
    </form>
  </div>
</body>
</html>