<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>保司复核</title>
    <!-- theme.css 修改主题配色 -->
    <link href="${ctx}/metronic/layui/css/theme.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/layui/css/layui.css" rel="stylesheet" type="text/css"/>
    <script src="${ctx}/metronic/layui/layui.js" type="text/javascript"></script>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <style>
        .layui-form-label{
            width: auto;
        }
    </style>

    <script type="text/javascript">
        function caseDetail(event) {
            event.preventDefault();
            let claimCaseId = $(this).attr('case-id');
            window.open("${ctx}/claimCaseController/caseDetail?caseId="+claimCaseId);
        }

        $(document).ready(function (){

            $('#caseNo-span').on('click','a',caseDetail);
            $('#case-reopen-span').on('click','a',caseDetail);

            //查询赔付物可退回状态
            function queryObjectState(objId, callback) {
                if (!objId) {
                    callback(null);
                    return;
                }
                $.ajax({
                    url: "${ctx}/restfull/applicationReviewTaskController/getCaseObjPreReturnState",
                    type: 'POST',
                    data: objId,
                    async: false,
                    cache: false,
                    dataType: 'json',
                    contentType: 'application/json;charset=utf-8',
                    processData: false,
                    success: function (data) {
                        callback(data);
                    },
                    error: function (data) {
                       callback(data);
                    }
                });
            }

            //查询审核人员
            async function findReviewManager(applyType, callback) {
                if (!applyType) {
                    callback(null);
                    return;
                }
                await $.ajax({
                    url: "${ctx}/restfull/applicationReviewTaskController/getApplicationManager",
                    type: 'POST',
                    data: applyType,
                    async: false,
                    cache: false,
                    dataType: 'json',
                    contentType: 'application/json;charset=utf-8',
                    processData: false,
                    success: function (data) {
                        if (data.data) {
                            if (callback) {
                                callback(data.data);
                            }
                        } else {
                            callback(null);
                        }
                    },
                    error: function (data) {
                        callback(null);
                    }
                });

            }

            //账号申请查询分配角色
            function findAllocationRole(callback) {
               $.ajax({
                    url: "${ctx}/restfull/applicationReviewTaskController/findAllocationRole",
                    type: 'POST',
                    data: '',
                    async: false,
                    cache: false,
                    dataType: 'json',
                    contentType: 'application/json;charset=utf-8',
                    processData: false,
                    success: function (data) {
                        if (data.data) {
                            if (callback) {
                                callback(data.data);
                            }
                        } else {
                            callback(null);
                        }
                    },
                    error: function (data) {
                        callback(null);
                    }
                });

            }

            //刷新审核人员选择框
            function reviewMangerSelect (res) {
                    let reviewManager = $('#case-obj-review-manager');
                    reviewManager.empty();
                    if (!res) {
                        reviewManager.append('<option value="" disabled>暂无审核人员</option> ');
                    } else {
                        reviewManager.append('<option value="" disabled>请选择</option> ');
                        $.each(res, function(key, res) {
                            reviewManager.append(new Option(res.name, res.id));
                        });
                    }
                    reviewManager.find("option").eq(0).attr("selected", "true");
                    form.render("select");
            }



        layui.use('table', function(){
            let table = layui.table;
            let laydate = layui.laydate;
            let form = layui.form;
            let element = layui.element;
            let util = layui.util;

            //案件赔付物状态退回
            form.on('input-affix(searchCaseObj)', function(data){
                searchCaseObj(data);
            });
            //案件重开查询
            form.on('input-affix(searchCaseReopen)',function (data) {
                searchReopenCase(data);
            });

            //页面标签选择
            element.on('tab(choose-tab)', function(data){
                $('#data-form')[0].reset();
                form.render();
                let applicationTag = this.id;
                $('#application-tag').val(applicationTag);
                table.reload('fullTable', {
                    page: {
                        curr: 1 // 重新从第 1 页开始
                    },
                    where: {'applicationTag':applicationTag} // 搜索的字段
                });
            });
            //日期加载
            laydate.render({
                elem: '.demo-table-search-date'
            });
            // 搜索提交
            form.on('submit(data-table-search)', function(data){
                let field = data.field; // 获得表单字段
                // 执行搜索重载
                table.reload('fullTable', {
                    page: {
                        curr: 1 // 重新从第 1 页开始
                    },
                    where: field // 搜索的字段
                });
                layer.msg('搜索成功');
                return false; // 阻止默认 form 跳转
            }
            );
            let applyId;
            // 已知数据渲染
            let mainTable = table.render({
                elem: '#fullTable',
                id: 'fullTable',
                cols: [[ //标题栏
                    {field: 'extendField', align: 'center', title: '任务名称'},
                    {field: 'applyType', align: 'center', title: '申请类型', templet: function(d) {
                        if (d.applyType === '0') {
                            return '赔付物状态退回';
                        } else if (d.applyType === '1') {
                            return '案件重开';
                        } else if (d.applyType === '2') {
                            return '账号申请';
                        } else {
                            return '未知类型';
                        }
                    }},
                    {field: 'creator', align: 'center', title: '申请人'},
                    {field: 'createTime',align: 'center', title: '申请时间', sort: true, templet: function(d) {
                            return util.toDateString(d.createTime, 'yyyy-MM-dd HH:mm:ss');
                        }},
                    {field: 'approvalTaskList', align: 'center', title: '审批人', templet: function(d) {
                            let approvalTaskList = d.approvalTaskList;
                            const approvalUserNames = approvalTaskList.map(approval => approval.approvalUserName);
                            return approvalUserNames.join(", ");
                        }},
                    {field: 'taskStatus', align: 'center', title: '审批状态', sort: true, templet: function(d) {
                            if (d.taskStatus === '-1') {
                                return '已取消';
                            } else if (d.taskStatus === '0') {
                                return '待审核';
                            } else if (d.taskStatus === '1') {
                                return '审批中';
                            } else if (d.taskStatus === '2') {
                                return '已驳回';
                            } else if (d.taskStatus === '3'){
                                return '已通过';
                            } else {
                                return '未知状态';
                            }
                    }},
                    {field: 'createTime',align: 'center', title: '审批时间', sort: true, templet: function(d) {
                            if (d.taskStatus === '3' || d.taskStatus === '2') {
                                return util.toDateString(d.modifyTime, 'yyyy-MM-dd HH:mm:ss');
                            }
                            return "";
                        }},

                    {fixed: 'right', align: 'center', title:'操作', templet: function (d) {
                        let applicationTag = $('#application-tag').val();
                        let tableBtn = "查看"
                        if(applicationTag === 'approval' && (d.taskStatus === '0' || d.taskStatus === '1')) {
                            tableBtn = "审核";
                            return ' <div class="layui-clear-space">\n' +
                                '            <a class="layui-btn layui-btn-xs layui-bg-orange" lay-event="edit">'+tableBtn+'</a>\n' +
                                '        </div>';
                        }
                        return ' <div class="layui-clear-space">\n' +
                                '            <a class="layui-btn layui-btn-xs" lay-event="edit">'+tableBtn+'</a>\n' +
                                '        </div>';

                        }}
                ]],
                url: '${ctx}/restfull/applicationReviewTaskController/findAllList',
                method: 'POST',
                contentType: 'application/json',
                response: {
                    statusCode: 200
                },
                request: {
                    pageName: 'pageNum',
                    limitName: 'pageSize'
                },
                parseData: function(res) { // res 即为原始返回的数据
                    return {
                        "code": res.code,
                        "msg": res.msg, // 解析提示文本
                        "count": res.data.total, // 解析数据长度
                        "data": res.data.list // 解析数据列表
                    };
                },
                skin: 'line', // 表格风格
                even: false,
                page: true, // 是否显示分页
                limits: [10],
                limit: 10, // 每页默认显示的数量
                done: function () {
                    let id = this.id;
                    table.on('tool(fullTable)', function(obj){ // 双击 toolDouble
                        let data = obj.data; // 获得当前行数据
                        $.ajax({
                            url: "${ctx}/restfull/applicationReviewTaskController/getApplicationTaskLog",
                            type: 'POST',
                            data: data.id,
                            async: false,
                            cache: false,
                            dataType: 'json',
                            contentType: 'application/json;charset=utf-8',
                            processData: false,
                            success: function (data) {
                                let logData = $('#log-timeline-data');
                                logData.empty();
                                //任务日志记录
                                for (let logLine of data.data) {
                                    let ATime = util.toDateString(logLine.createTime, 'yyyy-MM-dd');
                                    let claimCaseStatus = logLine.claimCaseNo??'';
                                    let appendData = '<div class="layui-timeline-item">\n' +
                                        '                <i class="layui-icon layui-timeline-axis"></i>\n' +
                                        '                <div class="layui-timeline-content layui-text">\n' +
                                        '                    <h3 class="layui-timeline-title">'+ATime+'</h3>\n' +
                                                           '<p>\n'+
                                                        ''+claimCaseStatus+'' +
                                                        '                    </p>\n' +
                                        '                    <p>\n' +
                                        ''+logLine.description+'' +
                                        '                    </p>\n' +
                                        '                </div>\n' +
                                        '            </div>';
                                    logData.append(appendData);
                                }
                            },
                            error: function (data) {

                            }
                        });
                        if(obj.event === 'edit'){
                            let applicationTag = $('#application-tag').val();
                            let workButtonDiv = $('#application-work-button');
                            let workButton;
                            //通过、驳回、取消无操作按钮
                            if (data.taskStatus === '3' || data.taskStatus === '2' || data.taskStatus === '-1') {
                                workButton = '<button type="button" class="layui-btn layui-bg-blue" lay-on="shut">关闭</button>'
                            } else {
                                //申请人操作
                                if (applicationTag === 'apply') {
                                    workButton = '<button type="button" class="layui-btn layui-bg-blue" lay-on="shut">关闭</button>\n' +
                                        '        <button type="button" class="layui-btn layui-bg-blue" lay-on="cancel">撤销</button>'
                                    workButtonDiv.html(workButton);
                                } else if (applicationTag === 'approval') { //审批人操作
                                    workButton = '<button type="button" class="layui-btn layui-bg-blue" lay-on="pass">通过</button>\n' +
                                        '        <button type="button" class="layui-btn layui-bg-blue" lay-on="reject">驳回</button>'
                                } else { //全部任务查询无操作按钮
                                    workButton = '<button type="button" class="layui-btn layui-bg-blue" lay-on="shut">关闭</button>'
                                }
                            }

                            workButtonDiv.html(workButton);
                            applyId = data.id;
                            let taskNo = data.extendField;
                            layer.open({
                                title: '任务名称:'+ taskNo,
                                type: 1,
                                shade: 0.3,
                                shadeClose: true,
                                area: ['60%','60%'],
                                content: $('#application-work')
                            });
                        }
                    });
                }
            });

            util.on('lay-on',{
                //新建申请
                'new-apply': function(){
                    layer.open({
                        type: 1,
                        shade: 0.3,
                        shadeClose: true,
                        title: '新建申请',
                        area: ['80%', '80%'],
                        content: $('#caseStatusReturn'),
                        success: function(){
                            element.on('tab(obj-tab)', function(data){
                                let reviewManager;
                                if (data.id && data.id === 'case-reopen') {
                                    reviewManager = $('#case-reopen-review-manager');
                                } else if (data.id === 'account-apply'){
                                    let roleSelect = $('#role');
                                    reviewManager = $('#account-review-manager');
                                    //账号申请查询所有可申请的角色
                                    $.ajax({
                                        url: "${ctx}/restfull/applicationReviewTaskController/findAllocationRole",
                                        type: 'POST',
                                        data: '',
                                        async: false,
                                        cache: false,
                                        dataType: 'json',
                                        contentType: 'application/json;charset=utf-8',
                                        processData: false,
                                        success: function (data) {
                                            if (data.success) {
                                                roleSelect.empty();
                                                if (!data.data) {
                                                    roleSelect.append('<option value="">无可申请角色</option> ');
                                                } else {
                                                    roleSelect.append('<option value="" disabled>请选择</option> ');
                                                    $.each(data.data, function(key, res) {
                                                        roleSelect.append(new Option(res.name, res.id));
                                                    });
                                                    roleSelect.find("option").eq(0).attr("selected", "true");
                                                }
                                            } else {

                                            }
                                        },
                                        error: function (data) {

                                        }
                                    });
                                } else {
                                    reviewManager = $('#case-obj-review-manager');
                                }
                                //查询审核人员
                                findReviewManager(data.id,function (res) {
                                    reviewManager.empty();
                                    if (!res) {
                                        // reviewManager.append('<option value="">暂无审核人员</option> ');
                                    } else {
                                        reviewManager.append('<option value="" disabled>请选择</option> ');
                                        $.each(res, function(key, res) {
                                            reviewManager.append(new Option(res.name, res.id));
                                        });
                                    }
                                    reviewManager.find("option").eq(0).attr("selected", "true");
                                    form.render("select");
                                });
                            });

                            let objBack = $('#obj-back');
                            let caseReopen = $('#case-reopen');
                            let accountApply = $('#account-apply');
                            let applyType = "";
                            if (objBack.length > 0) {
                                applyType = 'obj-back'
                            } else if (caseReopen.length > 0) {
                                applyType = 'case-reopen';
                            } else if (accountApply.length > 0) {
                                applyType = 'account-apply'
                            }
                            element.tabChange('obj-tab', applyType);

                            //案件赔付物退回提交申请
                            form.on('submit(case-obj-re-submit)',function(data){
                                let errMsg = "";
                                let pass = true;
                                let dataJson = data.field;
                                if (!dataJson.claimCaseNo) {
                                    errMsg += "案件号不能为空<br>";
                                    pass = false;
                                }
                                if (!dataJson.claimCaseObjectId) {
                                    errMsg += "案件赔付物不能为空<br>";
                                    pass = false;
                                }
                                if (!dataJson.returnState) {
                                    errMsg += "退回状态不能为空<br>";
                                    pass = false;
                                }
                                if (!dataJson.reviewManager) {
                                    errMsg += "审核人不能为空<br>";
                                    pass = false;
                                }
                                if (!dataJson.applyReason) {
                                    errMsg += "审请原因不能为空<br>";
                                    pass = false;
                                } else if (dataJson.applyReason.length < 3){
                                    errMsg += "申请原因不能少于三个字符<br>";
                                    pass = false;
                                }
                                if (!pass) {
                                    layer.msg(errMsg, {icon: 0}, function(){
                                    });
                                    return false;
                                }
                                dataJson.applyType = "0";
                                //提交申请
                                $.ajax({
                                    url: "${ctx}/restfull/applicationReviewTaskController/submitApply",
                                    type: 'POST',
                                    data: JSON.stringify(dataJson),
                                    async: false,
                                    cache: false,
                                    dataType: 'json',
                                    contentType: 'application/json;charset=utf-8',
                                    processData: false,
                                    success: function (data) {
                                        if (data.success) {
                                            layer.msg("申请成功", {icon: 1,time: 2000,shade: 0.3}, function(){
                                                layer.closeAll('page');
                                                let applicationTag = $('#application-tag').val();
                                                layui.table.reload('fullTable', {
                                                    page: {
                                                        curr: 1, // 重新从第 1 页开始
                                                        where: {'applicationTag':applicationTag}
                                                    }
                                                });
                                                return false;
                                            });
                                        } else {
                                            layer.msg(data.msg, {icon: 2,time: 2000,shade: 0.3}, function(){

                                            });
                                        }
                                    },
                                    error: function (data) {
                                        layer.msg(data.msg, {icon: 2,time: 2000,shade: 0.3}, function(){

                                        });
                                    }
                                });
                                return false;
                            });
                            //案件重开提交申请
                            form.on('submit(case-reopen-submit)',function(data){
                                let errMsg = "";
                                let pass = true;
                                let dataJson = data.field;
                                if (!dataJson.claimCaseNo) {
                                    errMsg += "案件号不能为空<br>";
                                    pass = false;
                                }
                                if (!dataJson.returnState) {
                                    errMsg += "退回状态不能为空<br>";
                                    pass = false;
                                }
                                if (!dataJson.reviewManager) {
                                    errMsg += "审核人不能为空<br>";
                                    pass = false;
                                }
                                if (!dataJson.applyReason) {
                                    errMsg += "审请原因不能为空<br>";
                                    pass = false;
                                } else if (dataJson.applyReason.length < 3){
                                    errMsg += "申请原因不能少于三个字符<br>";
                                    pass = false;
                                }
                                if (!pass) {
                                    layer.msg(errMsg, {icon: 0}, function(){
                                    });
                                    return false;
                                }
                                dataJson.applyType = "1";
                                //提交申请
                                $.ajax({
                                    url: "${ctx}/restfull/applicationReviewTaskController/submitApply",
                                    type: 'POST',
                                    data: JSON.stringify(dataJson),
                                    async: false,
                                    cache: false,
                                    dataType: 'json',
                                    contentType: 'application/json;charset=utf-8',
                                    processData: false,
                                    success: function (data) {
                                        if (data.success) {
                                            layer.msg("申请成功", {icon: 1,time: 2000,shade: 0.3}, function(){
                                                layer.closeAll('page');
                                                let applicationTag = $('#application-tag').val();
                                                layui.table.reload('fullTable', {
                                                    page: {
                                                        curr: 1, // 重新从第 1 页开始
                                                        where: {'applicationTag':applicationTag}
                                                    }
                                                });
                                                return false;
                                            });
                                        } else {
                                            layer.msg(data.msg, {icon: 2,time: 2000,shade: 0.3}, function(){

                                            });
                                        }

                                    },
                                    error: function (data) {
                                        layer.msg(data.msg, {icon: 2,time: 2000,shade: 0.3}, function(){

                                        });
                                    }
                                });
                                return false;
                            });
                            //账号申请提交申请
                            form.on('submit(account-apply-submit)',function(data){
                                let errMsg = "";
                                let pass = true;
                                let dataJson = data.field;
                                if (!dataJson.userName) {
                                    errMsg += "账号不能为空<br>";
                                    pass = false;
                                }
                                if (!dataJson.password) {
                                    errMsg += "密码不能为空<br>";
                                    pass = false;
                                }
                                if (!dataJson.realName) {
                                    errMsg += "真实姓名不能为空<br>";
                                    pass = false;
                                }
                                if (!dataJson.eSingnName) {
                                    errMsg += "e签宝姓名不能为空<br>";
                                    pass = false;
                                }
                                if (!dataJson.mobile) {
                                    errMsg += "手机号不能为空<br>";
                                    pass = false;
                                } else {
                                        let reg = /^1[3456789]\d{9}$/;
                                        // ^1  以1开头
                                        // [3456789] 第2位，使用原子表里的任意一个原子都可以
                                        // \d{9}$  第三位  朝后可以是任意数字  并且最后结尾必须是数字
                                        if(!reg.test(dataJson.mobile)){
                                            errMsg += "手机号格式不正确<br>";
                                            pass = false;
                                        }
                                }
                                if (!dataJson.email) {
                                    errMsg += "邮箱不能为空<br>";
                                    pass = false;
                                } else {
                                    let emailPat=/^(.+)@(.+)$/;
                                    let matchArray= dataJson.email.match(emailPat);
                                    if (matchArray == null) {
                                        errMsg += "邮箱格式不正确<br>";
                                        pass = false;
                                    }
                                }
                                if (!dataJson.role) {
                                    errMsg += "角色不能为空<br>";
                                    pass = false;
                                }
                                if (!dataJson.reviewManager) {
                                    errMsg += "审核人不能为空<br>";
                                    pass = false;
                                }
                                if (!dataJson.applyReason) {
                                    errMsg += "申请原因不能为空<br>";
                                    pass = false;
                                } else if (dataJson.applyReason.length < 3){
                                    errMsg += "申请原因不能少于三个字符<br>";
                                    pass = false;
                                }
                                if (!pass) {
                                    layer.msg(errMsg, {icon: 0}, function(){
                                    });
                                    return false;
                                }
                                dataJson.applyType = "2";
                                //提交申请
                                $.ajax({
                                    url: "${ctx}/restfull/applicationReviewTaskController/submitApply",
                                    type: 'POST',
                                    data: JSON.stringify(dataJson),
                                    async: false,
                                    cache: false,
                                    dataType: 'json',
                                    contentType: 'application/json;charset=utf-8',
                                    processData: false,
                                    success: function (data) {
                                        if (data.success) {
                                            layer.msg("申请成功", {icon: 1,time: 2000,shade: 0.3}, function(){
                                                layer.closeAll('page');
                                                let applicationTag = $('#application-tag').val();
                                                layui.table.reload('fullTable', {
                                                    page: {
                                                        curr: 1, // 重新从第 1 页开始
                                                        where: {'applicationTag':applicationTag}
                                                    }
                                                });
                                                return false;
                                            });
                                        } else {
                                            layer.msg(data.msg, {icon: 2,time: 2000,shade: 0.3}, function(){

                                            });
                                        }

                                    },
                                    error: function (data) {
                                        layer.msg(data.msg, {icon: 2,time: 2000,shade: 0.3}, function(){

                                        });
                                    }
                                });
                                return false;
                            });
                        },
                        //关闭申请框
                        end: function(){
                            $('#obj-back-form')[0].reset();
                            $('#case-reopen-form')[0].reset();
                            $('#apply-account-form')[0].reset();
                            form.render();
                            resetAppend('案件号','caseObj');
                            resetAppend('案件号','reopenCase');
                        }
                    });
                },
                //审核通过
                "pass": function () {
                    $.ajax({
                        url: "${ctx}/restfull/applicationReviewTaskController/passApplication",
                        type: 'POST',
                        data: applyId,
                        async: false,
                        cache: false,
                        dataType: 'json',
                        contentType: 'application/json;charset=utf-8',
                        processData: false,
                        success: function (data) {
                            if (data.success) {
                                layer.msg("成功", {icon: 1,time: 2000,shade: 0.3}, function(){
                                    layer.closeAll('page');
                                    layui.table.reload('fullTable', {
                                        page: {
                                            curr: 1 // 重新从第 1 页开始
                                        }
                                    });
                                    return false;
                                });
                            } else {
                                layer.msg(data.msg, {icon: 2,time: 2000,shade: 0.3}, function(){

                                });
                            }
                        },
                        error: function (data) {

                        }
                    });
                },
                //驳回
                "reject": function () {
                    layer.prompt({
                        formType: 2,
                        maxlength: 100,
                        value: ' ',
                        shade: 0.3,
                        shadeClose: true,
                        title: '请输入驳回原因',
                        area: ['500px', '300px']
                    }, function (value, index, elem) {
                        if (value === ' ') {
                            layer.msg('驳回原因不能为空', {icon: 2,time: 2000,shade: 0.3}, function(){

                            });
                            return;
                        }
                        $.ajax({
                            url: "${ctx}/restfull/applicationReviewTaskController/rejectApplication",
                            type: 'POST',
                            data: JSON.stringify({'applyId':applyId,'rejectReason':value}),
                            async: false,
                            cache: false,
                            dataType: 'json',
                            contentType: 'application/json;charset=utf-8',
                            processData: false,
                            success: function (data) {
                                if (data.success) {
                                    layer.msg("成功", {icon: 1,time: 2000,shade: 0.3}, function(){
                                        layer.closeAll('page');
                                        layui.table.reload('fullTable', {
                                            page: {
                                                curr: 1 // 重新从第 1 页开始
                                            }
                                        });
                                        return false;
                                    });
                                } else {
                                    layer.msg(data.msg, {icon: 2,time: 2000,shade: 0.3}, function(){

                                    });
                                }
                            },
                            error: function (data) {

                            }
                        });

                    });
                },
                //关闭
                "shut": function () {
                    layer.closeAll();
                },
                //撤销
                "cancel": function () {
                    $.ajax({
                        url: "${ctx}/restfull/applicationReviewTaskController/cancelApplication",
                        type: 'POST',
                        data: applyId,
                        async: false,
                        cache: false,
                        dataType: 'json',
                        contentType: 'application/json;charset=utf-8',
                        processData: false,
                        success: function (data) {
                            if (data.success) {
                                layer.msg("成功", {icon: 1,time: 2000,shade: 0.3}, function(){
                                    layer.closeAll('page');
                                    layui.table.reload('fullTable', {
                                        page: {
                                            curr: 1 // 重新从第 1 页开始
                                        }
                                    });
                                    return false;
                                });
                            } else {
                                layer.msg(data.msg, {icon: 2,time: 2000,shade: 0.3}, function(){

                                });
                            }
                        },
                        error: function (data) {

                        }
                    });
                }
            })

            //案件赔付物状态退回
            function searchCaseObj(data) {
                let elem = data.elem; // 输入框
                let value = elem.value; // 输入框的值
                if(!value){
                    layer.msg('填写案件号');
                    return elem.focus()
                };
                $.ajax({
                    url: "${ctx}/restfull/applicationReviewTaskController/findCaseObjectByCaseNo",
                    type: 'POST',
                    data: value,
                    async: false,
                    cache: false,
                    dataType: 'json',
                    contentType: 'application/json;charset=utf-8',
                    processData: false,
                    success: function (data) {
                        let icon = $('#query-case-icon');
                        let caseSpan =  $('#caseNo-span');
                        if (data.data) {
                            icon.addClass("layui-icon-success");
                            let claimCaseId = Object.values(data.data)[0][0].claimCaseId;
                            caseSpan.html('<span>查询成功  </span><a href="#" id="case-detail" case-id="'+claimCaseId+'">查看案件详情</a>');
                            let caseObj =  $('#case-obj-list');
                            caseObj.empty();
                            caseObj.append('<option value="" disabled>请选择</option> ');
                            $.each(data.data, function(groupLabel, options) {
                                let groupName;
                                //claimCaseObject  type + category = groupLabel
                                switch (groupLabel) {
                                    case '11':
                                        groupName = "骑手人伤"
                                        break;
                                    case '21':
                                        groupName = "三者人伤";
                                        break;
                                    case '22':
                                        groupName = "三者物损";
                                        break;
                                    case '23':
                                        groupName = "三者车损";
                                        break;
                                    default:
                                        groupName = "其它";
                                }
                                let $optgroup = $('<optgroup>').attr('label', groupName);
                                $.each(options, function(index, optionData) {
                                    let option = new Option(optionData.name, optionData.id);
                                    $optgroup.append(option);
                                });
                                caseObj.append($optgroup);
                            });
                            caseObj.find("option").eq(0).attr("selected", "true");
                            form.render("select");
                            //选择赔付物后选择退回状态
                            form.on('select(obj-select-filter)',function(data) {
                                let val = data.value;
                                let returnState = $('#return-state');
                                returnState.empty();
                                if (!val) {
                                    returnState.append('<option value="" disabled>请选择</option> ');
                                    form.render("select");
                                    return;
                                }
                                queryObjectState(val,function (res) {
                                    if (!res.data) {
                                        returnState.append('<option value="" disabled>'+res.msg+'</option> ');
                                    } else {
                                        returnState.append('<option value="" disabled>请选择</option> ');
                                        $.each(res.data, function(key, res) {
                                            if (key == 'rawStatus') {
                                                $('#obj-raw-status').val(res);
                                                return;
                                            }
                                            returnState.append(new Option(res, key));
                                        });
                                    }
                                    returnState.find("option").eq(0).attr("selected", "true");
                                    form.render("select");
                                });

                            });
                        } else {
                            $('#query-case-icon').addClass("layui-icon-error");
                            resetAppend(data.msg,'caseObj');
                        }
                    },
                    error: function (data) {
                        $('#query-case-icon').addClass("layui-icon-error");
                    }
                });
            }

            //案件重开
            function searchReopenCase(data) {
                let elem = data.elem; // 输入框
                let value = elem.value; // 输入框的值
                if(!value){
                    layer.msg('填写案件号');
                    return elem.focus()
                };
                $.ajax({
                    url: "${ctx}/restfull/applicationReviewTaskController/findCaseCanReopenByCaseNo",
                    type: 'POST',
                    data: value,
                    async: false,
                    cache: false,
                    dataType: 'json',
                    contentType: 'application/json;charset=utf-8',
                    processData: false,
                    success: function (data) {
                        let icon = $('#case-reopen-icon');
                        let caseSpan =  $('#case-reopen-span');
                        if (data.data) {
                            $('#case-raw-status').val(data.data.rawStatus);
                            icon.addClass("layui-icon-success");
                            let claimCaseId = data.data.claimCaseId;
                            caseSpan.html('<span>查询成功  </span><a href="#" id="case-detail" case-id="'+claimCaseId+'">查看案件详情</a>');
                            //选择重开状态
                            let returnState = $('#case-reopen-status');
                            returnState.empty();
                            returnState.append('<option value="" disabled>请选择</option> ');
                            data.data.res.forEach((item) => {
                                for (const [key, value] of Object.entries(item)) {
                                    returnState.append(new Option(value, key));
                                }
                            });
                            returnState.find("option").eq(0).attr("selected", "true");
                            form.render("select");
                        } else {
                            $('#case-reopen-status').addClass("layui-icon-error");
                            resetAppend(data.msg,'reopenCase');
                        }
                    },
                    error: function (data) {
                        $('#case-reopen-status').addClass("layui-icon-error");
                    }
                });
            }
        });
        })

        //重置表单
        function resetAppend(caseSpanStr,tag) {
            let caseSpan;
            let icon;
            if (tag === 'caseObj') {
                caseSpan =  $('#caseNo-span');
                icon = $('#query-case-icon');
                $('#case-obj-list').empty();
                $('#return-state').empty();
            } else if (tag === 'reopenCase') {
                caseSpan =  $('#case-reopen-span');
                icon = $('#case-reopen-icon');
                $('#case-reopen-status').empty();
            }
            caseSpan.html(caseSpanStr);
            icon.removeAttr("class");
            icon.addClass("layui-icon");
        }
    </script>
</head>
<body>
<div id="application-work" style="display: none">
    <div style="margin: 30px" id="application-work-data">
        <div class="layui-timeline" id="log-timeline-data">

        </div>
    </div>
    <div id="application-work-button" style="position: absolute;right: 0px;bottom: 0px;margin: 50px">

    </div>
</div>
<div id="caseStatusReturn" style="display: none;">
    <div class="layui-tab layui-tab-brief" lay-filter="obj-tab">
        <ul class="layui-tab-title">
            <@shiro.hasPermission name="APPLICATION_OBJ_BACK">
            <li class="layui-this" lay-id="obj-back" id="obj-back">赔付物状态退回</li>
            </@shiro.hasPermission>
            <@shiro.hasPermission name="APPLICATION_CASE_REOPEN">
            <li lay-id="case-reopen" id="case-reopen">案件重开</li>
            </@shiro.hasPermission>
            <@shiro.hasPermission name="APPLICATION_ACCOUNT">
            <li lay-id="account-apply" id="account-apply">账号申请</li>
            </@shiro.hasPermission>
        </ul>
        <div class="layui-tab-content">
            <@shiro.hasPermission name="APPLICATION_OBJ_BACK">
            <div class="layui-tab-item">
                <form class="layui-form layui-form-pane" action="" style="padding: 30px" id="obj-back-form">
                    <input type="text" hidden="hidden" id="obj-raw-status" name="rawStatus" value=""/>

                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">案件号</label>
                            <div class="layui-input-inline">
                                <div class="layui-input-wrap" style="width: 117%">
                                    <input type="text" name="claimCaseNo" maxlength="50" lay-affix="search" lay-filter="searchCaseObj" lay-options="{split: true}" placeholder="搜索…" class="layui-input">
                                </div>
                            </div>
                            <i class="layui-icon" id="query-case-icon" style="font-size: 20px; color: #1E9FFF;"></i>
                            <div class="layui-form-mid layui-text-em" id="caseNo-span" style="margin-left: 30px">案件号</div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                                <label class="layui-form-label">赔付物</label>
                                <div class="layui-input-inline">
                                    <select lay-search="" id="case-obj-list" name="claimCaseObjectId" lay-filter="obj-select-filter" >

                                    </select>
                                </div>
                        </div>
                        <div class="layui-inline">
                                <label class="layui-form-label">退回状态</label>
                                <div class="layui-input-inline">
                                    <select name="returnState" lay-filter="return-state" id="return-state" >

                                    </select>
                                </div>
                            </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">审核人</label>
                        <div class="layui-input-inline">
                            <select lay-search="" lay-filter="review-manager" id="case-obj-review-manager" name="reviewManager" >
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">申请原因</label>
                        <div class="layui-input-block">
                            <textarea placeholder="请输入内容" maxlength="100" class="layui-textarea" name="applyReason"></textarea>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <button class="layui-btn" lay-submit lay-filter="case-obj-re-submit">确认</button>
                        <button type="reset" class="layui-btn layui-btn-primary" id="apply-reset" onclick="resetAppend('案件号','caseObj')">重置</button>
                    </div>
                </form>
            </div>
            </@shiro.hasPermission>
            <@shiro.hasPermission name="APPLICATION_CASE_REOPEN">
            <div class="layui-tab-item">
                <form class="layui-form layui-form-pane" action="" style="padding: 30px" id="case-reopen-form">
                    <input type="text" hidden="hidden" id="case-raw-status" name="rawStatus" value=""/>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">案件号</label>
                            <div class="layui-input-inline">
                                <div class="layui-input-wrap" style="width: 117%">
                                    <input type="text" name="claimCaseNo" maxlength="50" lay-affix="search" lay-filter="searchCaseReopen" lay-options="{split: true}" placeholder="搜索…" class="layui-input">
                                </div>
                            </div>
                            <i class="layui-icon" id="case-reopen-icon" style="font-size: 20px; color: #1E9FFF;"></i>
                            <div class="layui-form-mid layui-text-em" id="case-reopen-span" style="margin-left: 30px">案件号</div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">退回状态</label>
                            <div class="layui-input-inline">
                                <select name="returnState" lay-filter="case-reopen-status" id="case-reopen-status" >

                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">审核人</label>
                        <div class="layui-input-inline">
                            <select lay-search="" lay-filter="review-manager" id="case-reopen-review-manager" name="reviewManager" >
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">申请原因</label>
                        <div class="layui-input-block">
                            <textarea placeholder="请输入内容" maxlength="100" class="layui-textarea" name="applyReason" ></textarea>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <button class="layui-btn" lay-submit lay-filter="case-reopen-submit">确认</button>
                        <button type="reset" class="layui-btn layui-btn-primary" id="case-reopen-reset" onclick="resetAppend('案件号','reopenCase')">重置</button>
                    </div>
                </form>
            </div>
            </@shiro.hasPermission>
            <@shiro.hasPermission name="APPLICATION_ACCOUNT">
            <div class="layui-tab-item">
                <form class="layui-form layui-form-pane" id="apply-account-form" action="" style="padding: 30px">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">账号</label>
                            <div class="layui-input-inline">
                                <input type="text" name="userName" autocomplete="off" placeholder="账号" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">密码</label>
                        <div class="layui-input-inline layui-input-wrap">
                            <input type="password" name="password" placeholder="密码" autocomplete="off" lay-affix="eye" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">真实姓名</label>
                            <div class="layui-input-inline">
                                <input type="text" name="realName" autocomplete="off" placeholder="姓名" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">e签宝姓名</label>
                            <div class="layui-input-inline">
                                <input type="text" name="eSingnName" autocomplete="off" placeholder="e签宝姓名" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">手机</label>
                            <div class="layui-input-inline">
                                <input type="text" name="mobile" autocomplete="off" class="layui-input"  placeholder="手机号" >
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">邮箱</label>
                            <div class="layui-input-inline">
                                <input type="text" name="email" autocomplete="off" class="layui-input"  placeholder="邮箱" >
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">角色</label>
                        <div class="layui-input-inline">
                            <select lay-search="" id="role" name="role" >
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">审核人</label>
                        <div class="layui-input-inline">
                            <select lay-search="" lay-filter="review-manager" id="account-review-manager" name="reviewManager" >
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">申请原因</label>
                        <div class="layui-input-block">
                            <textarea placeholder="请输入内容" maxlength="100" class="layui-textarea" name="applyReason"></textarea>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <button class="layui-btn" lay-submit lay-filter="account-apply-submit">确认</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </form>
            </div>
            </@shiro.hasPermission>
        </div>
    </div>
</div>
<div class="portlet light portlet-fit bordered">
    <div class="portlet-title">
        <ul class="page-breadcrumb breadcrumb">
            <li><span>新职业平台</span> <i class="fa fa-circle"></i></li>
            <li><span class="active">运营工作台</span></li>
        </ul>
    </div>
    <div class="portlet-body">
        <form class="layui-form layui-row layui-col-space16" id="data-form">
            <div class="layui-col-md4">
                <div class="layui-input-wrap">
                    <div class="layui-input-wrap">
                        <select lay-affix="clear" name="applicationStatus">
                            <option value="">任务状态</option>
                            <option value="-1">已撤回</option>
                            <option value="0">待审批</option>
                            <option value="1">审批中</option>
                            <option value="2">驳回</option>
                            <option value="3">通过</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-col-md4">
                <div class="layui-input-wrap">
                    <select lay-affix="clear" name="applyType">
                        <option value="">申请类型</option>
                        <option value="0">赔付物状态退回</option>
                        <option value="1">案件重开</option>
                        <option value="2">账号申请</option>
                    </select>
                </div>
            </div>
            <div class="layui-col-md4">
                <div class="layui-input-wrap">
                    <div class="layui-input-prefix">
                        <i class="layui-icon layui-icon-date"></i>
                    </div>
                    <input type="text" name="createDate" readonly placeholder="日期"
                           class="layui-input demo-table-search-date">
                </div>
            </div>
            <input type="text" hidden="hidden" id="application-tag" value="apply" name="applicationTag"/>
            <div class="layui-btn-container layui-col-xs12" style="text-align: right">
                <button class="layui-btn" lay-submit lay-filter="data-table-search">查询</button>
                <button type="reset" class="layui-btn layui-btn-primary" id="data-list-reset">清除</button>
                <@shiro.hasPermission name="APPLICATION_CREATE">
                <button type="button" class="layui-btn layui-btn-primary" lay-on="new-apply">新建申请</button>
                </@shiro.hasPermission>
            </div>
        </form>
        <div class="layui-tab layui-tab-card" lay-filter="choose-tab">
            <ul class="layui-tab-title">
                <@shiro.hasPermission name="QUERY_ALL_APPLICATION">
                <li id="all">所有申请任务</li>
                </@shiro.hasPermission>
                <li class="layui-this" id="apply">我的申请</li>
                <li id="approval">待我审批</li>
            </ul>
            <table class="layui-hide" id="fullTable"></table>
        </div>
    </div>
</div>
</body>
</html>