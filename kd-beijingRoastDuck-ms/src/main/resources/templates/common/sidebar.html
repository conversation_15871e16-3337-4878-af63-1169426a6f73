<!-- BEGIN SIDEBAR -->
<div class="page-sidebar-wrapper">
	<!-- BEGIN SIDEBAR -->
	<!-- DOC: Set data-auto-scroll="false" to disable the sidebar from auto scrolling/focusing -->
	<!-- DOC: Change data-auto-speed="200" to adjust the sub menu slide up/down speed -->
	<div class="page-sidebar navbar-collapse collapse">
		<!-- BEGIN SIDEBAR MENU -->
		<!-- DOC: Apply "page-sidebar-menu-light" class right after "page-sidebar-menu" to enable light sidebar menu style(without borders) -->
		<!-- DOC: Apply "page-sidebar-menu-hover-submenu" class right after "page-sidebar-menu" to enable hoverable(hover vs accordion) sub menu mode -->
		<!-- DOC: Apply "page-sidebar-menu-closed" class right after "page-sidebar-menu" to collapse("page-sidebar-closed" class must be applied to the body element) the sidebar sub menu mode -->
		<!-- DOC: Set data-auto-scroll="false" to disable the sidebar from auto scrolling/focusing -->
		<!-- DOC: Set data-keep-expand="true" to keep the submenues expanded -->
		<!-- DOC: Set data-auto-speed="200" to adjust the sub menu slide up/down speed -->
		<ul class="page-sidebar-menu page-sidebar-menu-light page-header-fixed"
			data-keep-expanded="false" data-auto-scroll="true" data-slide-speed="200" style="padding-top: 20px">
			
				<#list SESSION_MANAGER_RESOURCE as r>
					<#if r.parentId == null && r.isShow == 1>
						<#if r_index != 0>
								</ul>
							</li>
						</#if>

							<#if r.link!="">
                                <li class="parentNav nav-item">
									<a href="${r.link}" class="nav-link" onclick="markSelected(this);" target="contentFrame">
										<i class="icon-settings"></i> <span class="title">${r.name}</span>
										<span class="selected"></span>
									</a>
                                    <ul class="sub-menu" style="display: none">
							<#else>
                                <li class="parentNav nav-item">
									<a href="javascript:;" class="nav-link nav-toggle">
										<i class="icon-settings"></i> <span class="title">${r.name}</span>
										<span class="selected"></span>
										<span class="arrow"></span>
									</a>
                                    <ul class="sub-menu">
							</#if>

					<#else>
						<#if r.type == 1 && r.isShow == 1>
							<li class="nav-item ">
								<a href="${r.link}" class="nav-link " onclick="markSelected(this);" target="contentFrame"> 
									<span class="title">${r.name}</span>
								</a>
							</li>
						</#if>
					</#if>
				</#list>
				</ul>
			</li>
		</ul>
		<script>
			function markSelected(obj){
				$("ul.sub-menu").find(".open").first().removeClass("open active");
				$(obj).parent().addClass("open active");
			}
		</script>
		<!-- END SIDEBAR MENU -->
	</div>
	<!-- END SIDEBAR -->
</div>
<!-- END SIDEBAR -->
