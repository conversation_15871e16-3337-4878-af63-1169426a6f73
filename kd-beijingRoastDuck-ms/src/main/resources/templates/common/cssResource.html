<#include "/common/commonContext.html">
<!-- BEGIN GLOBAL MANDATORY STYLES -->
<link rel="icon" href="/favicon.ico" type="image/x-icon">

<link href="http://fonts.googleapis.com/css?family=Open+Sans:400,300,600,700&subset=all" rel="stylesheet" type="text/css" />
<link href="${ctx}/metronic/global/plugins/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css" />
<link href="${ctx}/metronic/global/plugins/simple-line-icons/simple-line-icons.min.css" rel="stylesheet" type="text/css" />
<link href="${ctx}/metronic/global/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
<link href="${ctx}/metronic/global/plugins/uniform/css/uniform.default.css" rel="stylesheet" type="text/css" />
<link href="${ctx}/metronic/global/plugins/bootstrap-switch/css/bootstrap-switch.min.css" rel="stylesheet" type="text/css" />
<!-- END GLOBAL MANDATORY STYLES -->

<!-- BEGIN THEME GLOBAL STYLES -->
<link href="${ctx}/metronic/global/css/components-rounded.min.css" rel="stylesheet" id="style_components" type="text/css" />
<link href="${ctx}/metronic/global/css/plugins.min.css" rel="stylesheet" type="text/css" />
<!-- END THEME GLOBAL STYLES -->

<!-- BEGIN THEME LAYOUT STYLES -->
<link href="${ctx}/metronic/layouts/layout/css/layout.min.css" rel="stylesheet" type="text/css" />
<link href="${ctx}/metronic/layouts/layout/css/themes/darkblue.min.css" rel="stylesheet" type="text/css" id="style_color" />
<link href="${ctx}/metronic/layouts/layout/css/custom.min.css" rel="stylesheet" type="text/css" />
<!-- END THEME LAYOUT STYLES -->

<link href="${ctx}/metronic/global/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css" rel="stylesheet" type="text/css" />
<link href="${ctx}/metronic/global/plugins/bootstrap-timepicker/css/bootstrap-timepicker.min.css" rel="stylesheet" type="text/css" />


<link href="${ctx}/plugins/layer/skin/layer.css" rel="stylesheet" type="text/css" />

<link href="${ctx}/js/custom/loaders.min.css" rel="stylesheet" type="text/css" />

<style>

/* 分页组件输入框的样式，保证高度的统一 */
.pagination .controls input {
	border: 0;
	color: #999;
	width: 30px;
	padding: 0;
	text-align: center;
}

/**
table 自适应换行 超长...
 */
.table-hide-overflow{
	table-layout:fixed;
}
.table-hide-overflow>thead>tr>th {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.table-hide-overflow>tbody>tr>td {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.label-hide-overflow{
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}


</style>

<link rel="shortcut icon" href="favicon.ico" />