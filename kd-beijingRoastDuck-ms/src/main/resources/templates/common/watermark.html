<style>
.watermark {
    position: fixed !important;
    /*top: 50% !important;*/
    /*left: 50% !important;*/
    top: 0px !important;
    /*transform: translate(-50%, -50%) rotate(-13deg) !important; !* 移动自身宽高的一半 并旋转文字 *!*/
    opacity: 0.05 !important; /* 设置水印的透明度 */
    font-size: 25px !important; /* 设置水印的字体大小 */
    color: #000 !important; /* 设置水印的颜色 */
    pointer-events: none !important; /* 确保水印不影响页面的交互 */
    user-select: none !important; /* 防止用户选择水印文本 */
    width: 120% !important;
}

.watermark-span {
    display: inline-block;
    margin: 80px;
    margin-left: 0px !important;
    transform: rotate(-40deg) !important; /* 移动自身宽高的一半 并旋转文字 */
}
</style>

<script>
    $(document).ready(function() {
        // 检查当前页面是否是登录页面
        const isLogin = window.location.pathname.includes('login');
        if (isLogin){
            return; // 如果是登录页面，不添加水印
        }
        // 调用 addWatermark 函数，添加水印
        addWatermark("${SESSION_MANAGER.userName + ' ' + SESSION_MANAGER.realName}");
    });

    // 全局变量存储 observer 实例
    let watermarkObserver = null;

    /**
     * 方法一
     * 将水印应用到整个页面上
     */
    /*function addWatermark(text){

        // 全局初始化：只创建一次 MutationObserver
        if (watermarkObserver) return; // 如果 observer 已经存在，直接返回

        watermarkObserver = new MutationObserver(function (mutations) {
            mutations.forEach(function (mutation) {
                Array.from(mutation.addedNodes).forEach(function (node) {
                    if (node.tagName === 'IFRAME') {
                        handleIframe(node); // 处理新添加的 iframe
                    }
                });
            });
        });
        watermarkObserver.observe(document.body, {
            childList: true, // 监听直接子节点的变化
            subtree: true // 监听所有子节点的变化
        });

        // 递归清理所有 iframe 水印
        function cleanAllIframeWatermarks(doc = document) {
            const iframes = doc.getElementsByTagName("iframe");
            Array.from(iframes).forEach(function (iframe) {
                try {
                    const iframeDoc = iframe.contentWindow.document;
                    const watermarks = iframeDoc.body.getElementsByClassName("watermark");
                    Array.from(watermarks).forEach(function (watermark) {
                        watermark.remove(); // 移除水印
                    });
                    // 递归处理嵌套 iframe
                    cleanAllIframeWatermarks(iframeDoc);
                } catch (e) {
                    console.warn('跨域 iframe 无法清理水印，建议同域访问：', e);
                }
            })
        };

        // 处理单个 iframe
        function handleIframe(iframe) {
            // 只使用一种事件监听方式
            iframe.onload = function () {
                try {
                    const iframeDoc = this.contentWindow.document;
                    cleanAllIframeWatermarks(iframeDoc); // 清理 iframe 内部的水印
                } catch (e) {
                    console.warn('iframe 加载后清理水印失败：', e);
                }
            };
            // 立即检查已加载的 iframe
            if (iframe.contentDocument && iframe.contentDocument.readyState === 'complete') {
                try {
                    cleanAllIframeWatermarks(iframe.contentDocument); // 清理已加载的 iframe 内部的水印
                } catch (e) {
                    console.warn('处理已加载 iframe 失败：', e);
                }
            }
        }

        // 检查最外层窗口是否已有水印
        const outerBody = window.top.document.body;
        if (outerBody.getElementsByClassName("watermark").length > 0 ) {
            // 已有水印，清理所有 iframe 内水印后返回
            cleanAllIframeWatermarks();
            return;
        }
        // 获取当前日期，将日期中的斜杠替换为点
        var loginDate = new Date().toLocaleDateString().replace(/\//g, '.');

        //  创建一个 div 元素作为水印容器
        const watermarkDiv = document.createElement('div');
        watermarkDiv.className = 'watermark';
        watermarkDiv.textContent = loginDate + " " + text;
        watermarkDiv.style.zIndex = '999999999'; // 确保水印在最上层
        outerBody.appendChild(watermarkDiv); // 将水印添加到最外层窗口

        // 清理所有 iframe 水印
        cleanAllIframeWatermarks();

        // 处理现有 iframe
        const iframes = document.getElementsByTagName("iframe");
        for (let i = 0; i < iframes.length; i++) {
            handleIframe(iframes[i]); // 处理每个现有的 iframe
        }
    }*/

    /**
     * 方法二
     * 将水印应用到整个页面上
     */
    function addWatermark(text) {

        var watermark;
        var ifm = parent.document.getElementsByTagName("iframe")[0];
        if (ifm) {
            var ifmBody = ifm.contentWindow.document.body;
            if (ifmBody) {
                // 获取顶层元素
                watermark = window.top.document.getElementsByClassName('watermark')[0];
                if (!watermark) {
                    window.top.document.body.appendChild(generateWatermark(text)); // 如果顶层没有水印，添加水印
                }
            } else {
                watermark = ifmBody.getElementsByClassName("watermark")[0];
                if (!watermark) {
                    ifmBody.appendChild(generateWatermark(text)); // 如果iframe的body中没有水印，添加水印
                }
            }
        } else {
            watermark = document.body.getElementsByClassName("watermark")[0];
            if (!watermark) {
                document.body.appendChild(generateWatermark(text)); // 如果当前文档的body中没有水印，添加水印
            }
        }
    }

    function generateWatermark(text) {
        // 创建一个div元素作为水印容器
        const watermarkDiv = document.createElement('div');
        watermarkDiv.className = 'watermark';
        for (var i = 1; i <= 500; i++) {
            const textSpan = document.createElement('span');
            textSpan.className = 'watermark-span';
            textSpan.textContent = getNowDate() + " " + text;
            $(watermarkDiv).append(textSpan);
        }
        watermarkDiv.style.zIndex = '999999999'; // 确保水印在最上层
        return watermarkDiv;
    }

    /**
     * 获取当前日期
     * @returns {string} 当前日期，格式为 YYYY.MM.DD
     */
    function getNowDate() {
        var time = new Date();
        let year = time.getFullYear() + '';
        let month = time.getMonth() + 1 + '';
        let date = time.getDate() + '';
        return year + "." +  (month < 10 ? '0' + month : month) + "." + (date < 10 ? '0' + date : date);
    }

</script>
