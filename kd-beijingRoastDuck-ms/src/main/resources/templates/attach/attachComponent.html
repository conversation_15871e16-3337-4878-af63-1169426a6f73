<!--[if IE 8]>
<html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]>
<html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
<!--<![endif]-->
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>物损详情</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <script src="${ctx}/carousel/dataCollection/viewer.js"></script>
    <script src="${ctx}/carousel/dataCollection/viewermain.js"></script>
    <link href="${ctx}/carousel/dataCollection/viewer.css" rel="stylesheet">
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/metronic/global/scripts/app.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/layout.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/demo.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/jquery-multi-select/js/jquery.multi-select.js"
            type="text/javascript"></script>

    <script src="${ctx}/js/idCardCheck.js" type="text/javascript"></script>
    <style>

        .clear-margin {
            margin: 0px;
        }

        .clear-padding {
            padding: 0px;
        }

        .clear-padding-left {
            padding-left: 0px;
        }

        .clear-padding-right {
            padding-right: 0px;
        }

        .chart-search {
            margin-bottom: 10px;
            display: none;
        }


        .search-channel-content {
            height: 100px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .search-ul-list {
            list-style-type: none;
            height: 20px;
            padding-left: 0px;
            color: black;
        }

        .channel {
            float: left;
            display: block;
        }

        .typeClick {
            height: 112px;
            margin-bottom: 10px;
            border: 2px solid black;
            border-radius: 5px !important;
            margin-left: 20px;
            cursor: pointer
        }

        .typeClickClick {
            height: 112px;
            margin-bottom: 10px;
            border: 2px solid #1676ff;
            background-color: #1676ff;
            color: white;
            border-radius: 5px !important;
            margin-left: 20px;
            cursor: pointer
        }

        .has-error {
            border-color: #ce0d0d !important;
            color: red !important;
        }

        .bckColor {
            background-color: rgba(51, 153, 241, 0.3);
        }

        .imageListChoose {
            /*background-color: #bad6ff !important;*/
            border: 5px solid #055ad7;
        }

        .collectionCompleted {
            background-color: #E8F2FF;
        }

        .collectionButton {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: transparent;
            border-radius: 6px !important;
            color: #1ABB00;
        }

        .collectionButtonClick {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00 !important;
            border-radius: 6px !important;
            color: #FFFFFF;
        }

        .collectionData:hover {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00;
            color: #FFFFFF;
        }

        .middleTable {
            border: 4px solid #1767ff !important;
        }

        .hangup {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #ce0d0d !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }

        .error {
            color: #ce0d0d !important;
        }

        .txt-over-hide {
            color: red;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: block;
            width: 650px;
        }

        .jump {
            /*float: right;*/
            border-radius: 5px !important;
            color: #3399ff !important;
            background-color: #fff !important;
            border-color: #3399ff;
            height: 40px;
            width: 100%;
        }

        .genTask {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #3399ff !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }

        .thClass {
            text-align: center;
            color: #fff;
        }

        .typeSelect {
            border-color: #3399ff;
            background-color: #3399ff;
        }

        .thumbnail-img {
            width: 50px;
            height: 45px;
            border: 4px solid #D6D6C1;
        }

        .selected-thumbnail-img {
            border: 4px solid #3399ff !important;
        }

        .collection-thumbnail-img {
            border: 4px solid #00B176;
        }

        /*里面的代码可以根据自己需求去进行更改*/
        /* 设置滚动条的样式 */
        ::-webkit-scrollbar {
            width: 5px;
        }

        /* 滚动槽 */
        ::-webkit-scrollbar-track {
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
            border-radius: 10px;
        }

        /* 滚动条滑块 */
        ::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
        }

        ::-webkit-scrollbar-thumb:window-inactive {
            background: rgba(255, 0, 0, 0.4);
        }

        .ticket-information {
            height: 1050px;
            overflow-y: scroll;
        }

        .dataDisplayArea {
            width: 100%;
            height: 19%;
            border-radius: 10px !important;
            /*border: 1px solid #aca8a8;*/
            border: 1px solid #797979;
            cursor: pointer;
            margin-top: 10px;
            overflow: hidden;
        }

        .selected-dataDisplayArea {
            background-color: #91cae9;
        }

        .submit-dataDisplayArea {
            border: 3px solid #44db69 !important;
        }

        .change-dataDisplayArea {
            border: 3px solid red !important;
        }

        .dataDisplayArea > div {
            height: 20%;
            padding: 10px 0px 0px 0px !important;
        }

        .dataDisplayArea-head {
            background-color: #3399ff;
            /*border-radius: 10px 10px 0px 0px !important;*/
        }

        .dataDisplayArea-head-left {
            color: #ffffff;
            font-size: 15px;
            padding: 0px 0px 0px 5px;
        }

        .dataDisplayArea-head-right {
            text-align: right;
            padding: 0px !important;
        }

        .dataDisplayArea-body-left {
            opacity: 0.7;
        }

        .dataDisplayArea-body-right {
            text-align: right;
        }

        .row {
            margin: 0px 0px;
        }

        .subject-name {
            font-size: 20px;
        }

        .estimateInventoryDataBlue {
            background-color: #1676ff;
        }

        .line-center {
            display: inline-block;
            background-color: blue;
            border-radius: 50%;
            height: 30px;
            width: 30px;
            color: white;
            text-align: center;
            line-height: 30px;
            font-size: 18px;
            font-weight: bold;
        }

        .icon-plus {
            font-size: 30px;
            line-height: 23px;
        }

        .icon-plus:hover {
            cursor: pointer;
        }

        .dataDisplayArea-head-img {
            float: right;
            width: 30px;
            height: 25px;
            margin: 10px 20px 10px 0px;
        }

        .dataDisplayArea-head-img:hover {
            cursor: pointer;
        }

        .form-control {
            height: 28px !important;
        }

        .label-right {
            font-size: 13px;
            text-align: right;
        }

        .align-item-center {
            display: flex;
            align-items: center;

        }

        .col-sm-1 {
            text-align: center;
        }

        textarea[class='layui-layer-input'] {
            outline: 0 !important;
            width: 700px !important;
            height: 350px !important;
            padding-left: 10px !important;
            border: 1px solid #e6e6e6 !important;
            color: #333 !important;
            box-shadow: none !important;
        }

        button[name='actionBtn'] {
            color: white;
        }

        select {
            padding: 0px 12px !important;
        }

        div[name='dataCollectAreaRow'] input {
            padding: 0px 5px;
        }


        #firstRegistrationTime[readonly]{
            background-color: white;
        }

        #lossAssessmentTime[readonly]{
            background-color: white;
        }

        #thumbnail p {
            margin: 10px 0px 5px -15px;
            font-size: 10px;
            text-align: left;
        }

        .viewer-canvas h2 {
            z-index: 9999999;
            position: relative;
            left: 20px;
            font-weight: bold;
        }

        /* 侧边栏样式 */
        .sidebar-nav {
            width: 100%;
            padding: 10px 0;
            background-color: #f5f5f5;
            border-radius: 4px;
        }

        .nav-list {
            padding-left: 0;
            margin-bottom: 0;
            list-style: none;
        }

        .nav-item {
            border-bottom: 1px solid #e0e0e0;
        }

        .nav-link {
            display: block;
            padding: 8px 15px;
            color: #333;
            text-decoration: none;
        }

        .nav-link:hover {
            background-color: #e9e9e9;
        }

        .nav-link i {
            margin-right: 8px;
            transition: transform 0.3s ease;
        }

        .nav.collapse {
            display: none;
            padding-left: 20px;
        }

        .nav.collapse.in {
            display: block;
        }

        /* 展开状态的箭头旋转 */
        .nav-link[aria-expanded="true"] i.glyphicon-chevron-right {
            transform: rotate(90deg);
        }

        /* 数量标签样式 */
        .nav-link span.badge {
            margin-left: 5px;
            background-color: #777;

        }
        .nav-link{
            cursor: pointer;
        }
    </style>

    <script type="text/javascript">

        var viewer = null;

        var rowNum = 0;

        let claimAttachMap = null;

        let imgInfoFatherMaps = null;


        $(function () {
            initData();

            /*点击缩略图*/
            $('body').on('click', '.thumbnail-img', function () {
                if ($(this).hasClass('selected-thumbnail-img')) {
                    return;
                }
                let attrId = $(this).data('fileid');
                $('.selected-thumbnail-img').removeClass('selected-thumbnail-img');
                $(this).addClass('selected-thumbnail-img');
                viewer.showImageByFileId(attrId);
                let imgNumber = $(this).data('img-number');
                let offset = imgNumber * 95 - (1050 - 95) / 2;
                $('#thumbnail').scrollTop(offset);
            });


            /*A/D切换影像*/
            $('body').on('click', '.viewer-list > li', function () {
                let attchId = $(this).find('img').attr('alt');
                $('#thumbnail-' + attchId).trigger('click');
            });





            // 为所有折叠菜单添加点击事件
            $('body').on('click','.nav-link[data-toggle="collapse"]', function(e) {
                e.preventDefault();
                e.stopPropagation(); // 防止事件冒泡到父元素
                collapseDetal($(this))
            });
            function collapseDetal(obj){
                var target = obj.data('target');
                $(target).toggleClass('in');

                //是否需要折叠当前菜单
                let isNeedZD = obj.attr("aria-expanded")=="true"

                $('#attachSidebarNav').find('i[class="glyphicon glyphicon-chevron-right"]').attr('style','')
                $('#attachSidebarNav').find("a[aria-expanded='true']").attr('aria-expanded','false');
                $('#attachSidebarNav').find('ul[class*="show"]').attr('class','nav collapse')
                if(!isNeedZD){
                    // 检查是否使用的是Bootstrap 4+，如果是则使用'show'类
                    if ($(target).hasClass('show')) {
                        $(target).removeClass('show');
                    } else {
                        $(target).addClass('show');
                    }


                    var isExpanded = $(target).hasClass('in') || $(target).hasClass('show');
                    obj.attr('aria-expanded', isExpanded);
                    obj.find('i.glyphicon-chevron-right').css('transform', isExpanded ? 'rotate(90deg)' : '');
                }

            }

            // 添加缩略图叉号点击事件
            $('body').on('click', '.thumbnail-close', function(e) {
                e.preventDefault();
                e.stopPropagation();
                let fileId = $(this).data('fileid');
                let attachTypeCode = $(this).attr('attachTypeCode');
                // 这里可以添加删除图片的逻辑
                console.log('Delete image with fileId: ' + attachTypeCode);
                /*$("#imgArea"+fileId).remove();
                $(".viewer-canvas").hide();*/
                // 例如：$(this).closest('li').remove();
                $.ajax({
                    url: "${ctx}/claimCaseController/logicDeteleAttach?attachId="+fileId,
                    type: 'POST',
                    async: false,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {     // 关闭加载层
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            layer.msg("删除成功", {
                                icon: 1,
                                time: 2000,
                                offset: scrollTop
                            }, function () {
                                let claimAttachMapElement = claimAttachMap[attachTypeCode];
                                let newClaimAttach = []
                                for(let elementData of claimAttachMapElement){
                                    if(elementData.id == fileId){
                                        claimAttachMap["删除"].push(elementData)
                                    }else {
                                        newClaimAttach.push(elementData)
                                    }
                                }
                                claimAttachMap[attachTypeCode] = newClaimAttach
                                $("#uploadImg").remove()
                                showSidebarNav()
                                collapseDetal($('.nav-item').find('[attachcode="'+attachTypeCode+'"]').parent().parent().parent().find('.nav-link[data-toggle="collapse"]'))
                                showAttach(attachTypeCode)
                            });
                        }else{
                            layer.msg(result.msg, {
                                icon: 2,
                                time: 2000,
                                offset: scrollTop
                            });
                        }
                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                        layer.closeAll();
                    }
                });
            });

            // 菜单项点击事件
            $('body').on('click','.nav-link[data-toggle="treeNode"]', function(e) {
                e.preventDefault();
                // 这里可以添加点击菜单项后的处理逻辑
                var code = $(this).attr("attachCode");
                console.log('Selected category: ' + code);
                // 可以添加加载对应分类图片的逻辑
                showAttach(code)
            });
        });

        //展示图片方法
        function showAttach(key){
            let isShowDeleteButton =  `${isShowDeleteButton}`;
            //不显示删除按钮逻辑
            if(key == "删除"){
                isShowDeleteButton = "0"
            }

            $("#showImageDiv").html('');
            $("#thumbnail").html('');
            let attachIndex = 0
            let ulDiv = $('<ul id="images"></ul>')
            let thumbnailOlDiv = $('<ol id = "thumbnailOl" style="padding-left:30px !important;"></ol>')
            for(let attach of claimAttachMap[key]){
                console.log(attach);
                {
                    thumbnailOlDiv.append(`<div id="imgArea`+attach.id+`">
                                                    <p title="`+attach.contentType+`" style="text-align: center;">`+attach.contentType+`</p>
                                                    <li style="width:auto; height:auto; cursor:pointer;text-align: center;padding-top:5px;list-style-type:none">
                                                        <div style="position: relative; display: inline-block;">
                                                            <img id="thumbnail-`+attach.id+`"  class="thumbnail-img `+(attachIndex==0?'selected-thumbnail-img':'')+`"
                                                                 data-fileid="`+attach.id+`" data-img-number=''
                                                                 title="`+attach.fileName+`" data-img-name="`+attach.attachName+`"
                                                                 src="`+attach.fileObjectId+`" onerror="javascript:this.src='/a/job_done.png'"/>
                                                            `+(isShowDeleteButton == "1"?'<span class="thumbnail-close" style="position: absolute; top: -5px; right: -5px; background-color: red; color: white; width: 16px; height: 16px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; cursor: pointer;" data-fileid="'+attach.id+'"  attachTypeCode="'+key+'">×</span>':'')+`
                                                        </div>
                                                    </li>
                                                </div>`);
                    let liDiv = $('<li hidden="hidden"></li>')

                    let imgDiv = $('<img data-fileid = "'+attach.id+'" title ="'+attach.fileName+'" src="'+attach.fileObjectId+'" alt="'+attach.id+'">')

                    liDiv.append(imgDiv);
                    ulDiv.append(liDiv);
                    attachIndex = attachIndex+1;
                }
            }
            $("#showImageDiv").append(ulDiv);
            $("#thumbnail").append(thumbnailOlDiv);

            if(claimAttachMap[key] && claimAttachMap[key].length>0){
                /*图片展示工具*/
                (function () {
                    viewer = new Viewer({
                        activeId: null
                    });
                })();
            }

        }


        //展示侧边栏
        function showSidebarNav(){
            $('#attachSidebarNav').html('');
            let navListDiv = $('<ul class="nav nav-list"></ul>')
            for(let fatherCode in imgInfoFatherMaps){
                let isShowDeleteDir =  `${isShowDeleteDir}`;
                //不显示删除文件夹逻辑
                if(isShowDeleteDir && isShowDeleteDir != "1" && fatherCode == "删除"){
                    continue;
                }
                {
                    let sumNum = 0;
                    let navUlDiv = $('<ul id="'+fatherCode+'" class="nav collapse"></ul>')
                    for(let code of imgInfoFatherMaps[fatherCode]){
                        let num = 0;
                        if(claimAttachMap[code]){
                            num = claimAttachMap[code].length
                        }
                        let navLiDiv = $('<li><a href="#" class="nav-link" data-toggle="treeNode" attachCode = "'+code+'">'+code.split("-")[0] +'('+num+')</a></li>');
                        navUlDiv.append(navLiDiv);
                        sumNum = sumNum + num
                    }

                    let navItemDiv = $('<li class="nav-item"></li>')
                    navItemDiv.append('<a href="#" class="nav-link" data-toggle="collapse" data-target="#'+fatherCode+'">' +
                        '<i class="glyphicon glyphicon-chevron-right"></i> '+fatherCode+' ('+sumNum+')</a>')
                    navItemDiv.append(navUlDiv)
                    navListDiv.append(navItemDiv)
                }
            }
            $('#attachSidebarNav').append(navListDiv);
            if(`${isShowUploadBotton}`== "1"){
                $('#attachSidebarNav').after(`<div id="uploadImg" style="position: relative;bottom: 48px;">
                        <button id="uploadBtn" class="btn genTask"  onclick="uploadImg('${caseId}')">
                            <i class="glyphicon glyphicon-upload"></i> 上传图片
                        </button>
                    </div>`)
            }
        }

        //初始化数据
        function initData(){
            let caseId = $("#caseId").val();

            $.ajax({
                url: "${ctx}/claimCaseController/getAttachData?caseId="+caseId,
                type: 'POST',
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {     // 关闭加载层
                    console.log(data)

                    claimAttachMap = data.claimAttachMap
                    imgInfoFatherMaps  = data.imgInfoFatherMaps;
                    showSidebarNav();
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                    layer.closeAll();
                }
            });
        }

        var scrollTop;

        //上传影像
        function uploadImg(claimCaseId) {
            scrollTop = calculationScrollTop();
            var openWindowWidth = $(document).width() * 0.8 + "px";
            layer.open({
                type: 2,
                title: '影像编辑',
                area: [openWindowWidth, '800px'],
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/claimCaseController/editAttach?claimCaseId=" + claimCaseId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        // 计算滚动高度
        function calculationScrollTop() {
            var ifm = parent.document.getElementById("contentFrame");
            scrollTop = $(window.parent).scrollTop() + 100;
            if (!ifm) {
                scrollTop = 100;
            }
            console.log("打开的高度"+scrollTop);
            return scrollTop + "px";
        }


    </script>
</head>


<body id="qc-Body" style="overflow-x:hidden;background: #fff;">
<input id="caseId" type="hidden" value="${caseId}">
<div class="container-fluid">
    <!-- 左部分 -->
    <div class="col-sm-12" style="background: none!important;padding-right: 0px !important;">
        <#--侧边栏，菜单缩进-->
        <div class="row col-sm-2" id="tree">
            <div id="attachSidebarNav" style="height: 1050px;overflow-x: auto; " class="sidebar-nav">
                <#--<ul class="nav nav-list">
                    <!-- 公共类资料 &ndash;&gt;
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-toggle="collapse" data-target="#publicDocs">
                            <i class="glyphicon glyphicon-chevron-right"></i> 公共类资料 (6)
                        </a>
                        <ul id="publicDocs" class="nav collapse">
                            <li><a href="#" class="nav-link">驾驶证正、副本 (2)</a></li>
                            <li><a href="#" class="nav-link">行驶证正、副本 (2)</a></li>
                            <li><a href="#" class="nav-link">交通事故责任认定书 (2)</a></li>
                        </ul>
                    </li>


                </ul>-->



            </div>
        </div>
        <#--审核头-->
        <div class="row  col-sm-10">
            <#-- 展示图片区域以及采集数据tbale -->
            <div class="col-sm-12" style="padding: 5px 0px 0px 0px; background: none;">
                <div class="row" style="height: 1050px; overflow: hidden;">
                    <div class="col-sm-1" style="height: 1050px;background-color: rgba(226, 226, 226, 0.5);padding-left: 0px!important;padding-right: 10px!important; border-right: groove;">
                        <#assign attachIndex = 0>
                        <!--缩略图-->
                        <div id="thumbnail" style="height: 1050px;overflow-y: scroll;overflow-x:hidden">
                            <#--<#if claimAttachMap?exists>&ndash;&gt;
                                <ol id = "thumbnailOl" style="padding-left:30px !important;">
                                    <#list imgInfoMaps.keySet() as key>

                                        <#if claimAttachMap?keys?seq_contains(key)>

                                            <#list claimAttachMap.get(key) as attach>
                                                <div id="imgArea${attach.id}">
                                                    <p title="${key}" style="text-align: center;">${key}</p>
                                                    <li style="width:auto; height:auto; cursor:pointer;text-align: center;padding-top:5px;list-style-type:none">
                                                        <div style="position: relative; display: inline-block;">
                                                            <img id="thumbnail-${attach.id}"  class="thumbnail-img <#if attachIndex == 0>selected-thumbnail-img</#if>"
                                                                 data-fileid="${attach.id}" data-img-number=''
                                                                 title="${attach.fileName}" data-img-name="${attach.attachName}"
                                                                 src="${attach.fileObjectId}" onerror="javascript:this.src='/a/job_done.png'"/>
                                                            <span class="thumbnail-close" style="position: absolute; top: -5px; right: -5px; background-color: red; color: white; width: 16px; height: 16px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; cursor: pointer;" data-fileid="${attach.id}">×</span>
                                                        </div>
                                                    </li>
                                                </div>

                                                <#assign attachIndex = attachIndex + 1>
                                            </#list>
                                        </#if>
                                    </#list>
                                </ol>
                             </#if>-->
                        </div>
                    </div>
                    <!--展示图-->
                    <div id="showImageDiv" class="col-sm-11" style="height: 1300px;">
                        <#--<ul id="images">
                            <#if claimAttachMap?exists>
                                <#list claimAttachMap.keySet() as key>
                                    <#list claimAttachMap.get(key) as attach>
                                        <li hidden="hidden">
                                            <img data-fileid="${attach.id}"
                                                 title="${attach.fileName}"
                                                 src="${attach.fileObjectId}"
                                                 alt="${attach.id}"/>
                                        </li>
                                    </#list>
                                </#list>
                            </#if>
                        </ul>-->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>