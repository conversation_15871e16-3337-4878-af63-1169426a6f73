<!DOCTYPE html>
<!--
Template Name: Metronic - Responsive Admin Dashboard Template build with Twitter Bootstrap 3.3.5
Version: 4.5.2
Author: KeenThemes
Website: http://www.keenthemes.com/
Contact: <EMAIL>
Follow: www.twitter.com/keenthemes
Like: www.facebook.com/keenthemes
Purchase: http://themeforest.net/item/metronic-responsive-admin-dashboard-template/4021469?ref=keenthemes
License: You must have a valid license purchased only from themeforest(the above link) in order to legally use the theme for your project.
-->
<!--[if IE 8]>
<html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]>
<html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
<!--<![endif]-->
<!-- BEGIN HEAD -->

<head>
    <meta charset="utf-8"/>
    <title>新职业平台</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <#include "/common/cssResource.html">
    <!-- BEGIN PAGE LEVEL STYLES -->
    <link href="${ctx}/metronic/pages/css/login.min.css" rel="stylesheet" type="text/css"/>
    <!-- END PAGE LEVEL STYLES -->

    <#include "/common/jsResource.html">
    <script src="${ctx}/js/jquery.slideunlock.js"></script>
    <#--xzp 滑动模板css-->
    <link href="${ctx}/static/css/imageValidateStyle.css" rel="stylesheet" type="text/css"/>


    <!--[if lt IE 10]>
    <meta http-equiv="Refresh" content="0; url=${ctx}/browserNotMatch"/>
    <![endif]-->

    <style type="text/css">
        .error-color {
            font-weight: bold;
            color: #f50000
        }

        #slider {
            margin: 0 0;
            width: 100%;
            height: 43px;
            position: relative;
            border-radius: 2px;
            background-color: #d3e3f4;
            overflow: hidden;
            text-align: center;
            user-select: none;
            -moz-user-select: none;
            -webkit-user-select: none;
        }

        #slider_bg {
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            background-color: #fdd711;
            z-index: 1;
        }

        #label {
            width: 43px;
            position: absolute;
            font-family: "宋体";
            left: 0;
            top: 0;
            height: 43px;
            /*line-height:43px;*/
            /*border: 1px solid #cccccc;*/
            /*background: #fff;*/
            background-image: url("${ctx}/images/rocket.png");
            z-index: 3;
            cursor: move;
            color: #ff9e77;
            font-size: 16px;
            font-weight: 900;
        }

        #labelTip {
            position: absolute;
            left: 0;
            width: 100%;
            height: 100%;
            font-size: 13px;
            color: #787878;
            line-height: 43px;
            text-align: center;
            z-index: 2;
        }

        video {
            position: fixed;
            right: 0px;
            bottom: 0px;
            min-width: 100%;
            min-height: 100%;
            height: auto;
            width: auto;
            /*加滤镜*/
            /*filter: blur(15px); //背景模糊设置 */
            /*-webkit-filter: grayscale(100%);*/
            /*filter:grayscale(100%); //背景灰度设置*/
            z-index: -11
        }

        source {
            min-width: 100%;
            min-height: 100%;
            height: auto;
            width: auto;
        }
    </style>
    <script type="text/javascript">
        //滑动验证是否成功
        var sliderStatus = 0;
        //十秒过期
        var second = 10;
        //首次验证图片
        var imgSlideBlock = true;
        //首次鼠标点击
        var mousemoveFirst = true;
        var touchmoveFirst = true;
        //首次进入页面
        var indxFirst = true;
        //关闭图片验证
        var closeImg = false;
        $(document).ready(
            function () {
                var form = $('#loginForm');
                form.validate({
                    errorElement: 'span', //default input error message container
                    errorClass: 'error-color', // default input error message class
                    focusInvalid: false, // do not focus the last invalid input
                    rules: {
                        userName: {
                            required: true
                        },
                        password: {
                            required: true
                        }
                    },
                    messages: {
                        userName: {
                            required: "<font color='#f50000' style='font-weight:bold;'>请填入用户名</font>"
                        },
                        password: {
                            required: "<font color='#f50000' style='font-weight:bold;'>请填入密码</font>"
                        }
                    },

                    invalidHandler: function (event, validator) { //display error alert on form submit
                        App.scrollTo(form, 0);
                    },

                    highlight: function (element) { // hightlight error inputs
                        $(element).closest('.form-group').removeClass(
                            "has-success").addClass('has-error'); // set error class to the control group
                    },

                    unhighlight: function (element) { // revert the change done by hightlight

                    },

                    success: function (label, element) {
                        var icon = $(element).parent('.input-icon').children(
                            'i');
                        $(element).closest('.form-group').removeClass(
                            'has-error').addClass('has-success'); // set success class to the control group
                        icon.removeClass("fa-warning").addClass("fa-checkDifference18");
                    },

                    submitHandler: function (form) {
                        $(".slideBarContent").show();

                    }
                });

                $('.login-form input').keypress(function (e) {
                    if (e.which == 13) {
                        if ($('.login-form').validate().form()) {
                            $('.login-form').submit(); //form validation success, call ajax form submit
                        }
                        return false;
                    }
                });
            });
        /*图片验证js*/
        (function (window, document) {
            var SliderBar = function (targetDom, options) {
                // 判断是用函数创建的还是用new创建的。这样我们就可以通过MaskShare("dom") 或 new MaskShare("dom")来使用这个插件了
                if (!(this instanceof SliderBar)) return new SliderBar(targetDom, options);
                // 参数
                this.options = this.extend({
                    dataList: []
                }, options);
                // 获取dom
                this.targetDom = document.getElementById(targetDom);
                var dataList = this.options.dataList;

                if (dataList.length > 0) {
                    var html = "<div class='slide-box'><div class='slide-img-block'>" +
                        "<div class='slide-loading'></div><div class='slide-img-border'>" +
                        "<div class='slide-top'>拖动下方滑块完成拼图</div><!--<div class='slide-img-div'>-->" +
                        "<div class='slide-img-nopadding'><img class='slide-img' id='slideImg' src='' />" +
                        "<div class='slide-block' id='slideBlock'></div><div class='slide-box-shadow' id='cutBlock'></div></div>" +
                        "<div class='scroll-background  slide-img-hint-info' id='slideHintInfo'>" +
                        "<div class='slide-img-hint'><div class='scroll-background slide-icon' id='slideIcon'></div>" +
                        "<div class='slide-text'><span class='slide-text-type' id='slideType'></span>" +
                        "<span class='slide-text-content' id='slideContent'></span></div></div></div></div>" +
                        // "<div class='scroll-background slide-bottom'>" +

                        // "<div class='slide-bottom-no-logo'></div></div></div></div>" +
                        "<div class='huadongkuang scroll-bar'>" +
                        "<div class='huadong huadong-slide-btn' id='slideBtn'></div>" +
                        "<div class='slide-title' id='slideHint'><!-- <-按住滑块，拖动完成上面拼图--></div></div></div>" +
                        "<div style='text-align: right;width: 100%'>" +
                        "<div class='shuaxin slide-bottom-refresh' id='refreshBtn' title='更换图片'></div>" +
                        "<div class='guanbi slide-bottom-refresh' id='closeBtn' title='关闭'></div>" +
                        "</div>";

                    this.targetDom.innerHTML = html;
                    this.slideBtn = document.getElementById("slideBtn");                 // 拖拽按钮
                    this.refreshBtn = document.getElementById("refreshBtn");             // 换图按钮
                    this.closeBtn = document.getElementById("closeBtn");                 // 关闭按钮
                    this.slideHint = document.getElementById("slideHint");               // 提示名称
                    this.slideImg = document.getElementById("slideImg");                 // 图片
                    this.cutBlock = document.getElementById("cutBlock");                 // 裁剪区域
                    this.slideBlock = document.getElementById("slideBlock");             // 裁剪的图片
                    this.slideIcon = document.getElementById("slideIcon");               // 正确、失败的图标
                    this.slideType = document.getElementById("slideType");               // 正确、失败
                    this.slideContent = document.getElementById("slideContent");         // 正确、失败的正文
                    this.slideHintInfo = document.getElementById("slideHintInfo");       // 弹出
                    this.resultX = 0;
                    this.startX = 0;

                    this.timer = 0;
                    this.startTamp = 0;
                    this.endTamp = 0;
                    this.x = 0;
                    this.imgWidth = 0;
                    this.imgHeight = 0;
                    this.imgList = [];
                    this.isSuccess = true;
                    for (var i = 1; i < 10; i++) {
                        this.imgList.push("/" + i + ".png");
                    }
                }
                this.init();
            }
            SliderBar.prototype = {
                init: function () {
                    this.event();
                },
                extend: function (obj, obj2) {
                    for (var k in obj2) {
                        obj[k] = obj2[k];
                    }
                    return obj;
                },
                event: function () {
                    var _this = this;
                    _this.reToNewImg();
                    _this.slideBtn.onmousedown = function (event) {
                        _this.mousedown(_this, event);
                    }
                    _this.slideBtn.ontouchstart = function (event) {
                        _this.touchstart(_this, event);
                    }
                    _this.refreshBtn.onclick = function () {
                        _this.refreshBtnClick(_this);
                    }
                    _this.closeBtn.onclick = function () {
                        _this.closeBtnClick(_this);
                    }
                },
                refreshBtnClick: function (_this) {
                    second = 10;
                    _this.timer = 0;
                    _this.startTamp = 0;
                    imgSlideBlock = true;
                    mousemoveFirst = true;
                    touchmoveFirst = true;
                    _this.isSuccess = true;
                    _this.slideBlock.style.cssText = "";
                    _this.cutBlock.style.cssText = "";
                    _this.reToNewImg();
                },

                closeBtnClick: function (_this) {
                    second = 10;
                    _this.timer = 0;
                    _this.startTamp = 0;
                    imgSlideBlock = true;
                    mousemoveFirst = true;
                    touchmoveFirst = true;
                    closeImg = true;
                    _this.isSuccess = true;
                    _this.slideBlock.style.cssText = "";
                    _this.cutBlock.style.cssText = "";
                    _this.reToNewImg();
                },

                reToNewImg: function () {
                    var _this = this;
                    var index = Math.round(Math.random() * 8);         // 该方法有等于0 的情况
                    var imgSrc = "${ctx}/images/templates" + _this.imgList[index] + "";
                    _this.slideImg.setAttribute("src", imgSrc);
                    _this.slideBlock.style.backgroundImage = "url(" + imgSrc + ")";
                    _this.slideImg.onload = function (e) {
                        e.stopPropagation();
                        _this.imgWidth = _this.slideImg.offsetWidth;                   // 图片宽
                        _this.imgHeight = _this.slideImg.offsetHeight;                 // 图片高
                        if (imgSlideBlock) {
                            _this.mousedown(_this, event);
                        }
                    }
                },
                cutImg: function () {
                    var _this = this;
                    _this.imgWidth = _this.slideImg.offsetWidth;                   // 图片宽
                    _this.imgHeight = _this.slideImg.offsetHeight;                 // 图片高
                    _this.cutBlock.style.display = "block";
                    var cutWidth = _this.cutBlock.offsetWidth;                // 裁剪区域宽
                    var cutHeight = _this.cutBlock.offsetHeight;              // 裁剪区域高
                    // left
                    _this.resultX = Math.floor(Math.random() * (_this.imgWidth - cutWidth * 2 - 10) + cutWidth);
                    // top
                    var cutTop = Math.floor(Math.random() * (_this.imgHeight - cutHeight * 2) + cutHeight);
                    // 设置样式
                    _this.cutBlock.style.cssText = "top:" + cutTop + "px;" + "left:" + _this.resultX + "px; display: block;";
                    _this.slideBlock.style.top = cutTop + "px";
                    _this.slideBlock.style.backgroundPosition = "-" + _this.resultX + "px -" + cutTop + "px";
                    _this.slideBlock.style.opacity = "1";
                    if (closeImg) {
                        closeImg = false;
                        $(".slideBarContent").hide();
                        return;
                    }
                },

                //PC端
                mousedown: function (_this, e) {
                    e.preventDefault();

                    _this.startX = e.clientX;

                    if (_this.startTamp == 0) {
                        _this.startTamp = Math.round(new Date().getTime())
                    }
                    var target = e.target;
                    target.style.backgroundPosition = "-3 500px";
                    _this.slideHint.style.opacity = "0";
                    if ((_this.isSuccess && imgSlideBlock) || closeImg) {
                        _this.cutImg();
                    }
                    document.addEventListener('mousemove', mousemove);
                    document.addEventListener('mouseup', mouseup);

                    if (imgSlideBlock) {
                        mouseup();
                    }

                    // 拖拽
                    function mousemove(event) {

                        if (mousemoveFirst) {
                            _this.startTamp = Math.round(new Date().getTime())
                        }
                        _this.x = event.clientX - _this.startX;

                        if (_this.x < 0) {
                            _this.slideBtn.style.left = "0px";
                            _this.slideBlock.style.left = "2px";
                        } else if (_this.x >= 0 && _this.x <= 250) {
                            _this.slideBtn.style.left = _this.x + "px";
                            _this.slideBlock.style.left = _this.x + "px";
                        } else if (_this.x >= 250) {
                            _this.slideBtn.style.left = "250px";
                            _this.slideBlock.style.left = "250px";
                        } else {
                            _this.slideBtn.style.left = "0px";
                            _this.slideBlock.style.left = "0px";
                        }

                        _this.slideBtn.style.transition = "none";
                        _this.slideBlock.style.transition = "none";
                        mousemoveFirst = false;

                    };

                    // 鼠标放开
                    function mouseup() {
                        document.removeEventListener('mousemove', mousemove);
                        document.removeEventListener('mouseup', mouseup);

                        var left = _this.slideBlock.style.left;
                        left = parseInt(left.substring(0, left.length - 2));
                        _this.endTamp = Math.round(new Date().getTime())
                        _this.timer = ((parseInt(_this.endTamp) - parseInt(_this.startTamp)) / 1000).toFixed(2);
                        // console.log("_this.timer:" + _this.timer);
                        if (_this.timer > 10) {
                            _this.isSuccess = false;
                            // 设置样式
                            // 裁剪图片(拼图的一块)
                            _this.slideBlock.style.left = "2px";
                            // 完成拼图
                            _this.slideBlock.style.transition = "left 0.6s";
                            // 错误弹出的图标

                            _this.slideIcon.style.backgroundPosition = "0 -1229px";
                            _this.slideType.className = "slide-text-type redColor";
                            _this.slideType.innerHTML = "验证失败:";
                            _this.slideContent.innerHTML = "滑块拼图已超时";
                            sliderStatus = 0;
                            _this.options.fail && _this.options.fail();
                        } else if (_this.resultX > (left - 15) && _this.resultX < (left + 15)) {
                            _this.isSuccess = true;
                            // 裁剪图片(拼图的一块)
                            _this.slideBlock.style.opacity = "0";
                            _this.slideBlock.style.transition = "opacity 0.6s";
                            // 裁剪的区域(黑黑的那一块)
                            _this.cutBlock.style.opacity = "0";
                            _this.cutBlock.style.transition = "opacity 0.6s";
                            // 正确弹出的图标

                            _this.slideIcon.style.backgroundPosition = "0 -1207px";
                            _this.slideType.className = "slide-text-type greenColor";
                            _this.slideType.innerHTML = "验证通过:";
                            _this.slideContent.innerHTML = "用时" + _this.timer + "s";

                            _this.options.success && _this.options.success();
                            var formData = new FormData($('#loginForm')[0]);
                            $.ajax({
                                url: "${ctx}/oauthController/login",
                                type: 'POST',
                                data: formData,
                                async: true,
                                cache: false,
                                contentType: false,
                                processData: false,
                                success: function (data) {
                                    var result = eval("(" + data + ")");
                                    if (result.ret == "-1") {
                                        setTimeout(function () {
                                            _this.cutBlock.style.display = "none";
                                            _this.slideBlock.style.left = "2px";
                                            _this.reToNewImg();
                                            _this.closeBtnClick(_this);
                                            $("#msg").html("<font color='#f50000' style='font-weight:bold;'>" + result.msg + "</font>");
                                        }, 1000);

                                    } else {
                                        window.location.href = "${request.getContextPath()}/mainFrame";
                                    }
                                },
                                error: function (data) {
                                    var result = eval("(" + data + ")");
                                }
                            });
                        } else if (!imgSlideBlock) {
                            _this.isSuccess = false;
                            // 设置样式
                            // 裁剪图片(拼图的一块)
                            _this.slideBlock.style.left = "2px";
                            _this.slideBlock.style.transition = "left 0.6s";
                            // 错误弹出的图标
                            _this.slideIcon.style.backgroundPosition = "0 -1229px";
                            _this.slideType.className = "slide-text-type redColor";
                            _this.slideType.innerHTML = "验证失败:";
                            _this.slideContent.innerHTML = "拖动滑块将悬浮图像正确拼合";
                            sliderStatus = 0;
                            _this.options.fail && _this.options.fail();
                        }
                        if (!imgSlideBlock) {
                            // 设置样式
                            _this.slideHintInfo.style.height = "22px";
                            setTimeout(function () {
                                _this.slideHintInfo.style.height = "0px";
                            }, 1300);
                            _this.slideBtn.style.backgroundPosition = "-3 500px";
                            _this.slideBtn.style.left = "0";
                            _this.slideBtn.style.transition = "left 0.6s";
                            _this.slideHint.style.opacity = "1";
                        }
                        if (indxFirst) {
                            $(".slideBarContent").hide();
                            indxFirst = false;
                        }
                        imgSlideBlock = false;
                        //超时自动刷新图片
                        setTimeout(function () {
                            if (_this.timer > 10) {
                                _this.refreshBtnClick(_this);
                            }
                        }, 1000);
                    }
                },

                //移动端----

                touchstart: function (_this, e) {

                    e.preventDefault(); //阻止默认行为,禁止滚动

                    _this.startX = event.touches[0].clientX;

                    if (_this.startTamp == 0) {
                        _this.startTamp = Math.round(new Date().getTime())
                    }

                    var target = e.target;
                    target.style.backgroundPosition = "-3 500px";
                    _this.slideHint.style.opacity = "0";
                    if ((_this.isSuccess && imgSlideBlock) || closeImg) {
                        _this.cutImg();
                    }

                    document.addEventListener('touchmove', touchmove);
                    document.addEventListener('touchend', touchend);

                    if (imgSlideBlock) {
                        touchup();
                    }

                    // 拖拽
                    function touchmove(event) {

                        if (touchmoveFirst) {
                            _this.startTamp = Math.round(new Date().getTime())
                        }

                        _this.x = event.touches[0].clientX - _this.startX;

                        if (_this.x < 0) {
                            _this.slideBtn.style.left = "0px";
                            _this.slideBlock.style.left = "2px";
                        } else if (_this.x >= 0 && _this.x <= 250) {
                            _this.slideBtn.style.left = _this.x + "px";
                            _this.slideBlock.style.left = _this.x + "px";
                        } else if (_this.x >= 250) {
                            _this.slideBtn.style.left = "250px";
                            _this.slideBlock.style.left = "250px";
                        } else {
                            _this.slideBtn.style.left = "0px";
                            _this.slideBlock.style.left = "0px";
                        }

                        _this.slideBtn.style.transition = "none";
                        _this.slideBlock.style.transition = "none";
                        touchmoveFirst = false;

                    };

                    // 放开
                    function touchend() {

                        document.removeEventListener('touchstart', touchmove);
                        document.removeEventListener('touchend', touchend);

                        var left = _this.slideBlock.style.left;
                        left = parseInt(left.substring(0, left.length - 2));
                        _this.endTamp = Math.round(new Date().getTime())
                        _this.timer = ((parseInt(_this.endTamp) - parseInt(_this.startTamp)) / 1000).toFixed(2);
                        // console.log("_this.timer:" + _this.timer);
                        if (_this.timer > 10) {
                            _this.isSuccess = false;
                            // 设置样式
                            // 裁剪图片(拼图的一块)
                            _this.slideBlock.style.left = "2px";
                            // 完成拼图
                            _this.slideBlock.style.transition = "left 0.6s";
                            // 错误弹出的图标

                            _this.slideIcon.style.backgroundPosition = "0 -1229px";
                            _this.slideType.className = "slide-text-type redColor";
                            _this.slideType.innerHTML = "验证失败:";
                            _this.slideContent.innerHTML = "滑块拼图已超时";
                            sliderStatus = 0;
                            _this.options.fail && _this.options.fail();
                        } else if (_this.resultX > (left - 15) && _this.resultX < (left + 15)) {
                            _this.isSuccess = true;
                            // 裁剪图片(拼图的一块)
                            _this.slideBlock.style.opacity = "0";
                            _this.slideBlock.style.transition = "opacity 0.6s";
                            // 裁剪的区域(黑黑的那一块)
                            _this.cutBlock.style.opacity = "0";
                            _this.cutBlock.style.transition = "opacity 0.6s";
                            // 正确弹出的图标

                            _this.slideIcon.style.backgroundPosition = "0 -1207px";
                            _this.slideType.className = "slide-text-type greenColor";
                            _this.slideType.innerHTML = "验证通过:";
                            _this.slideContent.innerHTML = "用时" + _this.timer + "s";

                            _this.options.success && _this.options.success();
                            var formData = new FormData($('#loginForm')[0]);
                            $.ajax({
                                url: "${ctx}/oauthController/login",
                                type: 'POST',
                                data: formData,
                                async: true,
                                cache: false,
                                contentType: false,
                                processData: false,
                                success: function (data) {
                                    var result = eval("(" + data + ")");
                                    if (result.ret == "-1") {
                                        setTimeout(function () {
                                            _this.cutBlock.style.display = "none";
                                            _this.slideBlock.style.left = "2px";
                                            _this.reToNewImg();
                                            _this.closeBtnClick(_this);
                                            $("#msg").html("<font color='#f50000' style='font-weight:bold;'>" + result.msg + "</font>");
                                        }, 1000);

                                    } else {
                                        window.location.href = "${request.getContextPath()}/mainFrame";
                                    }
                                },
                                error: function (data) {
                                    var result = eval("(" + data + ")");
                                }
                            });
                        } else if (!imgSlideBlock) {
                            _this.isSuccess = false;
                            // 设置样式
                            // 裁剪图片(拼图的一块)
                            _this.slideBlock.style.left = "2px";
                            _this.slideBlock.style.transition = "left 0.6s";
                            // 错误弹出的图标
                            _this.slideIcon.style.backgroundPosition = "0 -1229px";
                            _this.slideType.className = "slide-text-type redColor";
                            _this.slideType.innerHTML = "验证失败:";
                            _this.slideContent.innerHTML = "拖动滑块将悬浮图像正确拼合";
                            sliderStatus = 0;
                            _this.options.fail && _this.options.fail();
                        }
                        if (!imgSlideBlock) {
                            // 设置样式
                            _this.slideHintInfo.style.height = "22px";
                            setTimeout(function () {
                                _this.slideHintInfo.style.height = "0px";
                            }, 1300);
                            _this.slideBtn.style.backgroundPosition = "-3 500px";
                            _this.slideBtn.style.left = "0";
                            _this.slideBtn.style.transition = "left 0.6s";
                            _this.slideHint.style.opacity = "1";
                        }
                        if (indxFirst) {
                            $(".slideBarContent").hide();
                            indxFirst = false;
                        }
                        imgSlideBlock = false;
                        //超时自动刷新图片
                        setTimeout(function () {
                            if (_this.timer > 10) {
                                _this.refreshBtnClick(_this);
                            }
                        }, 1000);
                    }
                }
            }
            // SliderBar.prototype.touchstart = SliderBar.prototype.mousemove;

            window.SliderBar = SliderBar;
        }(window, document));
    </script>

</head>
<!-- END HEAD -->

<body class=" login" style="background-color: #ffffff!important;background-image: url(${ctx}/metronic/pages/img/background/23.jpg);background-repeat: no-repeat;background-size:cover; ">
<div class="menu-toggler sidebar-toggler"></div>
<!-- END SIDEBAR TOGGLER BUTTON -->
<!-- BEGIN LOGO -->
<div class="logo" style="padding-left: 80px!important;text-align: left;">
    <a href="index.html"> <span style="font-size: 30px; color: white;vertical-align: bottom">新职业平台</span>
    </a>
</div>
<!-- END LOGO -->
<!-- BEGIN LOGIN -->
<#--<div style="background-image: url('${ctx}/static/img/BG.2a8643f7.png' ) no-repeat; background-repeat:no-repeat;"></div>-->
<div style="background-repeat:no-repeat;height: 720px;">
    <div style="height: 50px"></div>
    <#--<video autoplay loop muted>-->
        <#--<source src="${ctx}/images/mp4/11.mp4" type="video/mp4"/>-->
    <#--</video>-->





    <div class="slideBarContent" style="background-color: white !important;">
        <div id="slideBar"></div>
    </div>
    <div class="content" style="width: 320px !important;margin: 40px 0 10px 60%!important;background: rgba(0,0,0,0.3);">
        <!-- BEGIN LOGIN FORM -->
        <form id="loginForm" class="login-form form-horizontal" method="post">
            <h3 class="form-title " style="color:#eec20a">登 录</h3>
            <div class="alert alert-danger display-hide">
                <button class="close" data-close="alert"></button>
                <span> Enter any username and password. </span>
            </div>
            <div class="form-group" style="margin-right: 0; margin-left: 0">
                <!--ie8, ie9 does not support html5 placeholder, so we just show field title for that-->
                <label class="control-label visible-ie8 visible-ie9">用户名</label> <input
                        class="form-control form-control-solid placeholder-no-fix"
                        type="text" autocomplete="off" placeholder="用户名" id="userName" name="userName"/>
            </div>
            <div class="form-group" style="margin-right: 0; margin-left: 0">
                <label class="control-label visible-ie8 visible-ie9">密码</label> <input
                        class="form-control form-control-solid placeholder-no-fix"
                        type="password" autocomplete="off" placeholder="密码" id="password" name="password"/>
            </div>

            <#--			<div class="form-group" style="margin-right: 0; margin-left: 0">
                            <div id="slider">
                                <div id="slider_bg"></div>
                                <span id="label"></span>
                                <span id="labelTip">拖动火箭验证</span>
                            </div>

                        </div>-->
            <div id="msg"></div>

            <div class="form-actions" style="border-bottom:0px">
                <button type="submit" class="btn uppercase" style="width: 100%!important;background-color:#eec20a;">登录
                </button>
                <!-- 				<label class="rememberme checkDifference18"> <input type="checkbox" -->
                <!-- 					name="remember" value="1" />Remember -->
                <!-- 				</label> <a href="javascript:;" id="forget-password" class="forget-password">Forgot -->
                <!-- 					Password?</a> -->
            </div>
            <#--<div class="create-account" style="padding-top: 0; padding-bottom: 25px; kgrobacund-color: #ffffff;">
                <p style="padding-left: 40px;padding-right: 40px; padding-bottom: 25px;">
                    <i style="float: left;">没有账号？<a style="color: #0c91e5" href="${ctx}/index?req=register">去注册></a></i>
                    <i style="float: right;"><a style="color: #0c91e5; " href="${ctx}/index?req=forgetPwd">忘记密码</a></i>
                </p>
            </div>-->
        </form>
        <!-- END LOGIN FORM -->

        <!-- BEGIN FORGOT PASSWORD FORM -->
        <form class="forget-form" action="index.html" method="post">
            <h3 class="font-green">Forget Password ?</h3>
            <p>Enter your e-mail address below to reset your password.</p>
            <div class="form-group">
                <input class="form-control placeholder-no-fix" type="text"
                       autocomplete="off" placeholder="Email" name="email"/>
            </div>
            <div class="form-actions">
                <button type="button" id="back-btn" class="btn btn-default">Back</button>
                <button type="submit" class="btn btn-success uppercase pull-right">Submit</button>
            </div>
        </form>
        <!-- END FORGOT PASSWORD FORM -->
    </div>
    <div style="height: 80px;"></div>
</div>

<div class="copyright">2019 ©
</div>
<script type="text/javascript">
    var dataList = ["0", "1"];
    var options = {
        dataList: dataList,
        success: function () {
            console.log("show");
        },
        fail: function () {
            console.log("fail");
        }
    };
    SliderBar("slideBar", options);
</script>

</body>

</html>