<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>Title</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <#--widget-->
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        function findObjectListByStatus(status) {

            if(status!='null'){
                var _status=[status];
            }else{
                var _status=['BAX24','BAX27','BAX34','BAX37'];
            }
            window.location.href = "${ctx}/claimCaseStatusCountInfoDetailController/findClaimCaseObjectsByStatus?status=" + _status
        }
        function findOtherObjectListByStatus() {

            var _status=['BAX24','BAX27','BAX34','BAX37','BAX10','BAX21','BAX22','BAX25','BAX31','BAX32','BAX35','BAX99'];
            window.location.href = "${ctx}/claimCaseStatusCountInfoDetailController/findUknownClaimCaseObjectsByStatus?status=" + _status
        }
        function findCaseListByStatus(status) {
            var _status=[status];
            window.location.href = "${ctx}/claimCaseStatusCountInfoDetailController/findClaimCaseByStatus?status=" + _status
        }
        function findExceptionCaseObjectsByStatus(status) {
            var _status=[status];
            window.location.href = "${ctx}/claimCaseStatusCountInfoDetailController/findExceptionCaseObjectsByStatus?status=" + _status
        }

    </script>
    <style>
        .border{
            border: 1px solid #1676FF;
            border-radius:10px;
        }
        .flex{
            display: flex;

        }
        .box{
            margin:10px 0 10px 20px;
            width: 22%;
            height: 83px;
            text-align: center;
            padding-top: 15px;
            cursor: pointer;
        }
        .box > span{
            display: block;
            padding-top: 10px;
        }
        .border > div{
            font-size: 16px;
            font-font: '黑体';
        }
    </style>
</head>
<body>
<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>新职业平台</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">待处理任务</span></li>
                </ul>
            </div>
            <div class="portlet-body flex" >
                <div class="border" style="width: 70%;padding-bottom: 10px;border-color: #1676FF">
                    <div style="margin:10px 0 0 20px;font-size: 24px;font-weight: bold;">进行中案件数：${all_case} (案件数)</div>
                    <div class="flex">
                        <div class="border box" onclick="findCaseListByStatus('aax20')">
                            报案审核
                            <span>${case_aax20}</span>
                        </div>
                        <div class="border box" onclick="findCaseListByStatus('abx10')">
                            估损/理算审核/驳回/签章
                            <span>${case_ars}</span>
                        </div>
                        <div class="border box" onclick="findCaseListByStatus('acx10')">
                            待推送
                            <span>${case_acx10}</span>
                        </div>
                        <div class="border box" onclick="findCaseListByStatus('acx21')">
                            待回盘
                            <span>${case_acx21}</span>
                        </div>
                    </div>
                    <div class="border" style="width: 95%;margin: 0 auto;">
                        <div style="margin:10px 0 0 20px">估损/理算审核(赔付对象为单位)</div>
                        <div class="flex">
                            <div class="border box" onclick="findObjectListByStatus('BAX10')">
                                待分配
                                <span>${case_BAX10}</span>
                            </div>
                            <div class="border box" onclick="findObjectListByStatus('BAX21')">
                                估损待提交
                                <span>${case_BAX21}</span>
                            </div>
                            <div class="border box" onclick="findObjectListByStatus('BAX22')">
                                估损内部审核
                                <span>${case_BAX22}</span>
                            </div>
                            <div class="border box" onclick="findObjectListByStatus('null')">
                                驳回
                                <span>${reject}</span>
                            </div>
                        </div>
                        <div class="flex">
                            <div class="border box" onclick="findObjectListByStatus('BAX25')">
                                估损保司审核
                                <span>${case_BAX25}</span>
                            </div>
                            <div class="border box" onclick="findObjectListByStatus('BAX31')">
                                理算待提交
                                <span>${case_BAX31}</span>
                            </div>
                            <div class="border box" onclick="findObjectListByStatus('BAX32')">
                                理算内部审核
                                <span>${case_BAX32}</span>
                            </div>
                            <div class="border box" onclick="findObjectListByStatus('BAX35')">
                                理算保司审核
                                <span>${case_BAX35}</span>
                            </div>
                        </div>
                        <div class="flex">
                            <div class="border box" onclick="findOtherObjectListByStatus()">
                                未知状态
                                <span>${unknown_status}</span>
                            </div>
                            <#--新加签章任务状态，并且兼容之前的逻辑-->
                            <div class="border box" onclick="findObjectListByStatus(['BAX33','BAX36','BAX40'])" >
                                签章任务
                                <span>${signature}</span>
                            </div>
                            <div class="border box" onclick="findObjectListByStatus('BAX38')" >
                                签字任务
                                <span>${case_BAX38}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="width: 15%;">
                    <div class="border box " onclick="findExceptionCaseObjectsByStatus('BAX99')" style="width: 100%;height:100px;margin-left: 20px;padding-top:20px;border-color: #EA0000 ;font-size: 16px;font-font: '黑体';">
                        异常案件数
                        <span>${case_exceptions}</span>
                    </div>
                    <div class="border box " onclick="findCaseListByStatus('aax21')" style="width: 100%;height:100px;margin-left: 20px;padding-top:20px;border-color: #F66333 ;font-size: 16px;font-font: '黑体';">
                        初审挂起
                        <span>${case_aax21}</span>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
</body>
</html>