<!DOCTYPE html>
<html>
<head>
    <title>保司核损成功</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport">
    
    <!-- 引入公共CSS -->
    <link href="${ctx}/metronic/global/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/css/components.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/layouts/layout/css/layout.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/css/custom.css" rel="stylesheet" type="text/css"/>
    
    <style>
        body {
            background-color: #f5f5f5;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .confirm-container {
            max-width: 800px;
            margin: 20px auto;
            background-color: white;
            border-radius: 8px;
            padding: 40px 30px;
            text-align: center;
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            background-color: white;
            border: 3px solid #52c41a;
            border-radius: 50% !important;
            margin: 0 auto 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
        }
        
        .success-icon i {
            color: #52c41a;
            font-size: 40px;
        }
        
        .page-title {
            font-size: 24px;
            color: #333;
            margin-bottom: 40px;
            font-weight: normal;
        }
        
        .info-table {
            width: 100%;
            margin-bottom: 40px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-item {
            display: flex;
            flex: 1;
            padding: 0 20px;
        }
        
        .info-item:first-child {
            padding-left: 0;
        }
        
        .info-item:last-child {
            padding-right: 0;
        }
        
        .info-label {
            color: #666;
            font-size: 16px;
            text-align: left;
            min-width: 100px;
        }
        
        .info-value {
            color: #333;
            font-size: 15px;
            text-align: right;
            flex: 1;
        }
        
        .btn-return {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 12px 40px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            position: relative;
        }
        
        .btn-return:hover {
            background-color: #40a9ff;
        }
        
        .error-message {
            background-color: #fff2f0;
            color: #cf1322;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #ffccc7;
        }
    </style>
</head>

<body>
    <div class="confirm-container">
        <!-- 成功图标 -->
        <div class="success-icon">
            <i class="fa fa-check"></i>
        </div>

        <!-- 页面标题 -->
        <div class="page-title">提交成功</div>

        <!-- 错误信息显示 -->
        <#if error??>
        <div class="error-message">
            <i class="fa fa-exclamation-triangle"></i> <span>${error}</span>
        </div>
        </#if>

    <#if submitInfo??>
    <!-- 信息展示 -->
    <div class="info-table">
        <div class="info-row">
            <div class="info-item">
                <div class="info-label">报案号</div>
                <div class="info-value">${submitInfo.reportNo!''}</div>
            </div>
            <div class="info-item">
                <div class="info-label">任务类型</div>
                <div class="info-value">${submitInfo.taskType!''}</div>
            </div>
        </div>
        <div class="info-row">
            <div class="info-item">
                <div class="info-label">任务接收岗</div>
                <div class="info-value">${submitInfo.acceptDepartment!''}</div>
            </div>
            <div class="info-item">
                <div class="info-label">任务状态</div>
                <div class="info-value">${submitInfo.taskStatus!''}</div>
            </div>
        </div>
    </div>

    <!-- 返回按钮 -->
    <button type="button" class="btn-return" onclick="confirmSubmit()">
        关闭
    </button>
    <#else>
    <div class="error-message">
        <i class="fa fa-warning"></i> 无法获取提交信息，请重新尝试。
    </div>
    <button type="button" class="btn-return" onclick="goBack()">
        关闭
    </button>
</#if>
    </div>

    <!-- 引入公共JS -->
    <script src="${ctx}/metronic/global/plugins/jquery.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/bootstrap/js/bootstrap.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/scripts/app.min.js" type="text/javascript"></script>
    
    <script type="text/javascript">
        jQuery(document).ready(function() {
            App.init();
        });

        // 返回核损任务管理页面
        function goBack() {
            window.close();

        }

        // 确认提交
        function confirmSubmit() {
            // 直接跳转到核损任务管理页面
            window.close();
        }
    </script>

</body>
</html> 