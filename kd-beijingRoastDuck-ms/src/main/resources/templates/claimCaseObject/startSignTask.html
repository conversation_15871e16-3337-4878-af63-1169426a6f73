<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>物损详情</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <script src="${ctx}/carousel/dataCollection/viewer.js"></script>
    <script src="${ctx}/carousel/dataCollection/viewermain.js"></script>
    <link href="${ctx}/carousel/dataCollection/viewer.css" rel="stylesheet">
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/metronic/global/scripts/app.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/layout.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/demo.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/jquery-multi-select/js/jquery.multi-select.js"
            type="text/javascript"></script>

    <script src="${ctx}/js/idCardCheck.js" type="text/javascript"></script>
    <style>

        .clear-margin {
            margin: 0px;
        }

        .clear-padding {
            padding: 0px;
        }

        .clear-padding-left {
            padding-left: 0px;
        }

        .clear-padding-right {
            padding-right: 0px;
        }

        .chart-search {
            margin-bottom: 10px;
            display: none;
        }


        .search-channel-content {
            height: 100px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .search-ul-list {
            list-style-type: none;
            height: 20px;
            padding-left: 0px;
            color: black;
        }

        .channel {
            float: left;
            display: block;
        }

        .typeClick {
            height: 112px;
            margin-bottom: 10px;
            border: 2px solid black;
            border-radius: 5px !important;
            margin-left: 20px;
            cursor: pointer
        }

        .typeClickClick {
            height: 112px;
            margin-bottom: 10px;
            border: 2px solid #1676ff;
            background-color: #1676ff;
            color: white;
            border-radius: 5px !important;
            margin-left: 20px;
            cursor: pointer
        }

        .has-error {
            border-color: #ce0d0d !important;
            color: red !important;
        }

        .bckColor {
            background-color: rgba(51, 153, 241, 0.3);
        }

        .imageListChoose {
            /*background-color: #bad6ff !important;*/
            border: 5px solid #055ad7;
        }

        .collectionCompleted {
            background-color: #E8F2FF;
        }

        .collectionButton {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: transparent;
            border-radius: 6px !important;
            color: #1ABB00;
        }

        .collectionButtonClick {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00 !important;
            border-radius: 6px !important;
            color: #FFFFFF;
        }

        .collectionData:hover {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00;
            color: #FFFFFF;
        }

        .middleTable {
            border: 4px solid #1767ff !important;
        }

        .hangup {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #ce0d0d !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }

        .error {
            color: #ce0d0d !important;
        }

        .txt-over-hide {
            color: red;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: block;
            width: 650px;
        }

        .jump {
            /*float: right;*/
            border-radius: 5px !important;
            color: #3399ff !important;
            background-color: #fff !important;
            border-color: #3399ff;
            height: 40px;
            width: 100%;
        }

        .genTask {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #3399ff !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }

        .thClass {
            text-align: center;
            color: #fff;
        }

        .typeSelect {
            border-color: #3399ff;
            background-color: #3399ff;
        }

        .thumbnail-img {
            width: 50px;
            height: 45px;
            border: 4px solid #D6D6C1;
        }

        .selected-thumbnail-img {
            border: 4px solid #3399ff !important;
        }

        .collection-thumbnail-img {
            border: 4px solid #00B176;
        }

        /*里面的代码可以根据自己需求去进行更改*/
        /* 设置滚动条的样式 */
        ::-webkit-scrollbar {
            width: 5px;
        }

        /* 滚动槽 */
        ::-webkit-scrollbar-track {
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
            border-radius: 10px;
        }

        /* 滚动条滑块 */
        ::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
        }

        ::-webkit-scrollbar-thumb:window-inactive {
            background: rgba(255, 0, 0, 0.4);
        }

        .ticket-information {
            height: 1050px;
            overflow-y: scroll;
        }

        .dataDisplayArea {
            width: 100%;
            height: 19%;
            border-radius: 10px !important;
            /*border: 1px solid #aca8a8;*/
            border: 1px solid #797979;
            cursor: pointer;
            margin-top: 10px;
            overflow: hidden;
        }

        .selected-dataDisplayArea {
            background-color: #91cae9;
        }

        .submit-dataDisplayArea {
            border: 3px solid #44db69 !important;
        }

        .change-dataDisplayArea {
            border: 3px solid red !important;
        }

        .dataDisplayArea > div {
            height: 20%;
            padding: 10px 0px 0px 0px !important;
        }

        .dataDisplayArea-head {
            background-color: #3399ff;
            /*border-radius: 10px 10px 0px 0px !important;*/
        }

        .dataDisplayArea-head-left {
            color: #ffffff;
            font-size: 15px;
            padding: 0px 0px 0px 5px;
        }

        .dataDisplayArea-head-right {
            text-align: right;
            padding: 0px !important;
        }

        .dataDisplayArea-body-left {
            opacity: 0.7;
        }

        .dataDisplayArea-body-right {
            text-align: right;
        }

        .row {
            margin: 0px 0px;
        }

        .subject-name {
            font-size: 20px;
        }

        .estimateInventoryDataBlue {
            background-color: #1676ff;
        }

        .line-center {
            display: inline-block;
            background-color: blue;
            border-radius: 50%;
            height: 30px;
            width: 30px;
            color: white;
            text-align: center;
            line-height: 30px;
            font-size: 18px;
            font-weight: bold;
        }

        .icon-plus {
            font-size: 30px;
            line-height: 23px;
        }

        .icon-plus:hover {
            cursor: pointer;
        }

        .dataDisplayArea-head-img {
            float: right;
            width: 30px;
            height: 25px;
            margin: 10px 20px 10px 0px;
        }

        .dataDisplayArea-head-img:hover {
            cursor: pointer;
        }

        .form-control {
            height: 28px !important;
        }

        .label-right {
            font-size: 13px;
            text-align: right;
        }

        .align-item-center {
            display: flex;
            align-items: center;

        }

        .col-sm-1 {
            text-align: center;
        }

        textarea[class='layui-layer-input'] {
            outline: 0 !important;
            width: 700px !important;
            height: 350px !important;
            padding-left: 10px !important;
            border: 1px solid #e6e6e6 !important;
            color: #333 !important;
            box-shadow: none !important;
        }

        button[name='actionBtn'] {
            color: white;
        }

        select {
            padding: 0px 12px !important;
        }

        div[name='dataCollectAreaRow'] input {
            padding: 0px 5px;
        }


        #firstRegistrationTime[readonly] {
            background-color: white;
        }

        #lossAssessmentTime[readonly] {
            background-color: white;
        }
    </style>

    <script type="text/javascript">

        var viewer = null;

        var rowNum = 0;

        $(function () {


            /*图片展示工具*/
            (function () {
                viewer = new Viewer({
                    activeId: null
                });
            })();

            var layerTop = top.layer;
            var errorMsg = "${error}";
            if (errorMsg) {
                layerTop.msg(errorMsg, {
                    icon: 2,
                    time: 3000
                });
            }


            /*点击缩略图*/
            $('body').on('click', '.thumbnail-img', function () {
                if ($(this).hasClass('selected-thumbnail-img')) {
                    return;
                }
                let attrId = $(this).data('fileid');
                $('.selected-thumbnail-img').removeClass('selected-thumbnail-img');
                $(this).addClass('selected-thumbnail-img');
                viewer.showImageByFileId(attrId);
                let imgNumber = $(this).data('img-number');
                let offset = imgNumber * 95 - (1050 - 95) / 2;
                $('#thumbnail').scrollTop(offset);
                $("div[class='col-sm-4 dataAllArea']").animate({
                    scrollTop: $("#" + attrId).offset().top
                }, 1000);
            });


            /*A/D切换影像*/
            $('body').on('click', '.viewer-list > li', function () {
                let attchId = $(this).find('img').attr('alt');
                $('#thumbnail-' + attchId).trigger('click');
            });

            /*初始化签字人信息*/
            <#if signTemplateShow?exists>
            <#list signTemplateShow.keySet() as key>
            {
                let templateId = `${key}`;
                <#if signTemplateShow.get(key).get("personInfo")?exists>
                <#list signTemplateShow.get(key).get("personInfo") as info>
                addPersonInfo(templateId, {
                    "personName": "${info.personName}",
                    "personIdType": "${info.personIdType}",
                    "personIdNumber": "${info.personIdNumber}",
                    "personPhone": "${info.personPhone}"
                })
                </#list>
                </#if>
            }
            </#list>
            </#if>


            /*
            删除签字人区域
            $("body").on("click", "div[name='personInfo']", function (event) {
                $(this).remove();
            })
            */
            /*点击录入区域自动跳转对应的影像*/
            $("body").on("click", "div[name='templateArea']", function (event) {

                let attrId = $(this).attr('id');
                dataArea4Attach(attrId)
            })

            /*点击录入区域自动跳转对应的影像*/
            $("body").on("click", "input[name='personName']", function (event) {
                event.stopPropagation();
                event.preventDefault();
                let attrId = $(this).closest("div[name='templateArea']").attr("id");
                dataArea4Attach(attrId)
            })
            /*点击录入区域自动跳转对应的影像*/
            $("body").on("click", "select[name='personIdType']", function (event) {
                event.stopPropagation();
                event.preventDefault();
                let attrId = $(this).closest("div[name='templateArea']").attr("id");
                dataArea4Attach(attrId)
            })
            /*点击录入区域自动跳转对应的影像*/
            $("body").on("click", "input[name='personIdNumber']", function (event) {
                event.stopPropagation();
                event.preventDefault();
                let attrId = $(this).closest("div[name='templateArea']").attr("id");
                dataArea4Attach(attrId)
            })
            /*点击录入区域自动跳转对应的影像*/
            $("body").on("click", "input[name='personPhone']", function (event) {
                event.stopPropagation();
                event.preventDefault();
                let attrId = $(this).closest("div[name='templateArea']").attr("id");
                dataArea4Attach(attrId)
            })

        });

        //点击录入区域自动对应模板影像的方法
        function dataArea4Attach(attrId) {
            let _this = $('#thumbnail-' + attrId)
            if (_this.hasClass('selected-thumbnail-img')) {
                return;
            }
            $('.selected-thumbnail-img').removeClass('selected-thumbnail-img');
            _this.addClass('selected-thumbnail-img');
            viewer.showImageByFileId(attrId);
            let imgNumber = _this.data('img-number');
            let offset = imgNumber * 95 - (1050 - 95) / 2;
            $('#thumbnail').scrollTop(offset);
        }


        //增加签字人
        function addPersonInfo(templateId, obj) {

            let personName = obj.personName ? obj.personName : '';
            let personIdType = obj.personIdType ? obj.personIdType : '';
            let personIdNumber = obj.personIdNumber ? obj.personIdNumber : '';
            let personPhone = obj.personPhone ? obj.personPhone : '';

            let str = `<div name="personInfo" style="border: 1px solid red;padding-top: 15px;padding-left: 15px;margin-bottom: 15px;cursor: pointer">
                                <div >
                                    <div class="row">
                                        <div style="display: flex;align-items: center;">
                                            <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">
                                                姓名：
                                            </div>
                                            <div class="col-sm-9 clear-padding-left form-group">
                                                <input  type="text"
                                                       name="personName" class="form-control"
                                                       value="` + personName + `" data-valid="none">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div >
                                    <div class="row">
                                        <div style="display: flex;align-items: center;">
                                            <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">
                                                证件类型：
                                            </div>
                                            <div class="col-sm-9 clear-padding-left form-group" style="padding-left: 0;">
                                                <select class="form-control select2-multiple"  name="personIdType">
                                                <option value="CRED_PSN_CH_IDCARD" ` + (personIdType == 'CRED_PSN_CH_IDCARD' ? 'selected' : '') + `>中国大陆居民身份证</option>
                                                <option value="CRED_PSN_CH_HONGKONG" ` + (personIdType == 'CRED_PSN_CH_HONGKONG' ? 'selected' : '') + `>香港来往大陆通行证</option>
                                                <option value="CRED_PSN_CH_MACAO" ` + (personIdType == 'CRED_PSN_CH_MACAO' ? 'selected' : '') + `>澳门来往大陆通行证</option>
                                                <option value="CRED_PSN_CH_TWCARD" ` + (personIdType == 'CRED_PSN_CH_TWCARD' ? 'selected' : '') + `>台湾来往大陆通行证</option>
                                                <option value="CRED_PSN_PASSPORT" ` + (personIdType == 'CRED_PSN_PASSPORT' ? 'selected' : '') + `>护照</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div >
                                    <div class="row">
                                        <div style="display: flex;align-items: center;">
                                            <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">
                                                证件号码：
                                            </div>
                                            <div class="col-sm-9 clear-padding-left form-group">
                                                <input   type="text"
                                                       name="personIdNumber" class="form-control"
                                                       value="` + personIdNumber + `" data-valid="none">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div >
                                    <div class="row">
                                        <div style="display: flex;align-items: center;">
                                            <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">
                                                电话号码：
                                            </div>
                                            <div class="col-sm-9 clear-padding-left form-group">
                                                <input  type="text"
                                                       name="personPhone" class="form-control"
                                                       value="` + personPhone + `" data-valid="none">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>`;

            $("#" + templateId).append(str);
        }


        //生成uuid
        function guid() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                var r = Math.random() * 16 | 0,
                    v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        //发起签字任务
        function pushSign() {
            let isCanPush = true;
            let dataList = [];
            $.each($("div[name='templateArea']"), function () {
                let templateData = {};
                let template_this = $(this);
                let templateId = template_this.attr("id")
                let title = template_this.attr("title")
                templateData.templateId = templateId;
                templateData.components = [];
                console.log(templateId);
                template_this.find("[dataType]").each(function () {
                    let dataType = $(this).attr("dataType");
                    let componentKey = $(this).attr("name");
                    let componentValue = $(this).val();
                    if (componentValue && componentValue != '') {
                        let componentValue = $(this).val();
                        templateData.components.push({"componentKey": componentKey, "componentValue": componentValue})
                    } else {
                        layer.tips('缺少数据', this, {tipsMore: true, icon: 2, time: 3000, tips: 1});
                        isCanPush = false;
                    }
                })

                templateData.personInfo = [];
                template_this.find("div[name='personInfo']").each(function () {
                    let personInfoDetailData = {};
                    for (let fieldName of ["personName", "personIdType", "personIdNumber", "personPhone"]) {
                        let fieldThis = $(this).find("[name='" + fieldName + "']");
                        let fieldData = fieldThis.val();
                        if (fieldData && fieldData != '') {
                            personInfoDetailData[fieldName] = fieldData;
                        } else {
                            layer.tips('缺少数据', fieldThis, {tipsMore: true, icon: 2, time: 3000, tips: 1});
                            isCanPush = false;
                        }
                    }
                    templateData.personInfo.push(personInfoDetailData)
                })

                if (templateData.personInfo.length == 0) {
                    layer.tips('最少要有一个签字人信息', template_this, {tipsMore: true, icon: 2, time: 3000, tips: 1});
                    isCanPush = false;
                }
                dataList.push(templateData)
            })
            if (isCanPush) {
                let formData = new FormData();
                formData.append("claimCaseObjectId", "${claimCaseObjectId}");
                formData.append("jsonStr", JSON.stringify(dataList));
                $.ajax({
                    url: "${ctx}/claimCaseObjectController/pushSign",
                    type: 'POST',
                    data: formData,
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        console.log(result);
                        if (result.ret == "0") {
                            changePushLogStatus();
                            layer.msg("发起签字成功", {icon: 1, time: 2000}, function (index) {
                                window.location.href = "${ctx}/claimCaseObjectController/claimCaseObjectList?status=7&comeFrom=1&claimCaseNo=${claimCase.claimCaseNo}";
                                layer.close(index);
                            })
                        }else {
                            layer.msg(result.msg, {icon: 2, time: 3000})
                        }
                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                });
            }
        }

        //如果存在推送任务，说明这是个重新发起的操作，将原操作关闭
        function changePushLogStatus() {
            <#if signTemplatePushLogId?exists>
                let signTemplatePushLogId = `${signTemplatePushLogId}`;
                let formData = new FormData();
                formData.append("id", signTemplatePushLogId);
                try {
                    $.ajax({
                        url: "${ctx}/signTemplateController/closePushLog",
                        type: 'POST',
                        data: formData,
                        async: true,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            console.log(result);
                        },
                        error: function (data) {
                        }
                    });
                }catch (e) {
                    console.log(e)
                }
            </#if>
        }

    </script>
</head>


<body id="qc-Body" style="overflow-x:hidden;background: #fff;">
<div class="container-fluid">
    <div class="row">
        <form id="inputForm" onsubmit="return false">
            <div class="row" style="padding-top: 25px">
                <div class="col-sm-8">
                    <div class="col-sm-12" style="margin-bottom: 5px;padding-left: 0px;padding-right: 0px;">
                        <div class="col-sm-3" style="padding-left: 0px;">
                            案件号：${claimCase.claimCaseNo}
                        </div>
                        <div class="col-sm-3" style="padding-left: 0px;">
                            姓名：${claimCase.treatName}
                        </div>
                        <div class="col-sm-3" style="padding-left: 0px;">
                            是否延迟报立案：<#if claimCase.delayReport?? && claimCase.delayReport == 1>是<#else >否</#if>
                        </div>
                        <div class="col-sm-3" style="padding-left: 0px;">
                            历史案件量：${historyCaseSize!'0'}个
                        </div>
                        <div class="col-sm-6 label-hide-overflow" style="padding-left: 0px;"
                             title="${claimCase.productName}">
                            产品名称：${claimCase.productName}
                        </div>
                        <div class="col-sm-6" style="padding-left: 0px;">
                            承保公司：
                            <#if claimCase.insCode == 'RB' || claimCase.insCode == 'HMRB'>
                                <span style="display:inline-block;background-color: #dbaf00;color: #fff;padding: 2px 6px;border-radius: 2px;">人保</span>
                            <#elseif claimCase.insCode == 'GY' || (claimCase.province == '辽宁省' && claimCase.treatDate?datetime gte "2023-09-29 00:00:00"?datetime) >
                                <span style="display:inline-block;background-color: #067ee5;color: #fff;padding: 2px 6px;border-radius: 2px;">国元</span>
                            <#elseif claimCase.insCode == 'DD'>
                                <span style="display:inline-block;background-color: #26c281;color: #fff;padding: 2px 6px;border-radius: 2px;">大地</span>
                            <#elseif claimCase.insCode == 'HX'>
                                <span style="display:inline-block;background-color: #ee5f5b;color: #fff;padding: 2px 6px;border-radius: 2px;">海峡</span>
                            <#elseif vo.insCode == 'JZTB'>
                                <span style="display:inline-block;background-color: #f78c45;color: #fff;padding: 2px 6px;border-radius: 2px;">太保</span>
                            </#if>
                        </div>
                        <div class="col-sm-6" style="padding-left: 0px;">
                            案件类型：
                            <#if claimCase.caseType == 'CP'>
                                <span style="display:inline-block;background-color: #067ee5;color: #fff;padding: 2px 6px;border-radius: 2px;">超赔</span>
                            <#elseif claimCase.caseType == 'YW'>
                                <span style="display:inline-block;background-color: #26c281;color: #fff;padding: 2px 6px;border-radius: 2px;">众包</span>
                            <#else>
                                <span style="display:inline-block;background-color: #26c281;color: #fff;padding: 2px 6px;border-radius: 2px;">雇主</span>
                            </#if>
                        </div>
                    </div>
                </div>
                <#if isCanPush=='true'>
                    <div class="col-sm-4">
                        <div class="pull-right" style="margin-bottom: 5px;padding-left: 0px;padding-right: 0px;">
                            <button class="btn btn-success" onclick="pushSign()">发起签字</button>
                        </div>
                    </div>
                </#if>

            </div>
            <!-- 左部分 -->
            <div class="col-sm-8" style="background: none!important;padding-right: 0px !important;">
                <#--审核头-->
                <div class="row">
                    <#-- 展示图片区域以及采集数据tbale -->
                    <div class="col-sm-12" style="padding: 5px 0px 0px 0px; background: none;">
                        <div class="row" style="height: 1050px; overflow: hidden;">
                            <div class="col-sm-1"
                                 style="height: 1050px;background-color: rgba(226, 226, 226, 0.5);padding-left: 0px!important;padding-right: 10px!important; border-right: groove;">
                                <#if signTemplateShow?exists>
                                    <div id="thumbnail"
                                         style="height: 1050px;<#if (signTemplateShow?size>11)>overflow-y: scroll;</#if>overflow-x:hidden">
                                        <ol style="padding-left:25px !important;/*padding-right: 15px !important;*/">
                                            <#list signTemplateShow.keySet() as key>
                                                <li style="width:auto; height:auto; cursor:pointer;text-align: center;padding-top:5px">
                                                    <img id="thumbnail-${key}"
                                                         class="thumbnail-img <#if key_index == 0>selected-thumbnail-img</#if>"
                                                         data-fileid="${key}" data-img-number=''
                                                         title="${(signTemplateShow.get(key).get('templateTitle'))!''}"
                                                         src="${(signTemplateShow.get(key).get('imgUrl'))}"
                                                         onerror="javascript:this.src='/a/job_done.png'"/>
                                                </li>
                                            </#list>
                                        </ol>
                                    </div>
                                </#if>
                            </div>
                            <div class="col-sm-11" style="height: 1300px;">
                                <ul id="images">
                                    <#if signTemplateShow?exists>
                                        <#list signTemplateShow.keySet() as key>
                                            <li hidden="hidden">
                                                <img data-fileid="${key}"
                                                     title="${(signTemplateShow.get(key).get('templateTitle'))!''}"
                                                     src="${(signTemplateShow.get(key).get('imgUrl'))}"
                                                     alt="${key}"/>
                                            </li>
                                        </#list>
                                    </#if>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <#if signTemplateShow?exists>
                <div class="col-sm-4 dataAllArea"
                     style="padding-left: 15px;height:1000px;overflow-y: scroll;overflow-x: hidden;">
                    <#list signTemplateShow.keySet() as key>
                    <#--<li hidden="hidden">
                        <img data-fileid="${key}"
                             title="${(signTemplateShow.get(key).get('templateTitle'))!''}"
                             src="${(signTemplateShow.get(key).get('imgUrl'))}"
                             alt="${key}"/>
                    </li>-->

                        <div class="row" style="margin-bottom: 20px;" name="templateArea" id="${key}"
                             title="${(signTemplateShow.get(key).get('templateTitle'))!''}">
                            <div style="margin-left: 5px;">
                                <img class="marginLeft15" src="${ctx}/images/u48.svg" align="left"
                                     style="margin-top: 3px;"/>
                                <span style="font-size: 16px;font-weight: bold;margin-left: 7px"> ${(signTemplateShow.get(key).get('templateTitle'))!''}</span>
                                <#--<button class="btn btn-success" onclick="addPersonInfo('${key}',this)">添加签字人</button>-->
                            </div>
                            <#if signTemplateShow.get(key).get('initValue')?exists>
                                <#list signTemplateShow.get(key).get('initValue') as info>
                                    <#switch info.type>
                                    <#--input框格式-->
                                        <#case 'text'>
                                            <div class="row">
                                                <div class="row">
                                                    <div style="margin: 15px;display: flex;align-items: center;">
                                                        <div class="col-sm-3 clear-padding"
                                                             style="font-size: 17px;font-weight: bold;">
                                                            ${info.name}
                                                        </div>
                                                        <div class="col-sm-9 clear-padding-left">
                                                            <input dataType="text" type="text"
                                                                   name="${info.field}" class="form-control"
                                                                   value="${info.value!''}" data-valid="none">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <#break>
                                        <#--文本域格式-->
                                        <#case 'textArea'>
                                            <div>
                                                <div class="row">
                                                    <div style="margin: 15px;display: flex;align-items: center;">
                                                        <div class="col-sm-3 clear-padding"
                                                             style="font-size: 17px;font-weight: bold;">
                                                            ${info.name}
                                                        </div>
                                                        <div class="col-sm-9 clear-padding-left form-group">
                                                            <textarea dataType="textArea" name="${info.field}"
                                                                      style="width: 100%;border: 1px solid #c2cad8;"
                                                                      cols="5" rows="5">${info.value!''}</textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <#break>
                                    </#switch>

                                </#list>
                            <#else >
                                <div class="row">
                                    <div class="row">
                                        <div style="margin: 15px;display: flex;align-items: center;">
                                            <div class="col-sm-12 clear-padding"
                                                 style="font-size: 30px;font-weight: bold;color: red;text-align: center">
                                                暂无数据
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </#if>


                        </div>

                    </#list>

                </div>
            </#if>


        </form>
    </div>
</div>

</body>
</html>

