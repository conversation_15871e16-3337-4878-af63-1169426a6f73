<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>审核</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <script src="${ctx}/carousel/dataCollection/viewer.js"></script>
    <script src="${ctx}/carousel/dataCollection/viewermain.js"></script>
    <link href="${ctx}/carousel/dataCollection/viewer.css" rel="stylesheet">
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/metronic/global/scripts/app.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/layout.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/demo.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/jquery-multi-select/js/jquery.multi-select.js"
            type="text/javascript"></script>

    <script src="${ctx}/js/genInjury.js" type="text/javascript"></script>
    <script src="${ctx}/js/idCardCheck.js" type="text/javascript"></script>
    <style>

        .container-fluid {
            margin: 20px;
        }

        .clear-margin {
            margin: 0px;
        }

        .clear-padding {
            padding: 0px;
        }

        table {
            border: 1px solid #C2C2C2;
            background-color: #FFF;
        }

        /*里面的代码可以根据自己需求去进行更改*/
        /* 设置滚动条的样式 */
        ::-webkit-scrollbar {
            width: 5px;
        }

        /* 滚动槽 */
        ::-webkit-scrollbar-track {
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
            border-radius: 10px;
        }

        /* 滚动条滑块 */
        ::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
        }

        ::-webkit-scrollbar-thumb:window-inactive {
            background: rgba(255, 0, 0, 0.4);
        }

        .ticket-information {
            height: 1050px;
            overflow-y: scroll;
        }

        .dataDisplayArea {
            width: 100%;
            height: 19%;
            border-radius: 10px !important;
            /*border: 1px solid #aca8a8;*/
            border: 1px solid #797979;
            cursor: pointer;
            margin-top: 10px;
            overflow: hidden;
        }

        .selected-dataDisplayArea {
            background-color: #91cae9;
        }

        .submit-dataDisplayArea {
            border: 3px solid #44db69 !important;
        }

        .change-dataDisplayArea {
            border: 3px solid red !important;
        }

        .dataDisplayArea > div {
            height: 20%;
            padding: 10px 0px 0px 0px !important;
        }

        .dataDisplayArea-head {
            background-color: #3399ff;
            /*border-radius: 10px 10px 0px 0px !important;*/
        }

        .dataDisplayArea-head-left {
            color: #ffffff;
            font-size: 15px;
            padding: 0px 0px 0px 5px;
        }

        .dataDisplayArea-head-right {
            text-align: right;
            padding: 0px !important;
        }

        .dataDisplayArea-head-img {
            width: 30px;
            height: 18px;
        }

        .dataDisplayArea-body-left {
            opacity: 0.7;
        }

        .dataDisplayArea-body-right {
            text-align: right;
        }

        .row {
            margin: 0px 0px;
        }

        .subject-name {
            font-size: 20px;
        }

        .estimateInventoryDataBlue {
            background-color: #3581ee;
        }

        #enumMapByParentCodeInfo > button {
            margin: 5px 6px;
        }

        .line-center {
            display: inline-block;
            background-color: blue;
            border-radius: 50%;
            height: 30px;
            width: 30px;
            color: white;
            text-align: center;
            line-height: 30px;
        }

        .icon-plus {
            font-size: 30px;
            line-height: 30px;
        }

        .icon-plus:hover {
            cursor: pointer;
        }

        .select2-selection {
            background-color: #fff !important;
            padding: 6px 12px !important;
            height: 32px !important;
            font-size: 14px !important;
            line-height: 1.42857 !important;
            color: #4d6b8a !important;
        }

    </style>

    <script type="text/javascript">
        $(function () {

            $("#supplementary").hide();

            $(document).on("change","#isRegister",function(){
                let isRegister = $(this).val();
                console.log(isRegister);
                if(isRegister == 1){
                    $("div").find("#supplementary").show();
                    $("div").find("#distribution").hide();
                }else {
                    $("div").find("#supplementary").hide();
                    $("div").find("#distribution").show();
                }
                let index = parent.layer.getFrameIndex(window.name);    //获取窗口索引
                parent.layer.iframeAuto(index);                         //layer.open 高度自适应
                window.parent.iframeH();
            });


        })
        function push() {

            var bindInfo = [];

            $.each($("tr[name='roleInfo']"), function (index, obj) {
                // 估损id
                let objectId = $(this).attr("code");
                // 分配人员id
                let auditerId = $.trim($(this).find("select[name='auditerId']").val());

                if (auditerId) {
                    bindInfo.push({
                        "objectId": objectId,
                        "auditerId": auditerId
                    })
                }

            });

            if (bindInfo.length  != $("tr[name='roleInfo']").size() ) {
                layer.msg("请选择分配人员", {icon: 2, time: 1500});
                return;
            }

            // // 审核人员id
            // var auditerId = $("#auditerId").val()
            // // 估损表id
            // var objectId = $("input[name='objectId']:checked").val();
            // console.log(auditerId);
            // console.log(objectId);
            // if (!auditerId) {
            //     layer.msg("请选择分配人员", {icon: 2, time: 1500});
            //     return;
            // }
            // if (!objectId) {
            //     layer.msg("请选择估损信息", {icon: 2, time: 1500});
            //     return;
            // }
            layer.confirm("请再次确认是否分配完成？", {icon: 3}, function (index) {
                    var formData = new FormData();
                    formData.append("bindInfo", JSON.stringify(bindInfo));
                    $.ajax({
                        url: "${ctx}/claimCaseDistributionController/bindAuditer",
                        type: 'POST',
                        data: formData,
                        async: true,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                layer.msg(result.msg, {icon: 1, time: 2000}, function(index) {
                                    parent.window.location.reload();
                                    layer.close(index);
                                });
                            } else {
                                layer.msg(result.msg, {
                                    icon: 2,
                                    time: 1500
                                });
                            }
                        },
                        error: function (data) {
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    });
                }
            );
        }
        function save() {
            let status =$("#isRegister").val();
            var layerTop = top.layer;
            if(status==0){
                console.log("status"+status);
                push();
            }else{
                var checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
                var values = [];
                checkboxes.forEach(function(checkbox) {
                    if (checkbox.checked) {
                        var code = checkbox.getAttribute('code');
                        values.push(code);
                    }
                });
                console.log(values.join(','));
                let missingAttachments=values.join(',');
                console.log("missingAttachments:"+missingAttachments);
                if(missingAttachments==""){
                    layerTop.msg("请选择补充材料", {icon: 5, time: 1000 ,id: "LAYER_MSG"});
                    return;
                }
                let objectData = {
                    claimCaseObjectId:"${objectId}",
                    missingAttachments:missingAttachments,
                    remark:$("div").find("#supplementaryReason").val()
                };
                $.ajax({
                    url: "${ctx}/claimCaseObjectController/claimCaseObjectAttachRefuse",
                    type: 'POST',
                    data: JSON.stringify(objectData),
                    async: false,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            layer.msg(result.msg, {icon: 1, time: 1500}, function(index) {
                                parent.location.reload();
                                layer.close(index);
                            });
                        } else {
                            layer.msg(result.msg, {icon: 2, time: 1500}, function(index) {
                                layer.close(index);
                            });
                        }
                    },
                    error: function (data) {
                        alert(data);
                    }
                });
            }
        }


    </script>

</head>


<body id="qc-Body" style="overflow-x:hidden;background: #fff;">
<div class="container-fluid">
    <div class="row">
        <div id="inputForm" onsubmit="return false">
            <div class="row" style="display: flex;justify-content: space-evenly;">
                <!-- 左部分 -->
                <div class="col-sm-12" id="object" style="background: #F4F4F4;padding: 2%;margin-bottom: 1%;">

                    <div class="row" style="margin-bottom: 20px;" >
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px">审核是否通过</div>
                            <div class="col-sm-8">
                                <select class="form-control" name="isRegister" id="isRegister" value="">
                                    <option value="0" >通过</option>
                                    <option value="1" >未通过</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-9 col-sm-offset-1" style="margin-bottom: 20px;" id="supplementary">
                            <div class="col-sm-3" style="text-align: right;line-height: 34px">选择补充材料</div>
                            <div class="col-sm-9">
                                <#if supplementaryMaterials?? && (supplementaryMaterials.keySet()?size>0)>
                                    <#list supplementaryMaterials.keySet() as key>
                                    <div class="col-sm-6" style="padding-left: 0px;margin-bottom: 10px;display: flex">
                                        <input name="labelType" style="left: 0px;right: 0px;margin-left: 0px;" code="${key}"
                                               type="checkbox">${supplementaryMaterials.get(key)}
                                    </div>
                                </#list>
                                <textarea class="form-control" id="supplementaryReason" rows="5" placeholder="请输入补材原因"></textarea>
                                </#if>
                            </div>
                        </div>
                         <div class="" style="margin-bottom: 20px;" id="distribution">
                             <div class="col-sm-12" style="margin-bottom: 20px;padding-left: 0px;padding-right: 0px;">
                                 <div class="col-sm-6" style="padding: 0px;">
                                     <div class="col-sm-12">
                                         案件号：${claimCase.claimCaseNo}
                                     </div>
                                 </div>

                                 <div class="col-sm-4" style="padding: 0px;">
                                     <div class="col-sm-12" style="padding: 0px;">
                                         当前状态：${claimCaseStatusEumMap.get(claimCase.status).msg}
                                     </div>
                                 </div>

                             </div>

                             <div class="col-sm-12" style="margin-bottom: 20px;padding-left: 0px;padding-right: 0px;">
                                 <div class="col-sm-12" style="margin-bottom: 10px;">
                                     人伤处理岗(任务数)：
                                 </div>
                                 <#if rsAuditerMap?? && (rsAuditerMap.keySet()?size>0)>
                                     <#list rsAuditerMap.keySet() as key>
                                         <div class="col-sm-3 clear-padding" style="margin-bottom: 10px;">
                                             ${rsAuditerMap.get(key)}
                                         </div>
                                     </#list>
                                 </#if>
                            </div>

                            <div class="col-sm-12" style="margin-bottom: 20px;padding-left: 0px;padding-right: 0px;">
                                <div class="col-sm-12" style="margin-bottom: 10px;">
                                    物损处理岗(任务数)：
                                </div>
                                <#if wsAuditerMap?? && (wsAuditerMap.keySet()?size>0)>
                                    <#list wsAuditerMap.keySet() as key>
                                        <div class="col-sm-3 clear-padding" style="margin-bottom: 10px;">
                                            ${wsAuditerMap.get(key)}
                                        </div>
                                    </#list>
                                </#if>
                             </div>

                            <div class="col-sm-15">
                                <table class="table">
                                    <thead>
                                    <tr>

                                        <th width="25%">类型</th>
                                        <th width="20%">赔付对象名称</th>
                                        <th width="20%">手机号</th>
                                        <th width="15%">预估金额</th>
                                        <th width="30%">操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <#if claimCaseObjectList??>
                                    <#list claimCaseObjectList as claimCaseObject>
                                    <tr class="rowInfo" name="roleInfo" code="${claimCaseObject.id}">

                                        <td width="25%">${applyTypeNewEnumMap.get(claimCaseObject.type + '-'+ claimCaseObject.category).msg}
                                            <#if claimCaseObject.insCode == "GY" >
                                                (国元)
                                            <#elseif claimCaseObject.insCode == "DD">
                                                (大地)
                                            <#elseif claimCaseObject.insCode == "RB">
                                                (人保)
                                            </#if>
                                        </td>
                                        <td width="20%">${claimCaseObject.name}</td>
                                        <td width="20%">${claimCaseObject.mobile}</td>
                                        <td width="15%">${claimCaseObject.estimatedApprovedMoney}</td>
                                        <td width="30%">
                                            <#if claimCaseObject.category == 1 >

                                            <select class="form-control select2" name="auditerId" >
                                                <option value="">请选择</option>
                                                <#if rsAuditerMap?? && (rsAuditerMap.keySet()?size>0) >
                                                <#list rsAuditerMap.keySet() as key>
                                                <option value="${key}">${rsAuditerMap.get(key)}</option>
                                            </#list>
                                        </#if>
                                        </select>

                                        <#else>
                                        <select class="form-control select2" name="auditerId" >
                                            <option value="">请选择</option>
                                            <#if wsAuditerMap?? && (wsAuditerMap.keySet()?size>0) >
                                            <#list wsAuditerMap.keySet() as key>
                                            <option value="${key}">${wsAuditerMap.get(key)}</option>
                                        </#list>
                                    </#if>
                                    </select>
                                    </#if>
                                    </td>
                                    </tr>

                                </#list>
                            </#if>
                            </tbody>
                            </table>
                        </div>

                         </div>


                    </div>
                </div>




            </div>
            <div class="row" style="text-align: center">
                <div class="col-sm-6">
                    <button class="btn btn-primary" onclick="save()" style="padding:5px 20px;font-size: 20px;">确定</button>
                </div>
                <div class="col-sm-6">
                    <button class="btn btn-primary" onclick="parent.layer.closeAll();" style="padding:5px 20px;font-size: 20px;">取消</button>
                </div>
            </div>
        </form>
    </div>
</div>

</body>
</html>
