<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        const loader =  new Loaders({style:"rectangle"});
        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        $(document).ready(function () {

            // 业务标签 select2初始化
            $("#type-category").select2({
                placeholder: "请选择",
                width: null
            });
            // 保司标签 select2初始化
            $("#insCode").select2({
                placeholder: "请选择",
                width: null
            });
            // 标签 select2初始化
            $("#label").select2({
                placeholder: "请选择",
                width: null
            });

            $("body").on("change", "select[name='type-category']", function() {
                var value = $(this).val();
                $("input[name='type']").val(value.split("-")[0]);
                $("input[name='category']").val(value.split("-")[1]);
            });


        });

        
        function statusSwitch(status) {
            $("#status").val(status);
            $("#searchForm").submit();
        }

        function startTask(id, isLossAssessmentNew, status) {
            if (isLossAssessmentNew != null && isLossAssessmentNew === "1") {
                if (status.startsWith("BAX3")) {
                    window.open("${ctx}/claimCaseObjectController/startTask?comeFrom=${claimCaseObjectVo.comeFrom}&id="+id);
                } else {
                    window.open("${ctx}/claimCaseObjectController/startMeasureLossAssessment4Car?comeFrom=${claimCaseObjectVo.comeFrom}&id="+id);
                }
            } else {
                window.location.href = "${ctx}/claimCaseObjectController/startTask?comeFrom=${claimCaseObjectVo.comeFrom}&id="+id;
            }

        }
        function showObject(id) {
            window.location.href = "${ctx}/claimCaseObjectController/showObject?id="+id;
        }
        function showCase(claimCaseId) {
            window.open("${ctx}/claimCaseController/caseDetail?caseId="+claimCaseId);
        }

        function startSignTask(id) {
            window.location.href = "${ctx}/claimCaseObjectController/startSignTask?claimCaseObjectId=" + id;
        }

        //完成签字环节，进入到签章环节
        function endSignTask(id) {
            if(!id){
                layer.msg("赔付对象信息缺失，请刷新后重试！！", {
                    icon: 1,
                    time: 2000,//1秒关闭（如果不配置，默认是3秒）
                }, function (index1) {
                    layer.close(index1);
                });
                return;
            }
            layer.confirm("是否确认完成？", {icon: 3, title: '温馨提示', offset: 'auto'}, function (index) {
                let formData = new FormData();
                formData.append("claimCaseObjectId",id)
                $.ajax({
                    url: "${ctx}/claimCaseObjectController/endSignTask",
                    type: 'POST',
                    data: formData,
                    async: false,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            layer.msg(result.msg, {icon: 1, time: 1500, offset: 'auto'}, function (index) {
                                <#--window.location.href = "${ctx}/claimCaseObjectController/claimCaseObjectList";-->
                                layer.close(index);
                            });
                        } else {
                            layer.msg(result.msg, {icon: 2, time: 1500, offset: 'auto'}, function (index) {
                                layer.close(index);
                            });
                        }
                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                });
            });
        }

        //查看签字推送详细
        function showSign(id) {
            if(!id){
                layer.msg("赔付对象信息缺失，请刷新后重试！！", {
                    icon: 1,
                    time: 2000,//1秒关闭（如果不配置，默认是3秒）
                }, function (index1) {
                    layer.close(index1);
                });
                return;
            }
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height()/5 - 20 > 120 ? 120 : $(window).height()/5 - 20) + "px";

            layer.open({
                type: 2,
                area: openWindowWidth,
                offset : offsetH,
                fix: false, //不固定
                maxmin: true,
                content: '${ctx}/claimCaseObjectController/detailSignPushLog?claimCaseObjectId='+id,
                success: function(layero, index){
                    layer.iframeAuto(index);
                }
            });
        }

        /**
         * 获取签署链接
         * @param id
         */
        function getSigningLink(id) {
            if(!id){
                layer.msg("赔付对象信息缺失，请刷新后重试！！", {
                    icon: 1,
                    time: 2000,//1秒关闭（如果不配置，默认是3秒）
                }, function (index1) {
                    layer.close(index1);
                });
                return;
            }
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height()/5 - 20 > 120 ? 120 : $(window).height()/5 - 20) + "px";

            layer.open({
                type: 2,
                area: openWindowWidth,
                offset : offsetH,
                fix: false, //不固定
                maxmin: true,
                content: '${ctx}/claimCaseObjectController/getSigningLink?claimCaseObjectId='+id,
                success: function(layero, index){
                    layer.iframeAuto(index);
                }
            });
        }

    </script>
    <style>

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
            border-right: 0;
            overflow: hidden;
            vertical-align: bottom;
            padding: 0px;
            display: inline-block;
        }
        .form-active > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
        }
        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 70px 6px 30px;
            font-size: 17px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
            border-right: 1px solid #e7ecf1;
        }
        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }
        td > a {
            display: inline-block;
            margin: 3px;
        }

        .li-blue {
            background: #0b94ea ;
            color: #fff;
        }

        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }
    </style>
</head>
<body>

<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>新职业平台 </span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">我的任务</span></li>
                </ul>
            </div>
            <div class="portlet-body">

                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal" action="${ctx}/claimCaseObjectController/claimCaseObjectList"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="claimCaseNo" id="claimCaseNo"
                                               value="${claimCaseObjectVo.claimCaseNo}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">立案号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="insuranceCaseNo" id="insuranceCaseNo"
                                               value="${claimCaseObjectVo.insuranceCaseNo}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人姓名：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatName" id="treatName"
                                               value="${claimCaseObjectVo.treatName}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人身份证：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatIdNum" id="treatIdNum"
                                               value="${claimCaseObjectVo.treatIdNum}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">估损单类型：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <select class="form-control" name="type-category" id="type-category" >
                                            <option value=" ">请选择</option>
                                            <#if typeCategoryMap??>
                                                <#list typeCategoryMap.keySet() as key>
                                                    <option value="${key}" <#if key == claimCaseObjectVo.type + "-" + claimCaseObjectVo.category> selected </#if>>${typeCategoryMap.get(key)}</option>
                                                </#list>
                                            </#if>
                                        </select>
                                        <input type="hidden" name="type" value="${claimCaseObjectVo.type}">
                                        <input type="hidden" name="category" value="${claimCaseObjectVo.category}">
                                    </div>
                                </div>
                            </div>
                            <@shiro.hasPermission name="CLAIM_SHOW_INS_SELECT">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="control-label col-sm-3" style="padding-right: 0">保司标识：</label>
                                        <div class="col-sm-8" style="padding-left: 0;">
                                            <select class="form-control" name="insCode" id="insCode" >
                                                <option value=" ">请选择</option>
                                                <#if insCodeMap?exists>
                                                    <#list insCodeMap.keySet() as key>
                                                        <option value="${key}" <#if claimCaseObjectVo.insCode==key>selected</#if>>${insCodeMap.get(key)}</option>
                                                    </#list>
                                                </#if>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </@shiro.hasPermission>
                            <!--                            <div class="col-sm-4">-->
                            <!--                                <div class="form-group">-->
                            <!--                                    <label class="control-label col-sm-3" style="padding-right: 0">标签：</label>-->
                            <!--                                    <div class="col-sm-8" style="padding-left: 0;">-->
                            <!--                                        <select class="form-control" name="label" id="label" >-->
                            <!--                                            <option value=" ">请选择</option>-->
                            <!--                                            <#if labelShowMap?exists>-->
                            <!--                                            <#list labelShowMap.keySet() as key>-->
                            <!--                                            <option value="${key}" <#if claimCaseObjectVo.label==key>selected</#if>>${labelShowMap.get(key).msg}</option>-->
                            <!--                                            </#list>-->
                            <!--                                            </#if>-->
                            <!--                                        </select>-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                            </div>-->
                        </div>
                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group pull-right" style="margin-bottom: 10px;">
                                    <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;margin-right: 20px;">查询</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-active">
                        <div>
                            <input type="hidden" name="status" id="status" value="${claimCaseObjectVo.status}">
                            <input type="hidden" name="comeFrom" id="comeFrom" value="${claimCaseObjectVo.comeFrom}">
                            <ul style="padding: 0px;margin: 0px;display: flex;">
                                <#if claimCaseObjectVo.comeFrom == 1>
                                    <li class="li-default <#if claimCaseObjectVo.status == "">li-blue</#if>" onclick="statusSwitch()">全部</li>
                                    <li class="li-default <#if claimCaseObjectVo.status == 0>li-blue</#if>" onclick="statusSwitch(0)">估损待处理</li>
                                    <li class="li-default <#if claimCaseObjectVo.status == 1>li-blue</#if>" onclick="statusSwitch(1)">理算待处理</li>
                                    <li class="li-default <#if claimCaseObjectVo.status == 2>li-blue</#if>" onclick="statusSwitch(2)">退回任务</li>
                                    <li class="li-default <#if claimCaseObjectVo.status == 7>li-blue</#if>" onclick="statusSwitch(7)">签字任务</li>
                                </#if>
                                <#if claimCaseObjectVo.comeFrom == 2 >
                                    <li class="li-default <#if claimCaseObjectVo.status == 5>li-blue</#if>" onclick="statusSwitch(5)">全部估损审核</li>
                                    <li class="li-default <#if claimCaseObjectVo.status == 3>li-blue</#if>" onclick="statusSwitch(3)">我的估损审核</li>
                                    <li class="li-default <#if claimCaseObjectVo.status == 4>li-blue</#if>" onclick="statusSwitch(4)">我的理算审核</li>
                                    <li class="li-default <#if claimCaseObjectVo.status == 6>li-blue</#if>" onclick="statusSwitch(6)">全部理算审核</li>
                                </#if>
                                <#if claimCaseObjectVo.comeFrom == 3>
                                    <li class="li-default <#if claimCaseObjectVo.status == 3>li-blue</#if>" onclick="statusSwitch(3)">采集审核(个人池)</li>
                                    <li class="li-default <#if claimCaseObjectVo.status == 4>li-blue</#if>" onclick="statusSwitch(4)">理算审核(个人池)</li>
                                    <li class="li-default <#if claimCaseObjectVo.status == 5>li-blue</#if>" onclick="statusSwitch(5)">采集审核(公共池)</li>
                                    <li class="li-default <#if claimCaseObjectVo.status == 6>li-blue</#if>" onclick="statusSwitch(6)">理算审核(公共池)</li>
                                </#if>
                            </ul>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <#if claimCaseObjectVo.status==7>
                        <thead>
                        <tr>
                            <th width="10%">报案号</th>
                            <th width="10%">立案号</th>
                            <th width="8%">出险人姓名</th>
                            <th width="15%">出险人身份证</th>
                            <th width="8%">估损单类型</th>
                            <th width="10%">签字状态</th>
                            <th width="10%">创建时间</th>
                            <th width="20%">功能</th>
                        </tr>
                        </thead>
                        <tbody>
                        <#list page.list as vo>
                            <tr>
                                <td title="${vo.claimCaseNo}">${vo.claimCaseNo}</td>
                                <td title="${vo.insuranceCaseNo}">${vo.insuranceCaseNo}</td>
                                <#if vo.insCode == 'JZTB'>
                                    <td title="${vo.name}">${vo.name}</td>
                                <#else>
                                    <td title="${vo.treatName}">${vo.treatName}</td>
                                </#if>

                                <td title="">${vo.treatIdNum}</td>
                                <td>
                                    <#switch vo.type + "-" + vo.category>
                                        <#case "1-1">
                                            骑手人伤
                                            <#break >
                                        <#case "2-1">
                                            三者人伤
                                            <#break >
                                        <#case "2-2">
                                            三者物损
                                            <#break >
                                        <#case "2-3">
                                            三者车损
                                            <#break >
                                    </#switch>
                                </td>
                                <td>
                                    <#if vo.isCaseClosed != 1>
                                        <#if (vo.status == 'BAX39') && (vo.sumSignPush??) && (vo.sumSignPush==vo.successSignPush)>
                                            签字完成
                                        <#else >
                                            ${statusMap.get(vo.status)}
                                        </#if>


                                        <#if vo.sumSignPush??>${vo.successSignPush!'0'}/${vo.sumSignPush}</#if>
                                    <#else >
                                        案件已关闭
                                    </#if>
                                </td>

                                <td>${vo.createTime?string('yyyy-MM-dd HH:mm:ss')}</td>
                                <td>
                                    <#if vo.isCaseClosed != 1>
                                    <#--签字待发起状态-->
                                        <#if  vo.status == "BAX38">
                                            <a href="#" onclick="startSignTask('${vo.id}')">开始任务</a>
                                        </#if>
                                    <#--签字待返回状态-->
                                        <a href="#" onclick="endSignTask('${vo.id}')">完成</a>
                                    </#if>
                                    <a href="#" onclick="showCase('${vo.claimCaseId}')">查看案件</a>
                                    <#if !vo.isNewCase?? && vo.isNewCase != "1" >
                                    <a href="#" onclick="showObject('${vo.id}')">查看详情</a>
                                    </#if>
                                    <a href="#" onclick="showSign('${vo.id}')">查看签字详情</a>
                                    <#if vo.status == "BAX39">
                                        <a href="#" onclick="getSigningLink('${vo.id}')">获取签署链接</a>
                                    </#if>
                                </td>
                            </tr>
                        </#list>
                        </tbody>
                    </#if>

                    <#if claimCaseObjectVo.status!=7>
                    <thead>
                    <tr>
                        <th width="9%">报案号</th>
                        <th width="9%">立案号</th>
                        <th width="8%">出险人姓名</th>
                        <#if claimCaseObjectVo.comeFrom == 3>
                        <th width="15%">出险人身份证</th>
                        </#if>
                        <#if claimCaseObjectVo.comeFrom != 3>
                        <th width="9%">报案时间</th>
                        <th width="9%">出险时间</th>
                        <th width="5%">承保公司</th>
                        <th width="8%">标签</th>
                        <th width="8%">估损金额</th>
                        </#if>
                        <th width="8%">赔付对象类型</th>
                        <th width="10%">状态</th>
                        <th width="10%">创建时间</th>
                        <th width="10%">功能</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as vo>
                        <tr>
                            <td title="">${vo.claimCaseNo}</td>
                            <td title="">${vo.insuranceCaseNo}</td>
                            <td title="">${vo.treatName}</td>
                            <#if claimCaseObjectVo.comeFrom == 3>
                            <td title="">${vo.treatIdNum}</td>
                            </#if>
                            <#if claimCaseObjectVo.comeFrom != 3>
                                <td title="${(vo.startDate?string["yyyy-MM-dd HH:mm:ss"])!'-'}">${(vo.startDate?string["yyyy-MM-dd HH:mm:ss"])!'-'}</td>
                                <td title="${(vo.treatDate?string["yyyy-MM-dd HH:mm:ss"])!'-'}">${(vo.treatDate?string["yyyy-MM-dd HH:mm:ss"])!'-'}</td>
                                <td>
                                    <#if vo.insCode == 'RB' || vo.insCode == 'HMRB'>
                                        <span style="display:inline-block;background-color: #dbaf00;color: #fff;padding: 2px 6px;border-radius: 2px;">人保</span>
                                    <#elseif vo.insCode == 'GY' || (vo.insCode=="DD" && vo.province == '辽宁省' && vo.treatDate?datetime gte "2023-09-29 00:00:00"?datetime  &&vo.treatDate?datetime lte "2024-01-01 00:00:00"?datetime) >
                                        <span style="display:inline-block;background-color: #067ee5;color: #fff;padding: 2px 6px;border-radius: 2px;">国元</span>
                                    <#elseif vo.insCode == 'DD'>
                                        <span style="display:inline-block;background-color: #26c281;color: #fff;padding: 2px 6px;border-radius: 2px;">大地</span>
                                    <#elseif vo.insCode == 'HX'>
                                        <span style="display:inline-block;background-color: #ee5f5b;color: #fff;padding: 2px 6px;border-radius: 2px;">海峡</span>
                                    <#elseif vo.insCode == 'JZTB' || vo.insCode='TBFZ'>
                                        <span style="display:inline-block;background-color: #f78c45;color: #fff;padding: 2px 6px;border-radius: 2px;">太保</span>
                                     </#if>
                                </td>
                                <td class="labelGroup">
                                    <#if vo.label??>
                                        <#list vo.label.split(",") as key>
                                            <#if (key?trim)!="">
                                                <span class="${key} span-type" style="color: ${labelShowMap.get(key).fontColor};background-color: ${labelShowMap.get(key).color}"><#if labelShowMap.get(key)??>${labelShowMap.get(key).msg}<#else>${key}</#if></span>
                                            </#if>
                                        </#list>
                                    </#if>
                                </td>
                                <td title="${(vo.estimatedApprovedMoney?string(',##0.00'))!''}">${(vo.estimatedApprovedMoney?string(',##0.00'))!''}</td>
                            </#if>
                            <td>
                                <#switch vo.type + "-" + vo.category>
                                    <#case "1-1">
                                        骑手人伤
                                        <#break >
                                    <#case "2-1">
                                        三者人伤
                                        <#break >
                                    <#case "2-2">
                                        三者物损
                                        <#break >
                                    <#case "2-3">
                                        三者车损
                                        <#break >
                                </#switch>
                            </td>
                            <td>
                                 <#if vo.isCaseClosed != 1>
                                    ${statusMap.get(vo.status)}
                                 <#else >
                                     案件已关闭
                                 </#if>
                            </td>

                            <td>${vo.createTime?string('yyyy-MM-dd HH:mm:ss')}</td>
                            <td>
                                <#if vo.isCaseClosed != 1>
                                    <#if claimCaseObjectVo.comeFrom == 1 && (vo.status == "BAX21" || vo.status == "BAX24" || vo.status == "BAX27" || vo.status == "BAX31" || vo.status == "BAX34" || vo.status == "BAX37")>
                                        <a href="#" onclick="startTask('${vo.id}', '${vo.isLossAssessmentNew}', '${vo.status}')">开始任务</a>
                                    </#if>
                                    <#if claimCaseObjectVo.comeFrom == 2 && (vo.status == "BAX22" || vo.status == "BAX32")>
                                        <a href="#" onclick="startTask('${vo.id}', '${vo.isLossAssessmentNew}', '${vo.status}')">内部审核</a>
                                    </#if>
                                    <#if claimCaseObjectVo.comeFrom == 3 && (vo.status == "BAX25" || vo.status == "BAX35")>
                                        <a href="#" onclick="startTask('${vo.id}', '${vo.isLossAssessmentNew}', '${vo.status}')">保司审核</a>
                                    </#if>
                                </#if>
                                <!--车损无需查看按钮-->
                                <#if (vo.isLossAssessmentNew?? && vo.isLossAssessmentNew == "1") || ((!vo.isLossAssessmentNew?? || vo.isLossAssessmentNew != "1") && vo.isNewCase?? && vo.isNewCase == "1")>
                                <#else>
                                    <a href="#" onclick="showObject('${vo.id}')">查看</a>
                                </#if>
                                <a href="#" onclick="showCase('${vo.claimCaseId}')">查看案件</a>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                    </#if>


                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>