<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        const loader = new Loaders({style: "rectangle"});

        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        function iframeH() {
            var ifm = parent.document.getElementById("contentFrame");
            if (ifm) {
                var ifmH = $(ifm).height()
                var h = $(document.body).height() + 120;
                if (ifmH < h) {
                    parent.document.getElementById("contentFrame").height = h;
                }
            }
        }

        $(document).ready(function () {

            iframeH(); // 计算页面高度

            // 业务标签 select2初始化
            $("#type-category").select2({
                placeholder: "请选择",
                width: null
            });
            // 保司标签 select2初始化
            $("#insCodeSelect").select2({
                placeholder: "请选择",
                width: null
            });

            $("body").on("change", "select[name='type-category']", function () {
                var value = $(this).val();
                $("input[name='type']").val(value.split("-")[0]);
                $("input[name='category']").val(value.split("-")[1]);
            });

        });


        function statusSwitch(status,stopFlag) {
            $("#status").val(status);
            $("#stopFlag").val(stopFlag);
            $("#searchForm").submit();
        }

        function startTask(id) {
            window.location.href = "${ctx}/claimCaseObjectController/startTask4BS?comeFrom=${claimCaseObjectVo.comeFrom}&checkCondition=${claimCaseObjectVo.checkCondition}&id=" + id;
        }

        function startTaskAdjustment4BS(id) {
            $.ajax({
                url: "${ctx}/claimCaseObjectController/checkIsNewCaseProcess",
                type: 'POST',
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        window.open("${ctx}/claimCaseObjectController/startTaskAdjustment4BS?comeFrom=${claimCaseObjectVo.comeFrom}&checkCondition=${claimCaseObjectVo.checkCondition}&id=" + id);
                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 1500
                        });
                    }
                },
                error: function (data) {
                    console.log(data);
                }
            });
        }

        //车损新流程
        function startTaskCar(id) {
            $.ajax({
                url: "${ctx}/claimCaseObjectController/checkIsNewCaseProcess",
                type: 'POST',
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        window.open("${ctx}/claimCaseObjectController/startTaskCarV2?comeFrom=${claimCaseObjectVo.comeFrom}&checkCondition=${claimCaseObjectVo.checkCondition}&id=" + id);
                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 1500
                        });
                    }
                },
                error: function (data) {
                    console.log(data);
                }
            });

        }

        function showObject(id) {
            window.location.href = "${ctx}/claimCaseObjectController/showObject4BS?id=" + id;
        }
        function showCase(claimCaseId) {
            window.open("${ctx}/claimCaseController/insuranceCaseDetail4BS?caseId="+claimCaseId);
        }

        function showStopTaskModal(id) {
            $('#taskId').val(id);
            $('#confirmationModal').fadeIn();
            $('body').css('overflow', 'hidden');
        }

        function confirmStopTask() {
            var id = $('#taskId').val();
            $.ajax({
                url: "${ctx}/claimCaseObjectController/stopTask?id=" + id,
                type: 'GET',
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layer.msg(result.msg, {
                            icon: 1,
                            time: 1500
                        }, function () {
                            statusSwitch(3,true);
                        });
                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 1500
                        });
                    }
                    $('#confirmationModal').fadeOut();
                    $('body').css('overflow', 'auto');
                },
                error: function (data) {
                    console.log(data);
                }
            });
        }

        function cancelStopTask() {
            $('#confirmationModal').fadeOut();
            $('body').css('overflow', 'auto');
        }
    </script>
    <style>

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
            border-right: 0;
            overflow: hidden;
            vertical-align: bottom;
            padding: 0px;
            display: inline-block;
        }

        .form-active > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
        }

        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 70px 6px 30px;
            font-size: 17px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
            border-right: 1px solid #e7ecf1;
        }

        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }

        td > a {
            display: inline-block;
            margin: 3px;
        }

        .li-blue {
            background: #0b94ea;
            color: #fff;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 9999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            overflow: auto;
            padding-top: 60px;
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 40%;
            border-radius: 5px;
            text-align: center;
        }

        .close-btn {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            position: absolute;
            top: -5px;
            right: 20px;
        }

        .close-btn:hover,
        .close-btn:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

        .warning-icon {
            width: 40px;
            height: 40px;
            margin-bottom: 20px;
        }

        .modal-message {
            font-size: 16px;
            color: #333;
            margin: 20px 0;
        }

        .modal-footer {
            display: flex;
            justify-content: center;
            gap: 20px;
        }

        .cancel-btn,
        .confirm-btn {
            padding: 10px 20px;
            font-size: 16px;
            border: none;
            cursor: pointer;
            border-radius: 5px;
        }

        .cancel-btn {
            background-color: #f44336;
            color: white;
        }

        .confirm-btn {
            background-color: #4CAF50;
            color: white;
        }

        .cancel-btn:hover {
            background-color: #e53935;
        }

        .confirm-btn:hover {
            background-color: #388e3c;
        }
    </style>
</head>
<body>
<div id="confirmationModal" class="modal">
    <div class="modal-content">
        <span class="close-btn" onclick="cancelStopTask()">&times;</span>
        <div class="modal-body">
            <#if claimCaseObjectVo.status == 3>
                <p class="modal-message">确认放弃当前任务，放弃后任务将退回全部复核待审核任务池</p>
            <#else>
                <p class="modal-message">确认放弃当前任务，放弃后任务将退回全部理算待审核任务池</p>
            </#if>
        </div>
        <div class="modal-footer">
            <button class="cancel-btn" onclick="cancelStopTask()">取消</button>
            <button class="confirm-btn" onclick="confirmStopTask()">放弃</button>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>新职业平台 </span> <i class="fa fa-circle"></i></li>
                    <li>
                        <span class="active">
                            <#if claimCaseObjectVo.comeFrom == 3>
                                <#if claimCaseObjectVo.insCode?has_content || claimCaseObjectVo.insCode != '' >
                                     ${claimCaseObjectVo.insCode}复核
                                <#else>
                                全案复核
                                </#if>
                            </#if>
                        </span>
                    </li>
                </ul>
            </div>
            <div class="portlet-body">

                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal" action="${ctx}/claimCaseObjectController/claimCaseObjectList4BSV2"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">立案号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="insuranceCaseNo" id="insuranceCaseNo"
                                               value="${claimCaseObjectVo.insuranceCaseNo}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人姓名：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatName" id="treatName"
                                               value="${claimCaseObjectVo.treatName}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人身份证：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatIdNum" id="treatIdNum"
                                               value="${claimCaseObjectVo.treatIdNum}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">估损单类型：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <select class="form-control" name="type-category" id="type-category">
                                            <option value=" ">请选择</option>
                                            <#if typeCategoryMap??>
                                                <#list typeCategoryMap.keySet() as key>
                                                    <option value="${key}" <#if key == claimCaseObjectVo.type + "-" + claimCaseObjectVo.category> selected </#if>>${typeCategoryMap.get(key)}</option>
                                                </#list>
                                            </#if>
                                        </select>
                                        <input type="hidden" name="type" value="${claimCaseObjectVo.type}">
                                        <input type="hidden" name="category" value="${claimCaseObjectVo.category}">
                                    </div>
                                </div>
                            </div>
                            <@shiro.hasPermission name="CLAIM_SHOW_INS_SELECT">
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="control-label col-sm-3" style="padding-right: 0">保险公司：</label>
                                        <div class="col-sm-8" style="padding-left: 0;">
                                            <select class="form-control" name="insCodeSelect"
                                                    id="insCodeSelect" onchange="javascript:$('#insCode').val($(this).val());">
                                                <option value=" " selected>请选择</option>
                                                <#if insCodeMap?exists>
                                                    <#list insCodeMap.keySet() as key>
                                                        <option value="${key}" <#if claimCaseObjectVo.insCode == key>selected</#if>>
                                                            ${insCodeMap.get(key)}
                                                        </option>
                                                    </#list>
                                                </#if>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            </@shiro.hasPermission>
                            <input type="hidden" name="insCode" id="insCode" value="${claimCaseObjectVo.insCode}">
                        </div>
                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group pull-right" style="margin-bottom: 10px;">
                                    <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;margin-right: 20px;">查询</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-active">
                        <div>
                            <input type="hidden" name="stopFlag" id="stopFlag" value="">
                            <input type="hidden" name="status" id="status" value="${claimCaseObjectVo.status}">
                            <input type="hidden" name="comeFrom" id="comeFrom" value="${claimCaseObjectVo.comeFrom}">
                            <input type="hidden" name="checkCondition" id="checkCondition" value="${claimCaseObjectVo.checkCondition}">
                            <ul style="padding: 0px;margin: 0px;display: flex;">
                                <#if claimCaseObjectVo.comeFrom == 3 >
                                    <@shiro.hasPermission name="INS_POOL_BAX2X_PUBLIC_SHOW">
                                        <li class="li-default <#if claimCaseObjectVo.status == 5>li-blue</#if>" onclick="statusSwitch(5, false)">全部复核待审核</li>
                                    </@shiro.hasPermission>
                                    <@shiro.hasPermission name="INS_POOL_BAX2X_PRIVATE_SHOW">
                                        <li class="li-default <#if claimCaseObjectVo.status == 3>li-blue</#if>" onclick="statusSwitch(3, false)">我的复核待审核</li>
                                    </@shiro.hasPermission>
                                    <@shiro.hasPermission name="INS_POOL_BAX3X_PUBLIC_SHOW">
                                        <li class="li-default <#if claimCaseObjectVo.status == 6>li-blue</#if>" onclick="statusSwitch(6, false)">全部理算待审核</li>
                                    </@shiro.hasPermission>
                                    <@shiro.hasPermission name="INS_POOL_BAX3X_PRIVATE_SHOW">
                                        <li class="li-default <#if claimCaseObjectVo.status == 4>li-blue</#if>" onclick="statusSwitch(4, false)">我的理算待审核</li>
                                    </@shiro.hasPermission>
                                </#if>
                            </ul>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th width="10%">立案号</th>
                        <th width="10%">报案号</th>
                        <th width="10%">出险人姓名</th>
                        <th width="10%">出险人身份证</th>
                        <th width="10%">承保公司</th>
                        <th width="10%">赔付对象类型</th>
                        <th width="10%">定损/维修总金额</th>
                        <th width="10%">处理岗人员</th>
<#--                        <th width="10%">状态</th>-->
                        <th width="10%">创建时间</th>
                        <th width="15%">功能</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as vo>
                        <tr>
                            <td title="${vo.insuranceCaseNo}">${vo.insuranceCaseNo}</td>
                            <td title="${vo.claimCaseNo}">${vo.claimCaseNo}</td>
                            <td title="${vo.treatName}">${vo.treatName}</td>
                            <td title="${vo.treatIdNum}">${vo.treatIdNum}</td>
                            <td>
                                <#if vo.insCode == 'RB' || vo.insCode == 'HMRB'>
                                    <span style="display:inline-block;background-color: #dbaf00;color: #fff;padding: 2px 6px;border-radius: 2px;">人保</span>
                                <#elseif vo.insCode == 'GY' || (vo.insCode=="DD" && vo.province == '辽宁省' && vo.treatDate?datetime gte "2023-09-29 00:00:00"?datetime  &&vo.treatDate?datetime lte "2024-01-01 00:00:00"?datetime) >
                                    <span style="display:inline-block;background-color: #067ee5;color: #fff;padding: 2px 6px;border-radius: 2px;">国元</span>
                                <#elseif vo.insCode == 'DD'>
                                    <span style="display:inline-block;background-color: #26c281;color: #fff;padding: 2px 6px;border-radius: 2px;">大地</span>
                                <#elseif vo.insCode == 'HX'>
                                    <span style="display:inline-block;background-color: #ee5f5b;color: #fff;padding: 2px 6px;border-radius: 2px;">海峡</span>
                                <#elseif vo.insCode == 'JZTB' || vo.insCode='TBFZ'>
                                    <span style="display:inline-block;background-color: #f78c45;color: #fff;padding: 2px 6px;border-radius: 2px;">太保</span>
                                </#if>
                            <td>
                                <#switch vo.type + "-" + vo.category>
                                    <#case "1-1">
                                        骑手人伤
                                        <#break >
                                    <#case "2-1">
                                        三者人伤
                                        <#break >
                                    <#case "2-2">
                                        三者物损
                                        <#break >
                                    <#case "2-3">
                                        三者车损
                                        <#break >
                                </#switch>
                            </td>
                            <td title="${(vo.lossAssessmentSum?string(',##0.00'))!''}">
                                ${(vo.lossAssessmentSum?string(',##0.00'))!''}
                            </td>
                            <td title=" <#if vo.auditer??>
                                    ${managerMap.get(vo.auditer)}
                                <#else >
                                    --
                                </#if>">
                                <#if vo.auditer??>
                                    ${managerMap.get(vo.auditer)}
                                <#else >
                                    --
                                </#if>

                            </td>
                           <#-- <td>
                                <#if vo.isCaseClosed != 1>
                                    ${statusMap.get(vo.status)}
                                <#else >
                                    案件已关闭
                                </#if>
                            </td>-->

                            <td>${vo.createTime?string('yyyy-MM-dd HH:mm:ss')}</td>
                            <td>
                                <#if vo.isCaseClosed != 1>
                                    <#if claimCaseObjectVo.comeFrom == 2 && (vo.status == "BAX22" || vo.status == "BAX32")>
                                        <a href="#" onclick="startTask('${vo.id}')">内部审核</a>
                                    </#if>
                                    <#if claimCaseObjectVo.comeFrom == 3 && (vo.status == "BAX25" || vo.status == "BAX35")>

                                        <#if vo.isNewCase?? && vo.isNewCase == "1"  && vo.status == "BAX25" && (vo.type + "-" + vo.category) == '2-3'>
                                        <#--估损保司审核车损调整-->
                                            <a href="#" onclick="startTaskCar('${vo.id}')">保司审核</a>
                                        <#elseif vo.isNewCase?? && vo.isNewCase == "1"  && vo.status == "BAX35" && (vo.type + "-" + vo.category) == '2-3'>
                                        <#--理算保司审核车损调整-->
                                            <a href="#" onclick="startTaskAdjustment4BS('${vo.id}')">保司审核</a>
                                        <#else>
                                            <a href="#" onclick="startTask('${vo.id}')">保司审核</a>
                                        </#if>
                                    </#if>

                                    <#if claimCaseObjectVo.comeFrom == 3 && (claimCaseObjectVo.status == 4 || claimCaseObjectVo.status == 3) && stopTaskMap[vo.insCode]??>
                                        <#if vo.status == "BAX35">
                                            <a href="#" onclick="showStopTaskModal('${vo.id}')">放弃任务</a>
                                            <input id="taskId" type="hidden"/>
                                        </#if>
                                    </#if>

                                </#if>
                                <#if vo.isNewCase?? && vo.isNewCase == "1"  && (vo.type + "-" + vo.category) == '2-3'>
                                    <#--车损从某个时间节点之后就不展示查看按钮了-->
                                <#else >
                                    <a href="#" onclick="showObject('${vo.id}')">查看</a>
                                </#if>
                                <a href="#" onclick="showCase('${vo.claimCaseId}')">查看案件</a>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>