<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>物损详情</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <script src="${ctx}/carousel/dataCollection/viewer.js"></script>
    <script src="${ctx}/carousel/dataCollection/viewermain.js"></script>
    <link href="${ctx}/carousel/dataCollection/viewer.css" rel="stylesheet">
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/metronic/global/scripts/app.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/layout.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/demo.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/jquery-multi-select/js/jquery.multi-select.js"
            type="text/javascript"></script>

    <script src="${ctx}/js/idCardCheck.js" type="text/javascript"></script>
    <style>

        .clear-margin {
            margin: 0px;
        }

        .clear-padding {
            padding: 0px;
        }

        .clear-padding-left {
            padding-left: 0px;
        }

        .clear-padding-right {
            padding-right: 0px;
        }

        .chart-search {
            margin-bottom: 10px;
            display: none;
        }


        .search-channel-content {
            height: 100px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .search-ul-list {
            list-style-type: none;
            height: 20px;
            padding-left: 0px;
            color: black;
        }

        .channel {
            float: left;
            display: block;
        }

        .typeClick {
            height: 112px;
            margin-bottom: 10px;
            border: 2px solid black;
            border-radius: 5px !important;
            margin-left: 20px;
            cursor: pointer
        }

        .typeClickClick {
            height: 112px;
            margin-bottom: 10px;
            border: 2px solid #1676ff;
            background-color: #1676ff;
            color: white;
            border-radius: 5px !important;
            margin-left: 20px;
            cursor: pointer
        }

        .has-error {
            border-color: #ce0d0d !important;
            color: red !important;
        }

        .bckColor {
            background-color: rgba(51, 153, 241, 0.3);
        }

        .imageListChoose {
            /*background-color: #bad6ff !important;*/
            border: 5px solid #055ad7;
        }

        .collectionCompleted {
            background-color: #E8F2FF;
        }

        .collectionButton {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: transparent;
            border-radius: 6px !important;
            color: #1ABB00;
        }

        .collectionButtonClick {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00 !important;
            border-radius: 6px !important;
            color: #FFFFFF;
        }

        .collectionData:hover {
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00;
            color: #FFFFFF;
        }

        .middleTable {
            border: 4px solid #1767ff !important;
        }

        .hangup {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #ce0d0d !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }

        .error {
            color: #ce0d0d !important;
        }

        .txt-over-hide {
            color: red;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: block;
            width: 650px;
        }

        .jump {
            /*float: right;*/
            border-radius: 5px !important;
            color: #3399ff !important;
            background-color: #fff !important;
            border-color: #3399ff;
            height: 40px;
            width: 100%;
        }

        .genTask {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #3399ff !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }

        .thClass {
            text-align: center;
            color: #fff;
        }

        .typeSelect {
            border-color: #3399ff;
            background-color: #3399ff;
        }

        .thumbnail-img {
            width: 50px;
            height: 45px;
            border: 4px solid #D6D6C1;
        }

        .selected-thumbnail-img {
            border: 4px solid #3399ff !important;
        }

        .collection-thumbnail-img {
            border: 4px solid #00B176;
        }

        /*里面的代码可以根据自己需求去进行更改*/
        /* 设置滚动条的样式 */
        ::-webkit-scrollbar {
            width: 5px;
        }

        /* 滚动槽 */
        ::-webkit-scrollbar-track {
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
            border-radius: 10px;
        }

        /* 滚动条滑块 */
        ::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
        }

        ::-webkit-scrollbar-thumb:window-inactive {
            background: rgba(255, 0, 0, 0.4);
        }

        .ticket-information {
            height: 1050px;
            overflow-y: scroll;
        }

        .dataDisplayArea {
            width: 100%;
            height: 19%;
            border-radius: 10px !important;
            /*border: 1px solid #aca8a8;*/
            border: 1px solid #797979;
            cursor: pointer;
            margin-top: 10px;
            overflow: hidden;
        }

        .selected-dataDisplayArea {
            background-color: #91cae9;
        }

        .submit-dataDisplayArea {
            border: 3px solid #44db69 !important;
        }

        .change-dataDisplayArea {
            border: 3px solid red !important;
        }

        .dataDisplayArea > div {
            height: 20%;
            padding: 10px 0px 0px 0px !important;
        }

        .dataDisplayArea-head {
            background-color: #3399ff;
            /*border-radius: 10px 10px 0px 0px !important;*/
        }

        .dataDisplayArea-head-left {
            color: #ffffff;
            font-size: 15px;
            padding: 0px 0px 0px 5px;
        }

        .dataDisplayArea-head-right {
            text-align: right;
            padding: 0px !important;
        }

        .dataDisplayArea-body-left {
            opacity: 0.7;
        }

        .dataDisplayArea-body-right {
            text-align: right;
        }

        .row {
            margin: 0px 0px;
        }

        .subject-name {
            font-size: 20px;
        }

        .estimateInventoryDataBlue {
            background-color: #1676ff;
        }

        .line-center {
            display: inline-block;
            background-color: blue;
            border-radius: 50%;
            height: 30px;
            width: 30px;
            color: white;
            text-align: center;
            line-height: 30px;
            font-size: 18px;
            font-weight: bold;
        }

        .icon-plus {
            font-size: 30px;
            line-height: 23px;
        }

        .icon-plus:hover {
            cursor: pointer;
        }

        .dataDisplayArea-head-img {
            float: right;
            width: 30px;
            height: 25px;
            margin: 10px 20px 10px 0px;
        }

        .dataDisplayArea-head-img:hover {
            cursor: pointer;
        }

        .form-control {
            height: 28px !important;
        }

        .label-right {
            font-size: 13px;
            text-align: right;
        }

        .align-item-center {
            display: flex;
            align-items: center;

        }

        .col-sm-1 {
            text-align: center;
        }

        textarea[class='layui-layer-input'] {
            outline: 0 !important;
            width: 700px !important;
            height: 350px !important;
            padding-left: 10px !important;
            border: 1px solid #e6e6e6 !important;
            color: #333 !important;
            box-shadow: none !important;
        }

        button[name='actionBtn'] {
            color: white;
        }

        select {
            padding: 0px 12px !important;
        }

        div[name='dataCollectAreaRow'] input {
            padding: 0px 5px;
        }


        #firstRegistrationTime[readonly]{
            background-color: white;
        }

        #lossAssessmentTime[readonly]{
            background-color: white;
        }
    </style>

    <script type="text/javascript">

        var viewer = null;

        var rowNum = 0;

        $(function () {

            /*图片展示工具*/
            (function () {
                viewer = new Viewer({
                    activeId: null
                });
            })();

            // $("#firstRegistrationTime").datepicker({
            //     format: "yyyy-mm-dd",
            //     endDate: new Date(),
            //     autoclose: true
            // });
            // $("#lossAssessmentTime").datepicker({
            //     format: "yyyy-mm-dd",
            //     endDate: new Date(),
            //     autoclose: true
            // });

            /*获取焦点移除报错样式*/
            $("body").on("focus", "input,textarea,select,.ggLayer", function () {
                document.onkeydown = function (w) {
                }
            });

            /*点击缩略图*/
            $('body').on('click', '.thumbnail-img', function () {
                if ($(this).hasClass('selected-thumbnail-img')) {
                    return;
                }
                let attrId = $(this).data('fileid');
                $('.selected-thumbnail-img').removeClass('selected-thumbnail-img');
                $(this).addClass('selected-thumbnail-img');
                viewer.showImageByFileId(attrId);
                let imgNumber = $(this).data('img-number');
                let offset = imgNumber * 95 - (1050 - 95) / 2;
                $('#thumbnail').scrollTop(offset);
            });


            /*A/D切换影像*/
            $('body').on('click', '.viewer-list > li', function () {
                let attchId = $(this).find('img').attr('alt');
                $('#thumbnail-' + attchId).trigger('click');
            });

            $("div[name='dataCollectArea']").on('change', "input[validName='billCollection']", check);

            $("div[name='claimCaseObjectPayment']").on('change', "input[validName='billCollection']", check);


            // 刷新报价维修金额，当input失去焦点的时候
            $("div[name='dataCollectArea']").on('blur', "input[name='lossAssessment'],input[name='approvedAmount']", freshLossAssessment);
            $("#residualValue").on('blur', freshLossAssessment);
            $("#residualNuclearLossValue").on('blur', freshLossAssessment);

            //初始话数据
            <#if assessmentReportList?exists &&(assessmentReportList?size>0)>
                <#list assessmentReportList as assessmentReportData>
            addDataRow('${assessmentReportData.code}', '${assessmentReportData}');
                </#list>
            </#if>

            let objectPaymentList = JSON.parse('${objectPaymentList}');
            for (let objectPayment of objectPaymentList) {
                console.info(objectPayment);
                addPaymentRow(objectPayment);
            }

            let status = '${status}';
            if (status == '0' || status == '2') {
                $.each($("[validName='billCollection']"), function (index, obj) {
                    $(this).attr("disabled", "1");
                });
                $.each($(""));
                $.each($(".icon-plus"), function (index, obj) {
                    $(this).remove();
                });
                $.each($(".dataDisplayArea-head-img"), function (index, obj) {
                    $(this).remove();
                });
            }
            if(status=='1'){
                $('#residualValue').val("");
                $('#verifyAmout').val("");
                $('#verifyDetail').val("");
                $('#dutyRate').val("");
                $('#deductFee').val("");
            }

            var createInput = function (name, value, type = "hidden") {
                var inputElement = document.createElement("input");
                inputElement.type = type;
                inputElement.name = name;
                if (value != null) {
                    inputElement.value = value;
                }
                return inputElement;
            }


            var postForm = function (url) {
                // 创建表单
                var formElement = document.createElement("form");
                formElement.action = url;
                formElement.method = "post";
                // 打开新标签
                formElement.target = '_blank';
                formElement.style.display = "none";
                var code = $.trim($("#assessmentReportName").val());
                var type = 2;//机动车为2
                if (code != "") {
                    formElement.appendChild(createInput("code", code));
                }
                formElement.appendChild(createInput("type", type));
                var assessmentFather = {};
                let lossAssessmentSum = $("#lossAssessmentSum").attr("sumMoney");
                if (lossAssessmentSum != "") {
                    assessmentFather.lossAssessmentSum = lossAssessmentSum;
                }
                let carNumber = $.trim($("#carNumber").val());
                if (carNumber != "") {
                    assessmentFather.carNumber = carNumber;
                }
                let carModel = $.trim($("#carModel").val());
                if (carModel != "") {
                    assessmentFather.carModel = carModel;
                }
                let carEncoding = $.trim($("#carEncoding").val());
                if (carModel != "") {
                    assessmentFather.carEncoding = carEncoding;
                }
                let firstRegistrationTime = $.trim($("#firstRegistrationTime").val());
                if (firstRegistrationTime != undefined && firstRegistrationTime.trim() != "") {
                    let firstRegistrationTimeStr = firstRegistrationTime.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
                    firstRegistrationTime = new Date(firstRegistrationTimeStr+" 00:00:00").getTime();
                    assessmentFather.firstRegistrationTime = firstRegistrationTime;
                }
                let carOwner = $.trim($("#carOwner").val());
                if (carOwner != "") {
                    assessmentFather.carOwner = carOwner;
                }
                let lossAssessmentTime = $.trim($("#lossAssessmentTime").val());
                if (lossAssessmentTime != undefined && lossAssessmentTime.trim() != "") {
                    let lossAssessmentTimeStr = lossAssessmentTime.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
                    lossAssessmentTime = new Date(lossAssessmentTimeStr+" 00:00:00").getTime();
                    assessmentFather.lossAssessmentTime = lossAssessmentTime;
                }
                let repairFactory = $.trim($("#repairFactory").val());
                if (repairFactory != "") {
                    assessmentFather.repairFactory = repairFactory;
                }
                let verifyAmout = $.trim($("#verifyAmout").val());
                if (verifyAmout != "") {
                    assessmentFather.verifyAmout = verifyAmout;
                }
                let residualValue = $.trim($("#residualValue").val());
                if (residualValue != "") {
                    assessmentFather.residualValue = residualValue;
                }
                let verifyDetail = $.trim($("#verifyDetail").val());
                if (verifyDetail != "") {
                    assessmentFather.verifyDetail = verifyDetail;
                }
                let is4S = $.trim($("#is4S").val());
                if (is4S != "") {
                    assessmentFather.is4S = is4S;
                }
                let dutyRate = $.trim($("#dutyRate").val());
                if (dutyRate != "") {
                    assessmentFather.dutyRate = dutyRate;
                }
                let deductFee = $.trim($("#deductFee").val());
                if (deductFee != "") {
                    assessmentFather.deductFee = deductFee;
                }

                assessmentFather.assessmentReport = [];
                $.each($("div[name='dataCollectAreaSubjet']"), function (index, obj) {
                    let code = $(this).attr("code");
                    $.each($(this).find("div[name='dataCollectAreaRow']"), function (index, obj) {
                        let repairName = $.trim($(this).find("input[name='repairName']").val());
                        let repairPrice = $.trim($(this).find("input[name='repairPrice']").val());
                        let approvedAmount = $.trim($(this).find("input[name='approvedAmount']").val());
                        let lossAssessment = $.trim($(this).find("input[name='lossAssessment']").val());

                        assessmentFather.assessmentReport.push({
                            "code": code,
                            "repairName": repairName,
                            "repairPrice": repairPrice,
                            "lossAssessment": lossAssessment,
                            "approvedAmount": approvedAmount
                        })
                    });
                });
                formElement.appendChild(createInput("assessmentFatherStr", JSON.stringify(assessmentFather)));
                document.body.appendChild(formElement);
                formElement.submit();
                formElement.remove();
                return;
            }


            //导出按钮监控器
            $("#inputForm").on("click", ".exportBtn", function (e) {
                e.stopPropagation();
                e.preventDefault();
                var _this = this;
                $(_this).removeClass("exportBtn");
                //提交之后再把class加回来 防止重复提交
                postForm("${ctx}/downloadCenterController/downloadAssessment");
                $(_this).addClass("exportBtn");

            });

            // 责任变更同步比例
            $("div[name='dataCollectArea']").on("change", "#accidentLiability", function() {
                let dutyRate = $(this).find("option:selected").attr("data-dutyRate");
                $("#dutyRate").val(dutyRate);
            });

            freshLossAssessment();

            // 判断是否查看
            var isShow = "${isShow}";
            if (isShow) {
                $("input").attr("disabled", "1");
                $("select").attr("disabled", "1");
                $("textarea").attr("disabled", "1");
            }

            var layerTop = top.layer;
            var errorMsg = "${errorMsg}";
            if (errorMsg) {
                layerTop.msg(errorMsg, {
                    icon: 2,
                    time: 3000
                });
            }

            let date = new Date();
            function pad(number) {
                return number < 10 ? '0' + number : number;
            }
            let year = date.getFullYear();
            let month = pad(date.getMonth() + 1);
            let day = pad(date.getDate());
            let formattedDate = `${year}-${month}-${day}`;
            laydate.render({
                elem: '#firstRegistrationTime',
                type: 'date',
                max: formattedDate,
                done: function(value, date){
                    console.log(value);
                }
            });

            laydate.render({
                elem: '#lossAssessmentTime',
                type: 'date',
                max: formattedDate,
                done: function(value, date){
                    console.log(value);
                }
            });
        });

        //刷新报价维修金额
        function freshLossAssessment() {
            var repairPriceSum = new BigDecimal("0.00");
            var sumApprovedAmount = new BigDecimal("0.00");
            $.each($("div[name='dataCollectAreaSubjet']"), function (index, obj) {
                let sumLineRepairPrice = new BigDecimal("0");

                $.each($(this).find("div[name='dataCollectAreaRow']"), function (index, obj) {
                    try {
                        let lossAssessment = $(this).find("input[name='lossAssessment']").val();
                        if (lossAssessment != undefined && lossAssessment.trim() != '') {
                            lossAssessment = new BigDecimal(lossAssessment).setScale(2, MathContext.ROUND_HALF_UP);
                            sumLineRepairPrice = sumLineRepairPrice.add(lossAssessment).setScale(2, MathContext.ROUND_HALF_UP);
                            repairPriceSum = repairPriceSum.add(lossAssessment).setScale(2, MathContext.ROUND_HALF_UP);
                        }

                        let approvedAmount = $(this).find("input[name='approvedAmount']").val();
                        if (approvedAmount != undefined && approvedAmount.trim() != '') {
                            approvedAmount = new BigDecimal(approvedAmount).setScale(2, MathContext.ROUND_HALF_UP);
                            sumApprovedAmount = sumApprovedAmount.add(approvedAmount).setScale(2, MathContext.ROUND_HALF_UP);
                        }
                    } catch (e) {

                    }

                });
                let code = $(this).attr("code");
                let remark = "合计";
                switch (code) {
                    case "2-1":
                        remark = "配件金额合计：";
                        break;
                    case "2-2":
                        remark = "维修人工合计：";
                        break;
                }
                $("#" + code + "LossAssessment").html(remark + sumLineRepairPrice + "元");
            });
            //维修合计金额=配件合计+人工合计-残值
            let residualValue = new BigDecimal("0");
            try {
                residualValue = new BigDecimal($("#residualValue").val());
            } catch (e) {
            }
            repairPriceSum = repairPriceSum.subtract(residualValue).setScale(2, MathContext.ROUND_HALF_UP);

            // 核损合计 = 核损合计 - 残值核损金额
            let residualNuclearLossValue = new BigDecimal("0");
            try {
                residualNuclearLossValue = new BigDecimal($("#residualNuclearLossValue").val());
            } catch (e) {
            }
            sumApprovedAmount = sumApprovedAmount.subtract(residualNuclearLossValue).setScale(2, MathContext.ROUND_HALF_UP);

            $("#lossAssessmentSum").html("维修估损总金额：" + repairPriceSum + "元");
            $("#lossAssessmentSum").attr("sumMoney", repairPriceSum);

            $("#nuclearLossSum").html("核损合计：" + sumApprovedAmount + "元");
            $("#nuclearLossSum").attr("sumMoney", sumApprovedAmount);
        }



        //计算相差年份
        function calculateYearDifference(dateString1, dateString2) {
            // 将日期字符串转换为Date对象
            const date1 = new Date(dateString1);
            const date2 = new Date(dateString2);

            // 获取年份
            const year1 = date1.getFullYear();
            const year2 = date2.getFullYear();

            // 计算年份差
            const yearDifference = Math.abs(year2 - year1);

            return yearDifference;
        }


        // 添加项目
        function addDataRowClick(code) {
            let data = {};
            data["id"] = "";
            data["name"] = "";
            data["lossAssessment"] = "";
            data["approvedAmount"] = "";
            data["verifyAmount"] = "0";
            addDataRow(code, JSON.stringify(data))
        }

        function getDateYYYYMMDD(timestamp){
            let date = new Date();
            if (timestamp){
                date = new Date(timestamp);
            }
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();
            month = month < 10 ? '0' + month : month;
            day = day < 10 ? '0' + day : day;
            let dateStr = year + '-' + month + '-' + day;
            return dateStr;
        }


        function addPaymentRow(objectPayment) {
            rowNum = rowNum + 1;
            let id = "";
            let name = "";
            let idNum = "";
            let idType = "身份证";
            let mobile = "";
            let payObjectType = "1";
            let bankAccount = "";
            let bankCard = "";
            let bankName = "";
            let bankInfoId = "";
            let payAmount = "";
            let idNumStartDate = "";
            let idNumEndDate = "";
            let idNumEndDateSpan = "";
            if (objectPayment != undefined) {
                id = objectPayment.id??"";
                name = objectPayment.name??"";
                idNum = objectPayment.idNum??"";
                idType = objectPayment.idType
                mobile = objectPayment.mobile??"";
                payObjectType = objectPayment.payObjectType;
                bankAccount = objectPayment.bankAccount??"";
                bankCard = objectPayment.bankCard??"";
                bankName = objectPayment.bankName??"";
                bankInfoId = objectPayment.bankInfoId??"";
                payAmount = objectPayment.payAmount??"";
                idNumStartDate =  objectPayment.idNumStartDate??""
                idNumEndDate =  objectPayment.idNumEndDate??""
            }
            let calculateYear;
            let idNumStartDateContent;
            if (idNumStartDate) {
                if (idNumEndDate) {
                    idNumEndDateSpan = getDateYYYYMMDD(idNumEndDate).replace(/-/g, '/');
                    calculateYear  = calculateYearDifference(idNumStartDate,idNumEndDate);
                }else {
                    //证件长期有效
                    calculateYear = -1;
                }
                idNumStartDateContent = getDateYYYYMMDD(idNumStartDate);
            }
            $("#paymentCollectArea").append(
                '<div name="paymentCollectAreaRow" class="row block-border" style="margin-bottom: 5px;background-color: white;" data-index="'+ rowNum +'">\n' +
                '<#if !verifyReadonly??>\n' +
                '                        <div class="row">\n' +
                '                            <img src="/images/shanchu.svg" id="deleteBtn' + rowNum +'" class="dataDisplayArea-head-img" onclick="delPaymentRow(this)">\n' +
                '                        </div>\n' +
                '</#if>\n' +
                '                        <input type="hidden" id="claimCaseObjectPaymentId" class="form-control" value="' + id + '">\n' +
                '                        <div class="row">\n' +
                '                            <div style="margin: 15px;display: flex;align-items: center;">\n' +
                '                                <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">领款人名称</div>\n' +
                '                                <div class="col-sm-9 clear-padding-left">\n' +
                '                                    <input validName="billCollection" id="paymentName" type="text" class="form-control" value="' + name + '" title="' + name + '" data-valid="isEmpty" <#if verifyReadonly??>readonly</#if>/>\n' +
                '                                </div>\n' +
                '                            </div>\n' +
                '                        </div>\n' +
                '                        <div class="row">\n' +
                '                            <div style="margin: 15px;display: flex;align-items: center;">\n' +
                '                                <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">证件类型</div>\n' +
                '                                <div class="col-sm-9 clear-padding-left">\n' +
                '                                    <select validName="billCollection" class="kp-select2 inputStyle form-control" data-valid="none" id="idType' + rowNum + '" name="idType' + rowNum + '" <#if verifyReadonly??>disabled</#if>>\n' +
                '                                        <option value="身份证">身份证</option>\n' +
                '                                        <option value="其他">其他</option>\n' +
                '                                    </select>\n' +
                '                               </div>\n' +
                '                            </div>\n' +
                '                        </div>\n' +
                '                        <div class="row">\n' +
                '                            <div style="margin: 15px;display: flex;align-items: center;">\n' +
                '                                <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">证件号码</div>\n' +
                '                                <div class="col-sm-9 clear-padding-left">\n' +
                '                                    <input validName="billCollection" id="idNum" type="text" class="form-control" value="' + idNum + '" title="' + idNum + '" data-valid="isEmpty" <#if verifyReadonly??>readonly</#if>/>\n' +
                '                                </div>\n' +
                '                            </div>\n' +
                '                        </div>\n' +
                '                        <div class="row">\n' +
                '                            <div style="margin: 15px;display: flex;align-items: center;">\n' +
                '                                <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">证件有效期</div>\n' +
                '                                <div class="col-sm-9 clear-padding-left" style="width: 38%">\n' +
                '                                    <input validName="billCollection" type="date" max="'+getDateYYYYMMDD()+'" id="idNumStartDate" class="form-control idNumStartDate'+rowNum+'" value="'+idNumStartDateContent+'" data-valid="none" <#if verifyReadonly??>readonly</#if>/>\n' +
                '                                </div>\n' +
                '                                <span style="position: relative;right: 5px">-</span>\n' +
                '                                <div class="col-sm-9 clear-padding-left" style="width: 38%">\n' +
                '                                    <select validName="billCollection" id="idNumEndDate" class="kp-select2 inputStyle form-control idNumSelect' +rowNum+ '" data-valid="none" value="'+calculateYear+'" <#if verifyReadonly??>disabled</#if>>\n' +
                '                                       <option value="">请选择</option>\n' +
                '                                       <option value="5">5年</option>\n' +
                '                                       <option value="10">10年</option>\n' +
                '                                       <option value="20">20年</option>\n' +
                '                                       <option value="-1">长期有效</option>\n' +
                '                                   </select>\n' +
                '                                </div>\n' +
                '                              </div>\n' +
                '                               <div class="col-sm-9 clear-padding-right"  style="width: 90%;text-align: right;line-height: 5px">' +
                '                                    <span id="idCardEndDateSpan'+rowNum+'">'+idNumEndDateSpan+'</span>' +
                '                               </div>'+
                '                            </div>\n' +
                '                        <div class="row">\n' +
                '                            <div style="margin: 15px;display: flex;align-items: center;">\n' +
                '                                <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">联系电话</div>\n' +
                '                                <div class="col-sm-9 clear-padding-left">\n' +
                '                                    <input validName="billCollection" id="mobile" type="text" class="form-control" value="' + mobile + '" title="' + mobile + '" data-valid="isEmpty" <#if verifyReadonly??>readonly</#if>/>\n' +
                '                                </div>\n' +
                '                            </div>\n' +
                '                        </div>\n' +
                '                        <div class="row">\n' +
                '                            <div style="margin: 15px;display: flex;align-items: center;">\n' +
                '                                <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">账号性质</div>\n' +
                '                                <div class="col-sm-9 clear-padding-left">\n' +
                '                                    <select validName="billCollection" class="kp-select2 inputStyle form-control" data-valid="none" id="payObjectType' + rowNum + '" name="payObjectType' + rowNum + '" <#if verifyReadonly??>disabled</#if>>\n' +
                '                                        <option value="1" >单位</option>\n' +
                '                                        <option value="2" >个人</option>\n' +
                '                                    </select>\n' +
                '                                </div>\n' +
                '                            </div>\n' +
                '                        </div>\n' +
                '                        <div class="row">\n' +
                '                            <div style="margin: 15px;display: flex;align-items: center;">\n' +
                '                                <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">开户名</div>\n' +
                '                                <div class="col-sm-9 clear-padding-left">\n' +
                '                                    <input validName="billCollection" id="bankAccount" type="text" class="form-control" value="' + bankAccount + '" title="' + bankAccount + '" data-valid="isEmpty" <#if verifyReadonly??>readonly</#if>/>\n' +
                '                                </div>\n' +
                '                            </div>\n' +
                '                        </div>\n' +
                '                        <div class="row">\n' +
                '                            <div style="margin: 15px;display: flex;align-items: center;">\n' +
                '                                <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">银行账号</div>\n' +
                '                                <div class="col-sm-9 clear-padding-left">\n' +
                '                                    <input validName="billCollection" id="bankCard" type="text" class="form-control" value="' + bankCard + '" title="' + bankCard + '" data-valid="isEmpty" <#if verifyReadonly??>readonly</#if>/>\n' +
                '                                </div>\n' +
                '                            </div>\n' +
                '                        </div>\n' +
                '                        <div class="row">\n' +
                '                            <div style="margin: 15px;display: flex;align-items: center;">\n' +
                '                                <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">银行名称</div>\n' +
                '                                <div class="col-sm-9 clear-padding-left">\n' +
                '                                    <select id="totalBankSelect' + rowNum + '" name="totalBankSelect' + rowNum + '" \n' +
                '                                class="js-data-example-ajax" <#if verifyReadonly??>disabled</#if>>\n' +
                '                                    </select>\n' +
                '                                    <input id="bankName' + rowNum + '" name="bankName' + rowNum + '" type="hidden" value="' + bankName + '" >\n' +
                '                                </div>\n' +
                '                            </div>\n' +
                '                        </div>\n' +
                '                        <div class="row">\n' +
                '                            <div style="margin: 15px;display: flex;align-items: center;">\n' +
                '                                <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">银行支行</div>\n' +
                '                                <div class="col-sm-9 clear-padding-left">\n' +
                '                                    <select id="bankSubbranchSelect' + rowNum + '" name="bankSubbranchSelect' + rowNum + '" class="js-data-example-ajax" <#if verifyReadonly??>disabled</#if>>\n' +
                '                                    </select>\n' +
                '                                    <input id="bankInfoId' + rowNum + '" name="bankInfoId' + rowNum + '" type="hidden" value="' + bankInfoId + '">\n' +
                '                                </div>\n' +
                '                            </div>\n' +
                '                        </div>\n' +
                '                        <div class="row">\n' +
                '                            <div style="margin: 15px;display: flex;align-items: center;">\n' +
                '                                <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">划款金额</div>\n' +
                '                                <div class="col-sm-9 clear-padding-left">\n' +
                '                                    <input validName="billCollection" id="payAmount" type="text" class="form-control" value="' + payAmount + '" title="' + payAmount + '" data-valid="isEmpty isNumberAndDecimalPoint" <#if verifyReadonly??>readonly</#if>/>\n' +
                '                                </div>\n' +
                '                            </div>\n' +
                '                        </div>\n' +
                '                       </div>\n' +
                '                      <hr />'
            );
            $(".idNumSelect" + rowNum).val(calculateYear);
            $("#idType" + rowNum).val(idType).attr("title", idType);
            $("#payObjectType" + rowNum).val(payObjectType).attr("title", payObjectType);
            reloadbankInfo(rowNum);
            if (objectPayment != undefined) {
                let bankInfoMap = JSON.parse('${bankInfoMap}');
                let bankInfo = bankInfoMap[objectPayment.bankInfoId];
                $("#select2-bankSubbranchSelect"+rowNum+"-container .select2-selection__placeholder").html(bankInfo.bankName??"").attr("title", bankInfo.bankName);
                $("#select2-totalBankSelect"+rowNum+"-container .select2-selection__placeholder").html(objectPayment.bankName).attr("title", objectPayment.bankName);
                $("#bankName" + rowNum).val(objectPayment.bankName);
                $("#bankInfoId" + rowNum).val(objectPayment.bankInfoId);
            }
            var isShow = "${isShow}";
            if (isShow) {
                $("#deleteBtn"+rowNum).prop('hidden', true);
            }

            function calculateEndDate(idNumStartDate, idNumSelectVal, spanId) {
                if (idNumSelectVal && idNumSelectVal != -1) {
                    let date = new Date(idNumStartDate);
                    let year = date.getFullYear();
                    date.setFullYear(year + parseInt(idNumSelectVal));
                    let idNumEndDateContent = date.toISOString().substring(0, 10).replace(/-/g, '/');
                    $("#" + spanId).text(idNumEndDateContent);
                } else {
                    $("#" + spanId).text("");
                }
            }
            for (let i = 1; i <= rowNum; i++) {
                // 为选择框添加change事件处理器
                $(".idNumSelect" + i).on('change', function () {
                    calculateEndDate($(".idNumStartDate" + i).val(), $(".idNumSelect" + i).val(), "idCardEndDateSpan" + i);
                });

                // 为开始日期输入框添加change事件处理器
                $(".idNumStartDate" + i).on('change', function () {
                    calculateEndDate($(".idNumStartDate" + i).val(), $(".idNumSelect" + i).val(), "idCardEndDateSpan" + i);
                });
            }



        }


        function reloadbankInfo(rowNum) {

            $("#bankSubbranchSelect" + rowNum).select2({
                ajax: {
                    type: 'POST',
                    url: "${ctx}/insuranceCaseController/getBankInfo",
                    dataType: 'json',
                    delay: 500,
                    contentType: "application/x-www-form-urlencoded; charset=utf-8",
                    data: function (params) {
                        return {
                            totalBankId: $('#totalBankSelect' + rowNum).val(),
                            paramMsg: params.term,
                            insCode: '${claimCase.insCode}'
                        };
                    },
                    processResults: function (data) {
                        console.log(data);
                        return {
                            results: data.bankList,
                        };
                    },
                },
                escapeMarkup: function (markup) {
                    return markup;
                },
                width: '100%',
                placeholder: "请输入支行名称查询...",
                minimumInputLength: 2,
                minimumResultsForSearch: Infinity, // 隐藏搜索框
                templateResult: function (data) {
                    if (data.id != undefined) {
                        return "<div>" + data.bankName+"("+data.provinceName+"-"+data.cityName+")" + "</div>";
                    }
                    return "";
                },
                templateSelection: function (data) {
                    if (data.id != "") {
                        $("#bankInfoId" + rowNum).val(data.id);
                        let bankText = data.bankName+"("+data.provinceName+"-"+data.cityName+")";
                        return "<span title='"+bankText+"'>" + bankText  + "</span>";
                    }
                    return "请输入支行名称查询...";
                }
            });


            //查询总行信息
            $("#totalBankSelect" + rowNum).select2({
                ajax: {
                    type: 'POST',
                    url: "${ctx}/insuranceCaseController/getTotalBankInfo",
                    dataType: 'json',
                    delay: 500,
                    contentType: "application/x-www-form-urlencoded; charset=utf-8",
                    data: function (params) {
                        return {
                            paramMsg: params.term,
                            insCode: '${claimCase.insCode}'
                        };
                    },
                    processResults: function (data) {
                        console.log(data);
                        return {
                            results: data.bankList,
                        };
                    },
                },
                escapeMarkup: function (markup) {
                    return markup;
                },
                width: '100%',
                placeholder: "请输入总行名称查询...",
                minimumInputLength: 2,
                minimumResultsForSearch: Infinity, // 隐藏搜索框
                templateResult: function (data) {
                    if (data.id != undefined) {
                        return "<div>" + data.bankName + "</div>";
                    }
                    return "";
                },
                templateSelection: function (data) {
                    if (data.id != "") {
                        $("#bankName" + rowNum).val(data.bankName);
                        $('#bankSubbranchSelect' + rowNum).prop('disabled', false);
                        $("#bankInfoId" + rowNum).val("");
                        $("#bankSubbranchSelect" + rowNum).select2("val", "");
                        $("#select2-bankSubbranchSelect-container .select2-selection__placeholder").html("");
                        return "<span>" + data.bankName + "</span>";
                    }
                    $("#bankName" + rowNum).val("");
                    $("#bankInfoId" + rowNum).val("");
                    $("#bankSubbranchSelect" + rowNum).select2("val", "");
                    $('#bankSubbranchSelect' + rowNum).prop('disabled', true);
                    return "请输入总行名称查询...";
                }
            });
        }

        //增加一条数据
        function addDataRow(code, data) {
            data = JSON.parse(data);
            console.log(data.id);
            let name=data.name;
            if(name == undefined){
                name = "";
            }
            let lossAssessment=data.lossAssessment;
            if(lossAssessment == undefined){
                lossAssessment = "";
            }
            let approvedAmount=data.approvedAmount;
            if(approvedAmount == undefined){
                approvedAmount = "";
            }
            let verifyAmount=data.verifyAmount;
            if(verifyAmount == undefined){
                verifyAmount = "";
            }
            let after = $("#" + code + "collectArea").append(`
                <div class="col-sm-12 align-item-center" style="margin-top: 15px" name="dataCollectAreaRow">
                    <input type="hidden"  name="id" class="form-control" value='`+ data.id + `' />
                    <div class="col-sm-1 clear-padding" style="text-align: left;">
                        <div class="clear-padding line-center"></div>
                    </div>
                    <div class="col-sm-10 clear-padding">
                        <div class="col-sm-12 clear-padding-left align-item-center" style="margin-bottom: 5px;">
                             <div class="col-sm-1 clear-padding" >名称</div>
                             <div class="col-sm-11 clear-padding-right">
                                <input type="text" style="padding: 0px !important;" validName="billCollection" data-valid="isEmpty" name="name" class="form-control" value="` + name + `" title="`+name+`" />
                             </div>
                        </div>
                        <div class="col-sm-4 clear-padding align-item-center" >
                               <div class="col-sm-3 clear-padding" >估损</div>
                               <div class="col-sm-9" >
                                   <input type="text" validName="billCollection" name="lossAssessment" data-valid="isNumberAndDecimalPoint isEmpty" class="form-control clear-padding" value="` + lossAssessment + `" title="`+lossAssessment+`" />
                               </div>
                        </div>
                        <div class="col-sm-4 clear-padding align-item-center" >
                            <div class="col-sm-3 clear-padding">核损</div>
                            <div class="col-sm-9" >
                                <input type="text" validName="billCollection" name="approvedAmount" data-valid="isNumberAndDecimalPoint" class="form-control clear-padding" value="` + approvedAmount + `" title="`+approvedAmount+`" readonly />
                            </div>
                        </div>
                        <div class="col-sm-4 clear-padding align-item-center" >
                            <div class="col-sm-3 clear-padding">理算</div>
                            <div class="col-sm-9" >
                                <input type="text" validName="billCollection" name="verifyAmount" data-valid="isNumberAndDecimalPoint" class="form-control clear-padding" value="` + verifyAmount + `" title="`+verifyAmount+`" readonly />
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-1 clear-padding" >
                        <img src="/images/shanchu.svg"  class="dataDisplayArea-head-img" onclick="delDataRow(this,` + code + `)">
                    </div>
                </div>
            `);
            //刷新序号
            freshLineCenter();
            //刷新维修报价金额
            freshLossAssessment();
        }

        //删除一行数据
        function delDataRow(obj, code) {
            $(obj).parent().parent().remove();
            //刷新序号
            freshLineCenter();
            //刷新维修报价金额
            freshLossAssessment();
        }

        function delPaymentRow(obj) {
            $(obj).parent().parent().remove();
        }

        //刷新序号
        function freshLineCenter() {
            $.each($("div[name='dataCollectAreaRow']"), function (index, obj) {
                let prevName = $(this).prev().attr("name");
                let num = 1;
                if (prevName != undefined && prevName == "dataCollectAreaRow") {
                    let prevNum = $(this).prev().find(".line-center").html();
                    num = new Number(prevNum) + 1;
                }
                $(this).find(".line-center").html(num);
            });
        }


        function seePlanName(planId) {
            if (typeof planId == 'undefined' || planId == '') {
                layer.msg('暂未数据！！！', {
                    icon: 2,
                    time: 2000
                });
                return;
            }

            var formData = new FormData();
            formData.append("planId", planId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/seePlanNameByPlanId",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        window.open(result.msg, "_blank");
                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

        }

        function seeCaseProcessReason(claimCaseId) {
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '查看案件流转',
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/getBSCaseAssessmentProcessReason?claimCaseId=" + claimCaseId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        //查看案件流转原因并显示所有日志信息
        function seeCaseProcessReasonAndLogs(claimCaseId){
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '查看案件流转并显示所有日志信息',
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/getBSCaseAssessmentProcessReasonAll?claimCaseId=" + claimCaseId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        function getHistroyCaseInfo(baseUserId) {
            if (baseUserId == '') {
                layer.msg("暂无信息！！！", {icon: 2, time: 3000});
                return;
            }
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '查看历史案件',
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/getHistroyCaseInfo?baseUserId=" + baseUserId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        //电子保单
        function onloadInteriorPolicy(policyPersonId) {
            if (typeof policyPersonId == 'undefined' || policyPersonId == '') {
                layerTop.msg('暂未数据！！！', {
                    icon: 2,
                    time: 2000
                });
                return;
            }

            var formData = new FormData();
            formData.append("policyPersonId", policyPersonId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/onloadInteriorPolicy",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        window.open(result.msg, "_blank");
                    } else {
                        layerTop.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

        }

        //生成uuid
        function guid() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                var r = Math.random() * 16 | 0,
                    v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }


        let check = function () {
            var _this = this;
            var val = _this.value;
            var valid = _this.getAttribute('data-valid').trim();
            valid = valid.split(" ");
            for (let check of valid) {
                if (!checkService[check](val)) {
                    $(_this).val("");
                    // 带遮罩的弹出
                    // layer.msg("输入格式不对", {icon: 2,time: 2000,shade: [0, 'rgba(0,0,0,0)']});
                    if (check == "isEmpty") {
                        layer.tips("值不能为空", $(_this), {time: 1500, shade: [0.0001, '#000']});
                    } else {
                        layer.tips("输入格式不对", $(_this), {time: 1500, shade: [0.0001, '#000']});
                    }
                    return;
                }
            }

            $(this).attr("title", val);

        }
        //校验规则
        const checkService = {
            // 不校验
            none: function () {
                return true;
            },

            //非空校验
            isEmpty: function (str) {
                let result = true;

                if (str == null || str == "") {
                    result = false;
                }
                if (str == "0") {
                    result = true;
                }

                return result;
            },

            //身份证校验
            isIdCard: function (str) {
                const idCardCheck = new IdCardCheck();
                if (idCardCheck.checkIdCard(str)) {
                    return false;
                }
                return true;
            },

            // 只能输入数字[0-9]
            isDigits: function (str) {
                if (str == null || str == "") return true;
                var reg = /^\d+$/;
                return reg.test(str);
            },
            //百分比0-100
            isRate: function(str){
                if (str == null || str == "") return true;
                var reg = /^\d+$/;
                let result = false;
                if(reg.test(str) && (str>=0 && str <=100)){
                    result = true;
                }
                return result;
            },

            // 匹配english
            isEnglish: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[A-Za-z]+$/;
                return reg.test(str);
            },

            // 匹配integer(包含正负)
            isInteger: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[-\+]?\d+$/;
                return reg.test(str);
            },

            // 匹配汉字
            isChinese: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[\u4e00-\u9fa5]+$/;
                return reg.test(str);
            },

            // 匹配中文(双字节字符,包括汉字和符号)
            isChineseChar: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[\u0391-\uFFE5]+$/;
                return reg.test(str);
            },

            //匹配中英文
            isChineseAndEnglish: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[\u4e00-\u9fa5a-zA-Z]+$/;
                return reg.test(str);
            },

            // 匹配URL
            isUrl: function (str) {
                if (str == null || str == "") return true;
                var reg = /^http:\/\/[A-Za-z0-9]+\.[A-Za-z0-9]+[\/=\?%\-&_~`@[\]\’:+!]*([^<>\"])*$/;
                return reg.test(str);
            },

            // 字符验证，只能包含中文、英文、数字、下划线、空格。
            stringCheck: function (str) {
                if (str == null || str == "") return true;
                var reg = /^[a-zA-Z0-9\u4e00-\u9fa5_ ,.，。]+$/;
                return reg.test(str);
            },

            //字符长度校验（最长64位）
            stringLengthCheck: function (str, length) {
                if (str == null || str == "") return true;
                length = length || 64;
                if (str.length > length) return false;
                return true;
            },
            //IP格式验证
            isIP: function (str) {
                if (str == null || str == "") return true;
                var reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                return reg.test(str);
            },
            //YYYYMMDD格式验证
            isTime: function (str) {
                if (str == null || str == "") return true;
                var reg = /^((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})(((0[13578]|1[02])(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)(0[1-9]|[12][0-9]|30))|(02(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))0229))$/;
                return reg.test(str);
            },
            //只能输入数字和小数点
            isNumberAndDecimalPoint: function (str) {
                if (str == null || str == "") return true;
                var reg = /^\d+(\.\d{1,2})?$/;
                return reg.test(str);
            },
            //只能输入数字和小数点
            isNumberAndDecimalPointNotDefault: function (str) {
                if (str == null || str == "") return true;
                var reg = /^\d+(\.\d{1,2})?$/;
                return reg.test(str);
            },
            //select2--->是否
            isTrueFalse: function (str) {

                return true;
            },
            //伤残等级
            isDisabilityLevel: function () {
                return true;
            },
            //伤残等级
            isDeadSurplusQuota: function () {
                return true;
            }
        };

        // 组装数据
        function assembleData(status) {

            freshLossAssessment();  // 刷新估损总金额

            $("button[name='actionBtn']").attr('disabled','1');

            let objectName = $("#objectName").val().trim();
            let lossAssessmentSum = $("#lossAssessmentSum").attr("sumMoney");
            let nuclearLossSum = $("#nuclearLossSum").attr("sumMoney");

            let residualValue = $("#residualValue").val();
            let residualNuclearLossValue = $("#residualNuclearLossValue").val();

            let category = "${claimCaseObject.category}";

            let isError = false;
            let errorMsg = "";

            /*// 必填校验
            $("input[data-valid*='isEmpty']").not("[readonly]").each(function() {
                let val = $(this).val();
                if (!checkService["isEmpty"](val)) {
                    $(this).val("");
                    isError = true;
                    layer.tips("值不能为空", $(this), {time: 1500, shade: [0.0001, '#000']});
                    return false;
                }
            });
            if (isError) {
                $("button[name='actionBtn']").removeAttr("disabled");
                return;
            }*/

            if (!objectName) {
                isError = true;
                errorMsg += "估损清单名称不能为空！</br>";
            }


            let objectAssessmentList = [];
            $.each($("div[name='dataCollectAreaSubjet']"), function (index, obj) {
                let code = $(this).attr("code");
                $.each($(this).find("div[name='dataCollectAreaRow']"), function (index, obj) {
                    let id = $(this).find("input[name='id']").val().trim();
                    let name = $(this).find("input[name='name']").val().trim();
                    let lossAssessment = $(this).find("input[name='lossAssessment']").val().trim();
                    let approvedAmount = $(this).find("input[name='approvedAmount']").val().trim();
                    let verifyAmount = $(this).find("input[name='verifyAmount']").val().trim();
                    objectAssessmentList.push({
                        "code": code,
                        "id": id,
                        "name": name,
                        "appeal": lossAssessment,
                        "lossAssessment": lossAssessment,
                        "approvedAmount": approvedAmount,
                        "verifyAmount": verifyAmount
                    });
                });
            });

            let objectPaymentList = [];
            $.each($("div[name='paymentCollectAreaRow']"), function (index, obj) {
                let num = $(this).attr("data-index");
                let id = $(this).find("#claimCaseObjectPaymentId").val().trim();
                let name = $(this).find("#paymentName").val().trim();
                let idNum = $(this).find("#idNum").val().trim();
                let idType = $(this).find("#idType"+num).val().trim();
                let mobile = $(this).find("#mobile").val().trim();
                let payObjectType = $(this).find("#payObjectType"+num).val().trim();
                let bankAccount = $(this).find("#bankAccount").val().trim();
                let bankCard = $(this).find("#bankCard").val().trim();
                let bankName = $(this).find("#bankName"+num).val().trim();
                let bankInfoId = $(this).find("#bankInfoId"+num).val().trim();
                let payAmount = $(this).find("#payAmount").val().trim();
                let idNumStartDate = $(this).find("#idNumStartDate").val();
                let idNumEndDate = $(this).find("#idNumEndDate").val();
                let idNumEndDateContent;
                if(idNumStartDate && idNumEndDate) {
                    if (idNumEndDate != -1) {
                        let date = new Date(idNumStartDate);
                        let year = date.getFullYear();
                        date.setFullYear(year + parseInt(idNumEndDate));
                        idNumEndDateContent = date.toISOString().substring(0,10);
                    }
                }
                objectPaymentList.push({"id": id,"name": name, "idNum": idNum, "idType": idType, "mobile": mobile, "payObjectType": payObjectType, "bankAccount": bankAccount, "bankCard": bankCard, "bankName": bankName, "bankInfoId": bankInfoId, "payAmount": payAmount, "idNumStartDate": idNumStartDate,"idNumEndDate": idNumEndDateContent,"isLt":idNumEndDate});
            });

            for (let assessment of objectAssessmentList) {
                if (!checkService["isEmpty"](assessment.name)) {
                    errorMsg += "维修名称不能为空！</br>";
                    isError = true;
                    break;
                }
                if (!checkService["isEmpty"](assessment.lossAssessment)) {
                    errorMsg += "维修估损金额不能为空！</br>";
                    isError = true;
                    break;
                }
            }

            if (!residualValue && category == 3) {
                isError = true;
                errorMsg += "残值不能为空！</br>";
            }

            let object = {
                id: "${claimCaseObject.id}",
                claimCaseId: "${claimCaseObject.claimCaseId}",
                claimCaseNo: "${claimCaseObject.claimCaseNo}",
                name: objectName,
                type: "${claimCaseObject.type}",
                category: "${claimCaseObject.category}",
                claimCaseObjectAssessmentList: objectAssessmentList,
                claimCaseObjectPaymentList: objectPaymentList,
                lossAssessmentSum: lossAssessmentSum,
                nuclearLossSum: nuclearLossSum,
                residualValue: residualValue,
                residualNuclearLossValue: residualNuclearLossValue,
                status: status,
                remark: $("#objectRemark").val().trim()
            };

            <#if claimCaseObject.category == 3>
            let carNumber = $("#carNumber").val().trim();
            if (!carNumber) {
                isError = true;
                errorMsg += "车牌号不能为空！</br>";
            }
            let carModel = $("#carModel").val().trim();
            let carEncoding = $("#carEncoding").val().trim();

            let firstRegistrationTime = $("#firstRegistrationTime").val().trim();
            if (firstRegistrationTime != undefined && firstRegistrationTime.trim() != "") {
                let firstRegistrationTimeStr = firstRegistrationTime.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
                firstRegistrationTime = new Date(firstRegistrationTimeStr+" 00:00:00").getTime();
                console.log(firstRegistrationTime);
            }

            let treatName = $("#treatName").val().trim();

            let lossAssessmentTime = $("#lossAssessmentTime").val().trim();
            if (lossAssessmentTime != undefined && lossAssessmentTime.trim() != "") {
                let lossAssessmentTimeStr = lossAssessmentTime.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
                lossAssessmentTime = new Date(lossAssessmentTimeStr+" 00:00:00").getTime();
                console.log(lossAssessmentTime);
            }

            let repairFactory = $("#repairFactory").val().trim();
            let isServiceShop = $("#isServiceShop").val().trim();

            object.carNumber = carNumber;
            object.carModel = carModel;
            object.carEncoding = carEncoding;
            object.firstRegistrationTime = firstRegistrationTime;
            object.treatName = treatName;
            object.lossAssessmentTime = lossAssessmentTime;
            object.repairFactory = repairFactory;
            object.isServiceShop = isServiceShop;
            </#if>

            if (status.startsWith("BAX2") && status != "BAX21" && status != "BAX22" && status != "BAX25") {
                for (let assessment of objectAssessmentList) {
                    if (!checkService["isEmpty"](assessment.approvedAmount)) {
                        errorMsg += "各项核损金额不能为空！</br>";
                        isError = true;
                        break;
                    }
                }
                if (!checkService["isEmpty"](residualNuclearLossValue) && category == 3) {
                    errorMsg += "残值核损金额不能为空！</br>";
                    isError = true;
                }
            }

            if (status.startsWith("BAX3")) {
                let policyNo = $("#policyNo").val();
                let accidentLiability = $("#accidentLiability").val();
                let dutyRate = $("#dutyRate").val();
                let deductFee = $("#deductFee").val();
                let verifyAmout = $("#verifyAmout").val();
                let assessmentVerifyAmount = new BigDecimal("0");
                object["policyNo"] = policyNo;
                object["accidentLiability"] = accidentLiability;
                object["dutyRate"] = dutyRate;
                object["deductFee"] = deductFee;
                object["verifyAmout"] = verifyAmout;
                object["verifyDetail"] = $("#verifyDetail").val();
                for (let assessment of objectAssessmentList) {
                    if (!checkService["isEmpty"](assessment.verifyAmount)) {
                        errorMsg += "各项理算金额不能为空！</br>";
                        isError = true;
                        break;
                    } else {
                        assessmentVerifyAmount = assessmentVerifyAmount.add(new BigDecimal(assessment.verifyAmount)).setScale(2, MathContext.ROUND_HALF_UP);
                    }
                }
                if (!policyNo) {
                    errorMsg += "保单号不能为空！</br>";
                    isError = true;
                }
                if (!accidentLiability) {
                    errorMsg += "责任不能为空！</br>";
                    isError = true;
                }
                if (!dutyRate) {
                    errorMsg += "赔付比例不能为空！</br>";
                    isError = true;
                }
                if (!deductFee) {
                    errorMsg += "扣减费用不能为空！</br>";
                    isError = true;
                }
                if (!verifyAmout) {
                    errorMsg += "理算金额不能为空！</br>";
                    isError = true;
                } else if (eval(assessmentVerifyAmount) != eval(verifyAmout)) {
                    console.log(eval(assessmentVerifyAmount), eval(verifyAmout));
                    errorMsg += "理算金额不等于各项理算金额和！</br>";
                    isError = true;
                }
                if (verifyAmout && eval(verifyAmout) > eval(nuclearLossSum)) {
                    errorMsg += "理算金额不能大于核损总金额！</br>";
                    isError = true;
                }
                var payAmountSum = new BigDecimal("0");
                let isValidity = '${isValidity}';
                for(let payment of objectPaymentList) {
                    if (!payment.name) {
                        errorMsg += "领款人不能为空！</br>";
                        isError = true;
                    }
                    if (!payment.idType) {
                        errorMsg += "证件类型不能为空！</br>";
                        isError = true;
                    }
                    if (!payment.idNum) {
                        errorMsg += "证件号码不能为空！</br>";
                        isError = true;
                    }  else if (payment.idType == "身份证" && !checkService["isIdCard"](payment.idNum)) {
                        errorMsg += "身份证件号码错误！</br>";
                        isError = true;
                    }
                    if (!payment.mobile) {
                        errorMsg += "联系电话不能为空！</br>";
                        isError = true;
                    }
                    if (!payment.payObjectType) {
                        errorMsg += "账号性质不能为空！</br>";
                        isError = true;
                    }
                    if (!payment.bankAccount) {
                        errorMsg += "开户名不能为空！</br>";
                        isError = true;
                    }
                    if (!payment.bankCard) {
                        errorMsg += "银行账号不能为空！</br>";
                        isError = true;
                    }
                    if (!payment.bankName) {
                        errorMsg += "银行名称不能为空！</br>";
                        isError = true;
                    }
                    if (!payment.bankInfoId) {
                        errorMsg += "银行支行不能为空！</br>";
                        isError = true;
                    }
                    if (!payment.payAmount) {
                        errorMsg += "划款金额不能为空！</br>";
                        isError = true;
                    } else {
                        payAmountSum = payAmountSum.add(new BigDecimal(payment.payAmount));
                    }
                    if (isValidity){
                        if (!payment.idNumStartDate){
                            errorMsg += "证件有效期开始日期不能为空！</br>";
                            isError = true;
                        }
                        if (!payment.idNumEndDate && payment.isLt != -1){
                            errorMsg += "证件有效期不能为空！</br>";
                            isError = true;
                        } else {
                            let endDate = new Date(payment.idNumEndDate);
                            let today = new Date();
                            if (endDate < today) {
                                errorMsg += "证件已过期！</br>";
                                isError = true;
                            }
                        }
                    }
                }
                if (eval(verifyAmout) != eval(payAmountSum)) {
                    errorMsg += "划款金额总和与理算金额不一致！</br>";
                    isError = true;
                }
            }

            if(isError){
                layer.msg(errorMsg, {icon: 2, time: 3000},function () {
                    $("button[name='actionBtn']").removeAttr("disabled");
                });
                return;
            }

            console.log(JSON.stringify(object));

            return object;
        }

        // 保存 / 提审
        function submitData(status) {
            let claimCaseObject = assembleData(status);
            if (claimCaseObject) {
                $.ajax({
                    url: "${ctx}/claimCaseObjectV2Controller/claimCaseObjectSubmit",
                    type: 'POST',
                    data: JSON.stringify(claimCaseObject),
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            layer.msg(result.msg, {
                                icon: 1,
                                time: 2000,
                                shade: [0.0001, '#000']
                            }, function () {
                                window.location.href = "${ctx}/claimCaseObjectController/claimCaseObjectList?comeFrom=${comeFrom}&status=0";
                            });
                        } else {
                            layer.msg(result.msg, {
                                icon: 2,
                                time: 2000,
                                shade: [0.0001, '#000']
                            }, function() {
                                $("button[name='actionBtn']").removeAttr("disabled");
                            });
                        }

                    },
                    error: function (data) {
                        $("button[name='actionBtn']").removeAttr("disabled");
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                })
            }
        }

        // 审核通过
        function auditPass(status) {
            let claimCaseObject = assembleData(status);

            var lossAssessmentSum = $("#lossAssessmentSum").attr("sumMoney");

            var nuclearLossSum = $("#nuclearLossSum").attr("sumMoney");

            if (lossAssessmentSum != nuclearLossSum) {
                layer.msg("估损总金额不等于核损总金额！", {icon: 2, time: 3000},function () {
                    $("button[name='actionBtn']").removeAttr("disabled");
                });
                return;
            }

            if (claimCaseObject) {
                $.ajax({
                    url: "${ctx}/claimCaseObjectController/auditPass",
                    type: 'POST',
                    data: JSON.stringify(claimCaseObject),
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            layer.msg(result.msg, {
                                icon: 1,
                                time: 2000,
                                shade: [0.0001, '#000']
                            }, function () {
                                var comeFrom = "${comeFrom}";
                                var checkCondition = "${checkCondition}";
                                let status;
                                if (checkCondition == "ls"){
                                    //理算
                                    status = 4;
                                }else if (checkCondition == "gs"){
                                    status = 3;
                                }
                                if (comeFrom == 2) {        // 内部审核
                                    window.location.href = "${ctx}/claimCaseObjectController/claimCaseObjectList4BS?comeFrom=${comeFrom}&checkCondition=${checkCondition}&status="+status;
                                }
                                if (comeFrom == 3) {        // 保司审核
                                    window.location.href = "${ctx}/claimCaseObjectController/claimCaseObjectList4BS?comeFrom=${comeFrom}&status=5&insCode=${claimCase.insCode}&checkCondition=${checkCondition}";
                                }
                            });
                        } else {
                            layer.msg(result.msg, {
                                icon: 2,
                                time: 2000,
                                shade: [0.0001, '#000']
                            },function(){
                                $("button[name='actionBtn']").removeAttr("disabled");
                            });
                        }

                    },
                    error: function (data) {
                        $("button[name='actionBtn']").removeAttr("disabled");
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                });
            }
        }

        var layerTop = top.layer;

        // 审核驳回
        function auditReject(status) {
            let claimCaseObject = assembleData(status);
            if (claimCaseObject) {
                layer.prompt({
                    formType: 2,        //0（文本）默认1（密码）2（文本域）
                    title: '请输入驳回原因',
                    fixed: false,
                    area: ['800px', '480px'],
                    closeBtn: 1,
                    yes: function (index, layero) {
                        let value = layero.find(".layui-layer-input").val().trim();
                        if (!value) {
                            layerTop.msg("驳回原因不能为空", {
                                icon: 2,
                                time: 2000
                            });
                            return false;
                        }
                        claimCaseObject["reason"] = value;
                        console.log(claimCaseObject, "对象数据");
                        $.ajax({
                            url: "${ctx}/claimCaseObjectController/auditReject",
                            type: 'POST',
                            data: JSON.stringify(claimCaseObject),
                            async: true,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 2000,
                                        shade: [0.0001, '#000']
                                    }, function () {
                                        var comeFrom = "${comeFrom}";
                                        var checkCondition = "${checkCondition}";
                                        //估损
                                        let status;
                                        if (checkCondition== "ls"){
                                            //理算
                                            status = 4;
                                        }else if (checkCondition == "gs"){
                                            status = 3;
                                        }
                                        if (comeFrom == 2) {        // 内部审核
                                            window.location.href = "${ctx}/claimCaseObjectController/claimCaseObjectList4BS?comeFrom=${comeFrom}&checkCondition=${checkCondition}&status="+status;
                                        }
                                        if (comeFrom == 3) {        // 保司审核
                                            window.location.href = "${ctx}/claimCaseObjectController/claimCaseObjectList4BS?comeFrom=${comeFrom}&status=5&insCode=${claimCase.insCode}&checkCondition=${checkCondition}";
                                        }
                                    });
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 2,
                                        time: 2000,
                                        shade: [0.0001, '#000']
                                    },function(){
                                        $("button[name='actionBtn']").removeAttr("disabled");
                                    });
                                }
                            },
                            error: function (data) {
                                $("button[name='actionBtn']").removeAttr("disabled");
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });
                    },
                    end: function () {
                        $("button[name='actionBtn']").removeAttr("disabled");
                    }
                });
            }
        }

        //全部同步
        function synchronousAll()
        {

            $.each($("div[name='dataCollectAreaRow']"), function (index, obj) {
                let code = $(this).attr("code");
                var lossAssessment = document.getElementById(code+"lossAssessment").value;
                var approvedAmount = lossAssessment;
                document.getElementById(code+"approvedAmount").value = approvedAmount;
                freshLossAssessment();
            });
        }

        //同
        function synchronousValue(code)
        {
            //估损  lossAssessment
            //核损  approvedAmount
            var lossAssessment = document.getElementById(code+"lossAssessment").value;
            var approvedAmount = lossAssessment;
            document.getElementById(code+"approvedAmount").value = approvedAmount;
            freshLossAssessment();
        }

        // 车牌号和车辆标识代码是否存在
        // 如果存在需要弹窗提醒已经存在的案件号
        $(function () {
            // 首次进入页面检查
            var carNumber = $('#carNumber').val();
            var carEncoding = $('#carEncoding').val();

            var carParams = {
                "carNumber": carNumber,
                "carEncoding": carEncoding,
                claimCaseNo: "${claimCaseObject.claimCaseNo}"
            };
            checkValue(JSON.stringify(carParams))

            // 失去焦点分别检查
            $('#carNumber').on('blur', function() {
                var carNumber = this.value;
                checkValue(JSON.stringify({carNumber, claimCaseNo: "${claimCaseObject.claimCaseNo}"}));
            });

            $('#carEncoding').on('blur', function() {
                var carEncoding = this.value;
                checkValue(JSON.stringify({carEncoding, claimCaseNo: "${claimCaseObject.claimCaseNo}"}));
            });

        });

        function checkValue(params) {
            var formData = new FormData();
            formData.append("params", params);
            $.ajax({
                url: '${ctx}/claimCaseObjectController/taskFieldCheck',
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function(data) {
                    data = JSON.parse(data);

                    var carNumberClaimCaseNo = data.carNumberClaimCaseNo ? data.carNumberClaimCaseNo.join(', ') : null;
                    var carEncodingClaimCaseNo = data.carEncodingClaimCaseNo ? data.carEncodingClaimCaseNo.join(', ') : null;

                    // 如果两个条件都满足，分别显示两个弹窗
                    if (carNumberClaimCaseNo && carEncodingClaimCaseNo) {
                        layer.open({
                            content: '车牌号码存在相同的案件号: ' + carNumberClaimCaseNo,
                            icon: 2,
                            yes: function(index, layero) {
                                // 点击确认按钮时,再显示第二个弹窗
                                layer.open({
                                    content: '车辆识别代码存在相同的案件号: ' + carEncodingClaimCaseNo,
                                    icon: 2,
                                });
                            }
                        });
                        // 如果只有车牌号码单独显示弹窗
                    } else if (carNumberClaimCaseNo) {
                        layer.open( {
                            content: '车牌号码存在相同的案件号: ' + carNumberClaimCaseNo,
                            icon: 2,
                        });
                        // 如果只有车辆识别代码单独显示弹窗
                    } else if (carEncodingClaimCaseNo) {
                        layer.open( {
                            content: '车辆识别代码存在相同的案件号: ' + carEncodingClaimCaseNo,
                            icon: 2,
                        });
                    }
                },
                complete: function(data) {
                    // 请求完成处理
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    alert("请求失败");
                }
            });
        }



    </script>
</head>


<body id="qc-Body" style="overflow-x:hidden;background: #fff;">
<div class="container-fluid">
    <div class="row">
        <form id="inputForm" onsubmit="return false">
            <div class="row">
                <div class="col-sm-8">
                    <div class="col-sm-12" style="margin-bottom: 5px;padding-left: 0px;padding-right: 0px;">
                        <div class="col-sm-3" style="padding-left: 0px;">
                            案件号：${claimCase.claimCaseNo}
                        </div>
                        <div class="col-sm-3" style="padding-left: 0px;">
                            姓名：${claimCase.treatName}
                        </div>
                        <div class="col-sm-3" style="padding-left: 0px;">
                            是否延迟报立案：<#if claimCase.delayReport?? && claimCase.delayReport == 1>是<#else >否</#if>
                        </div>
                        <div class="col-sm-3" style="padding-left: 0px;">
                            历史案件量：${historyCaseSize!'0'}个
                        </div>
                        <div class="col-sm-6 label-hide-overflow" style="padding-left: 0px;" title="${claimCase.productName}">
                            产品名称：${claimCase.productName}
                        </div>
                        <div class="col-sm-6" style="padding-left: 0px;">
                            起保时间：${(policyPerson.startDate?string["yyyy-MM-dd HH:mm:ss"])!''}
                        </div>
                        <div class="col-sm-6" style="padding-left: 0px;">
                            承保公司：
                            <#if claimCase.insCode == 'RB' || claimCase.insCode == 'HMRB'>
                                <span style="display:inline-block;background-color: #dbaf00;color: #fff;padding: 2px 6px;border-radius: 2px;">人保</span>
                            <#elseif claimCase.insCode == 'GY' || (claimCase.province == '辽宁省' && claimCase.treatDate?datetime gte "2023-09-29 00:00:00"?datetime) >
                                <span style="display:inline-block;background-color: #067ee5;color: #fff;padding: 2px 6px;border-radius: 2px;">国元</span>
                            <#elseif claimCase.insCode == 'DD'>
                                <span style="display:inline-block;background-color: #26c281;color: #fff;padding: 2px 6px;border-radius: 2px;">大地</span>
                            <#elseif claimCase.insCode == 'HX'>
                                <span style="display:inline-block;background-color: #ee5f5b;color: #fff;padding: 2px 6px;border-radius: 2px;">海峡</span>
                           </#if>
                        </div>
                        <div class="col-sm-6" style="padding-left: 0px;">
                            案件类型：
                            <#if claimCase.caseType == 'CP'>
                                <span style="display:inline-block;background-color: #067ee5;color: #fff;padding: 2px 6px;border-radius: 2px;">超赔</span>
                            <#elseif claimCase.caseType == 'YW'>
                                <span style="display:inline-block;background-color: #26c281;color: #fff;padding: 2px 6px;border-radius: 2px;">众包</span>
                            <#else>
                                <span style="display:inline-block;background-color: #26c281;color: #fff;padding: 2px 6px;border-radius: 2px;">雇主</span>
                            </#if>
                        </div>
                    </div>
                    <#-- 按钮 -->
                    <div class="col-sm-12" style="margin-bottom: 5px;padding-left: 0px;padding-right: 0px;">

                        <#--<div class="col-sm-2" style="padding-left: 0px;">
                            <div class="col-sm-12" style="padding-left: 0px;">
                                <button type="button" class="btn genTask btn-look" onclick="seePlanName('${product.id!''}')">
                                    查看产品方案
                                </button>
                            </div>
                        </div>-->
                        <div class="col-sm-2" style="padding-left: 0px;">
                            <button type="button" onclick="getHistroyCaseInfo('${claimCase.baseUserId}')" class="btn genTask">
                                历史案件
                            </button>
                        </div>
                        <#if claimCase.insCode != 'HX'>
                        <div class="col-sm-2">
                            <button type="button" class="btn genTask circulation_reasons" onclick="seeCaseProcessReason('${claimCase.id}')">查看案件流转原因</button>
                        </div>
                        </#if>
                        <div class="col-sm-2">
                            <button type="button" class="btn genTask circulation_reasons" onclick="onloadInteriorPolicy('${claimCase.policyPersonId}')">电子保单</button>
                        </div>
                        <#if claimCase.insCode != 'HX'>
                        <div class="col-sm-2">
                            <button type="button" class="btn genTask circulation_reasons" onclick="seeCaseProcessReasonAndLogs('${claimCase.id}')">案件日志</button>
                        </div>
                        </#if>
                        <#if claimCase.insCode != 'HX'>
                        <div class="col-sm-4">
                            <span class="pull-down" id="objectAppraisalAmount">估损金额：${objectAppraisalAmount!''}元</span>
                        </div>
                        </#if>
                    </div>
                    <#if rejectReason.firstRejectReason != "">
                    <div class="col-sm-12 clear-padding" style="color: red">
                        估损初审驳回原因：${rejectReason.firstRejectReason} （最新一次驳回原因）
                    </div>
                    </#if>
                    <#if rejectReason.companyRejectReason != "">
                    <div class="col-sm-12 clear-padding" style="color: red">
                        保司复核驳回原因：${rejectReason.companyRejectReason} （最新一次驳回原因）
                    </div>
                    </#if>
                </div>
                <div class="col-sm-4" style="margin-bottom: 5px;padding-left: 0px;padding-right: 0px;">
                    <#if !isShow>
                    <div class="col-sm-12 pull-left" style="margin: 20px auto;display: flex;">
                        <#if comeFrom == 1>
                            <!--采集按钮-->
                            <#if !readonly>
                                <div class="col-sm-3 col-sm-offset-3">
                                    <button name="actionBtn" style="background-color: #0597FF;" class="btn btn-lg" onclick="submitData('BAX21')">保存</button>
                                </div>
                                <div class="col-sm-3">
                                    <button name="actionBtn" style="background-color: #F49929;" class="btn btn-lg" onclick="submitData(<#if claimCaseObject.status == 'BAX27'>'BAX25'<#else>'BAX22'</#if>)">提审</button>
                                </div>
                            </#if>
                            <!--理算按钮-->
                            <#if !verifyReadonly>
                                <div class="col-sm-3 col-sm-offset-3">
                                    <button name="actionBtn" style="background-color: #0597FF;" class="btn btn-lg" onclick="submitData('BAX31')">保存</button>
                                </div>
                                <div class="col-sm-3">
                                    <button name="actionBtn" style="background-color: #F49929;" class="btn btn-lg" onclick="submitData(<#if claimCaseObject.status == 'BAX37'>'BAX35'<#else>'BAX32'</#if>)">提审</button>
                                </div>
                            </#if>
                        </#if>
                        <#if comeFrom == 2>
                            <#if claimCaseObject.status == "BAX22">
                                <div class="col-sm-3 col-sm-offset-3">
                                    <button name="actionBtn" style="background-color: #0597FF;" class="btn btn-lg" onclick="auditPass('BAX23')">通过</button>
                                </div>
                                <div class="col-sm-3">
                                    <button name="actionBtn" style="background-color: #F49929;" class="btn btn-lg" onclick="auditReject('BAX24')">驳回</button>
                                </div>
                            </#if>
                            <#if claimCaseObject.status == "BAX32">
                                <div class="col-sm-3 col-sm-offset-3">
                                    <button name="actionBtn" style="background-color: #0597FF;" class="btn btn-lg" onclick="auditPass('BAX33')">通过</button>
                                </div>
                                <div class="col-sm-3">
                                    <button name="actionBtn" style="background-color: #F49929;" class="btn btn-lg" onclick="auditReject('BAX34')">驳回</button>
                                </div>
                            </#if>
                        </#if>
                        <#if comeFrom == 3>
                            <#if claimCaseObject.status == "BAX25">
                                <div class="col-sm-3 col-sm-offset-3">
                                    <button name="actionBtn" style="background-color: #0597FF;" class="btn btn-lg" onclick="auditPass('BAX26')">通过</button>
                                </div>
                                <div class="col-sm-3">
                                    <button name="actionBtn" style="background-color: #F49929;" class="btn btn-lg" onclick="auditReject('BAX27')">驳回</button>
                                </div>
                            </#if>
                            <#if claimCaseObject.status == "BAX35">
                                <div class="col-sm-3 col-sm-offset-3">
                                    <button name="actionBtn" style="background-color: #0597FF;" class="btn btn-lg" onclick="auditPass('BAX36')">通过</button>
                                </div>
                                <div class="col-sm-3">
                                    <button name="actionBtn" style="background-color: #F49929;" class="btn btn-lg" onclick="auditReject('BAX37')">驳回</button>
                                </div>
                            </#if>
                        </#if>
                        <#--   <div class="col-sm-4">
                               <button style="background-color: #009F9F;color: white" class="exportBtn btn btn-lg">导出</button>
                           </div>-->

                    </div>
                    </#if>
                </div>
            </div>
            <!-- 左部分 -->
            <div class="col-sm-8" style="background: none!important;padding-right: 0px !important;">
                <#--审核头-->
                <div class="row">
                    <#-- 展示图片区域以及采集数据tbale -->
                    <div class="col-sm-12" style="padding: 5px 0px 0px 0px; background: none;">
                        <div class="row" style="height: 1050px; overflow: hidden;">
                            <div class="col-sm-1" style="height: 1050px;background-color: rgba(226, 226, 226, 0.5);padding-left: 0px!important;padding-right: 10px!important; border-right: groove;">
                                <#if claimAttachList?exists && (claimAttachList?size>0)>
                                    <div id="thumbnail" style="height: 1050px;<#if (claimAttachList?size>11)>overflow-y: scroll;</#if>overflow-x:hidden">
                                        <ol style="padding-left:25px !important;/*padding-right: 15px !important;*/">
                                            <#list claimAttachList as attach>
                                                <li style="width:auto; height:auto; cursor:pointer;text-align: center;padding-top:5px">
                                                    <img id="thumbnail-${attach.id}" class="thumbnail-img <#if attach_index == 0>selected-thumbnail-img</#if>"
                                                         data-fileid="${attach.id}" data-img-number=''
                                                         title="${attach.fileName}"
                                                         src="${attach.fileObjectId}" onerror="javascript:this.src='/a/job_done.png'"/>
                                                </li>
                                            </#list>
                                        </ol>
                                    </div>
                                </#if>
                            </div>
                            <div class="col-sm-11" style="height: 1300px;">
                                <ul id="images">
                                    <#list claimAttachList as attach>
                                        <li hidden="hidden">
                                            <img data-fileid="${attach.id}"
                                                 title="${attach.fileName}"
                                                 src="${attach.fileObjectId}"
                                                 alt="${attach.id}"/>
                                        </li>
                                    </#list>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 右部分 -->
            <div class="col-sm-4" style="padding-left: 15px;">
                <div class="row" style="font-size: 15px;margin: 9px 0px;display: flex;align-items: center;">
                    <div class="col-sm-3 clear-padding" style="text-align: right">估损清单名称</div>
                    <div class="col-sm-9 ">
                        <input id="objectName" type="text" class="form-control" title="${claimCaseObject.name!''}" value="${claimCaseObject.name!''}" <#if readonly?? >readonly</#if>>
                    </div>
                </div>
                <#if claimCaseObject.category == 3>
                <div class="row" style="margin-bottom: 20px">
                    <div style="margin-left: 5px;">
                        <img class="marginLeft15" src="${ctx}/images/u48.svg" align="left" style="margin-top: 3px;"/>
                        <span style="font-size: 16px;font-weight: bold;margin-left: 7px">机动车基本信息 </span>
                    </div>
                    <div name="dataCollectArea" class="row">
                        <div class="col-sm-12 clear-padding">
                            <div class="col-sm-6 clear-padding align-item-center" style="margin: 7px 0px;">
                                <div class="col-sm-4 clear-padding label-right">车牌号码</div>
                                <div class="col-sm-8 ">
                                    <input type="text" validName="billCollection" id="carNumber" data-valid="none" name="carNumber" title="${claimCaseObject.carNumber!''}" value="${claimCaseObject.carNumber!''}" class="form-control" <#if readonly?? >readonly</#if> />
                                </div>
                            </div>
                            <div class="col-sm-6 clear-padding align-item-center" style="margin: 7px 0px;">
                                <div class="col-sm-4 clear-padding label-right" >厂牌型号</div>
                                <div class="col-sm-8 ">
                                    <input type="text" validName="billCollection" id="carModel" data-valid="none" name="carModel" title="${claimCaseObject.carModel!''}" value="${claimCaseObject.carModel!''}" class="form-control" <#if readonly?? >readonly</#if> />
                                </div>
                            </div>
                            <div class="col-sm-6 clear-padding align-item-center" style="margin: 7px 0px;">
                                <div class="col-sm-4 clear-padding label-right" >车辆识别代码</div>
                                <div class="col-sm-8 ">
                                    <input type="text" validName="billCollection" id="carEncoding" data-valid="none" name="carEncoding" title="${claimCaseObject.carEncoding!''}" value="${claimCaseObject.carEncoding!''}" class="form-control" <#if readonly?? >readonly</#if> />
                                </div>
                            </div>
                            <div class="col-sm-6 clear-padding align-item-center" style="margin: 7px 0px;">
                                <div class="col-sm-4 clear-padding label-right">初次登记日期</div>
                                <div class="col-sm-8 ">
<!--                                    <input readonly type="text" class="form-control" validName="billCollection" data-valid="none" id="firstRegistrationTime" name="firstRegistrationTime" title="${(claimCaseObject.firstRegistrationTime?string('yyyy-MM-dd'))!''}" value="${(claimCaseObject.firstRegistrationTime?string('yyyy-MM-dd'))!''}" <#if readonly?? >disabled</#if> />-->
                                    <input readonly type="text"  class="form-control" data-valid="none" name="firstRegistrationTime" id="firstRegistrationTime" placeholder="yyyy-mm-dd" value="${(claimCaseObject.firstRegistrationTime?string('yyyy-MM-dd'))!''}" <#if readonly?? >disabled</#if> />

                                </div>
                            </div>
                            <div class="col-sm-6 clear-padding align-item-center" style="margin: 7px 0px;">
                                <div class="col-sm-4 clear-padding label-right">行驶证车主</div>
                                <div class="col-sm-8 ">
                                    <input type="text" validName="billCollection" id="treatName" name="treatName" data-valid="none" title="${claimCaseObject.treatName!''}" value="${claimCaseObject.treatName!''}" class="form-control" <#if readonly?? >readonly</#if> />
                                </div>
                            </div>
                            <div class="col-sm-6 clear-padding align-item-center" style="margin: 7px 0px;">
                                <div class="col-sm-4 clear-padding label-right" >定损时间</div>
                                <div class="col-sm-8 ">
<!--                                    <input type="text" validName="billCollection" data-valid="none" id="lossAssessmentTime" name="lossAssessmentTime" title="${(claimCaseObject.lossAssessmentTime?string('yyyy-MM-dd'))!''}" value="${(claimCaseObject.lossAssessmentTime?string('yyyy-MM-dd'))!''}" class="form-control" <#if readonly?? >disabled</#if> />-->
                                    <input readonly type="text"  class="form-control" data-valid="none" name="lossAssessmentTime" id="lossAssessmentTime" placeholder="yyyy-mm-dd" value="${(claimCaseObject.lossAssessmentTime?string('yyyy-MM-dd'))!''}" <#if readonly?? >disabled</#if> />

                            </div>
                            </div>
                            <div class="col-sm-6 clear-padding align-item-center" style="margin: 7px 0px;">
                                <div class="col-sm-4 clear-padding label-right" >修理厂</div>
                                <div class="col-sm-8 ">
                                    <input type="text" validName="billCollection" id="repairFactory" data-valid="none" name="repairFactory" title="${claimCaseObject.repairFactory!''}" value="${claimCaseObject.repairFactory!''}" class="form-control" <#if readonly?? >readonly</#if> />
                                </div>
                            </div>
                            <div class="col-sm-6 clear-padding align-item-center" style="margin: 7px 0px;">
                                <div class="col-sm-4 clear-padding label-right">是否4S</div>
                                <div class="col-sm-8 ">
                                    <select validName="billCollection" class="kp-select2 inputStyle form-control" data-valid="none" id="isServiceShop" name="isServiceShop" <#if readonly?? >disabled</#if>>
                                        <option value="是" <#if claimCaseObject.isServiceShop='是' >selected</#if>>是</option>
                                        <option value="否" <#if claimCaseObject.isServiceShop='否' >selected</#if>>否</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                </#if>

                <div class="row" style="margin-bottom: 20px;">
                    <div style="margin-left: 5px;">
                        <img class="marginLeft15" src="${ctx}/images/u48.svg" align="left" style="margin-top: 3px;"/>
                        <span style="font-size: 16px;font-weight: bold;margin-left: 7px">维修定损 </span>
                        <#if readonly>
                            <#if comeFrom == 2>
                                <#if !claimCaseObject.status?starts_with("BAX3") || !claimCaseObject.status == "BAX40" || !claimCaseObject.status == "BAX99">
                                <button onclick="synchronousAll()" class="btn blue" style="margin-top: 3px;display:inline-block;background-color: #067ee5;color: #fff;padding: 2px 6px;border-radius: 2px;">全部同步</button>
                                </#if>
                            </#if>
                        </#if>
                    </div>
                    <div name="dataCollectArea" class="row" style="height:500px;overflow-y: scroll;overflow-x: hidden;">
                        <#assign pjIndex = 0>
                        <#assign rgIndex = 0>
                        <#if enumMapByParentCode?exists>
                            <#list enumMapByParentCode.keySet() as code>
                                <div class="row" name="dataCollectAreaSubjet" code="${code}" name="${enumMapByParentCode.get(code)}">
                                    <div class="col-sm-8" style="margin: 15px;">
                                        <div class="subject-name">${enumMapByParentCode.get(code)}</div>
                                    </div>
                                    <#if !readonly>
                                        <div class="col-sm-12 align-item-center">
                                            <div class="col-sm-1 clear-padding" style="font-size: 15px;margin: 9px 0px;"></div>
                                            <div class="col-sm-10 clear-padding">
                                                <div class="col-sm-12 clear-padding-left align-item-center" style="margin-bottom: 5px;">
                                                    <div class="col-sm-1 clear-padding" >名称</div>
                                                    <div class="col-sm-11 clear-padding-right">
                                                        <input type="text" style="padding: 0px !important;" class="form-control" disabled/>
                                                    </div>
                                                </div>
                                                <div class="col-sm-4 clear-padding align-item-center">
                                                    <div class="col-sm-3 clear-padding">估损</div>
                                                    <div class="col-sm-9 ">
                                                        <input type="text" style="padding: 0px !important;" class="form-control" disabled/>
                                                    </div>
                                                </div>

                                                <div class="col-sm-4 clear-padding align-item-center">
                                                    <div class="col-sm-3 clear-padding" >核损</div>
                                                    <div class="col-sm-9 ">
                                                        <input type="text" style="padding: 0px !important;" class="form-control" disabled/>
                                                    </div>
                                                </div>

                                                <div class="col-sm-4 clear-padding align-item-center">
                                                    <div class="col-sm-3 clear-padding" >理算</div>
                                                    <div class="col-sm-9 ">
                                                        <input type="text" style="padding: 0px !important;" class="form-control" disabled/>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-sm-1 clear-padding">
                                                <div class="icon-plus" id="${code}AddDataRow" onclick="addDataRowClick('${code}')"></div>
                                            </div>
                                        </div>
                                    </#if>
                                    <div class="row" id="${code}collectArea">
                                        <#list objectAssessmentList as assessment>
                                            <#if assessment.code == code>
                                                <div class="col-sm-12 align-item-center" style="margin-top: 15px" name="dataCollectAreaRow"  code="${assessment.id}">
                                                    <input type="hidden" name="id" class="form-control" value="${assessment.id}">
                                                    <div class="col-sm-1 clear-padding" style="text-align: left;">
                                                        <div class="clear-padding line-center">
                                                            <#switch code>
                                                                <#case "2-1">
                                                                    <#assign pjIndex = pjIndex + 1>
                                                                    ${pjIndex}
                                                                    <#break >
                                                                <#case "2-2">
                                                                    <#assign rgIndex = rgIndex + 1>
                                                                    ${rgIndex}
                                                                    <#break >
                                                            </#switch>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-10 clear-padding">
                                                        <div class="col-sm-12 clear-padding-left align-item-center" style="margin-bottom: 5px;">
                                                            <div class="col-sm-1 clear-padding">名称</div>
                                                            <div class="col-sm-11 clear-padding-right">
                                                                <input type="text" validname="billCollection" data-valid="isEmpty" name="name" class="form-control clear-padding" value="${assessment.name!''}" title="${assessment.name!''}" <#if readonly?? >readonly</#if>>
                                                            </div>
                                                            <#if readonly>
                                                                <#if comeFrom == 2>
                                                                    <#if !claimCaseObject.status?starts_with("BAX3") || !claimCaseObject.status == "BAX40" || !claimCaseObject.status == "BAX99">
                                                                    <button onclick="synchronousValue('${assessment.id}')" class="btn blue" style="display:inline-block;background-color: #067ee5;color: #fff;padding: 2px 6px;border-radius: 2px;">同</button>
                                                                    </#if>
                                                                </#if>
                                                            </#if>
                                                        </div>
                                                        <div class="col-sm-4 clear-padding align-item-center">
                                                            <div class="col-sm-3 clear-padding">估损</div>
                                                            <div class="col-sm-9 ">
                                                                <input type="text"  id="${assessment.id}lossAssessment" validname="billCollection" name="lossAssessment" data-valid="isNumberAndDecimalPoint isEmpty" class="form-control clear-padding" value="${assessment.lossAssessment!''}" title="${assessment.lossAssessment!''}" <#if readonly?? >readonly</#if>>
                                                            </div>
                                                        </div>
                                                        <div class="col-sm-4 clear-padding align-item-center">
                                                            <div class="col-sm-3 clear-padding">核损</div>
                                                            <div class="col-sm-9 ">
                                                                <input type="text"  id="${assessment.id}approvedAmount"  validname="billCollection" name="approvedAmount" data-valid="isNumberAndDecimalPoint isEmpty" class="form-control clear-padding" value="${assessment.approvedAmount!''}" title="${assessment.approvedAmount!''}" <#if  claimCaseObject.status != "BAX22" && claimCaseObject.status != "BAX25">readonly</#if>>
                                                            </div>
                                                        </div>
                                                        <div class="col-sm-4 clear-padding align-item-center">
                                                            <div class="col-sm-3 clear-padding">理算</div>
                                                            <div class="col-sm-9 ">
                                                                <input type="text" validname="billCollection" name="verifyAmount" data-valid="isNumberAndDecimalPoint isEmpty" class="form-control clear-padding" value="${assessment.verifyAmount!''}" title="${assessment.verifyAmount!''}" <#if verifyReadonly??>readonly</#if>>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <#if !readonly>
                                                        <div class="col-sm-1 clear-padding">
                                                            <img src="/images/shanchu.svg" class="dataDisplayArea-head-img" onclick="delDataRow(this,'${assessment.code}')">
                                                        </div>
                                                    </#if>
                                                </div>
                                            </#if>
                                        </#list>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-12" style="margin: 10px;">
                                            <span style="font-size: 15px;float:right;color: red;font-weight: bold" id="${code}LossAssessment"></span>
                                        </div>
                                    </div>
                                </div>
                            </#list>
                        </#if>
                        <div class="row">
                            <div style="margin: 15px;display: flex;align-items: center;">
                                <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">估损描述</div>
                                <div class="col-sm-9 clear-padding-left">
                                    <input validName="billCollection" type="text" id="objectRemark" name="objectRemark" class="form-control" title="${claimCaseObject.remark!''}" value="${claimCaseObject.remark!''}"  data-valid="none" <#if readonly?? >readonly</#if>>
                                </div>
                            </div>
                            <#if claimCaseObject.category == 3 >
                            <div style="margin: 15px;display: flex;align-items: center;">
                                <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">残值</div>
                                <div class="col-sm-9 clear-padding-left">
                                    <input validName="billCollection" id="residualValue" name="residualValue" type="text" class="form-control" value="${claimCaseObject.residualValue!''}" title="${claimCaseObject.residualValue!''}" data-valid="isNumberAndDecimalPoint isEmpty" <#if readonly??>readonly</#if>/>
                                </div>
                            </div>
                            <div style="margin: 15px;display: flex;align-items: center;">
                                <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">残值核损金额</div>
                                <div class="col-sm-9 clear-padding-left">
                                    <input validName="billCollection" id="residualNuclearLossValue" name="residualNuclearLossValue" type="text" class="form-control" value="${claimCaseObject.residualNuclearLossValue!''}" title="${claimCaseObject.residualNuclearLossValue!''}" data-valid="isNumberAndDecimalPoint isEmpty" <#if claimCaseObject.status != "BAX22" && claimCaseObject.status != "BAX25">readonly</#if> />
                                </div>
                            </div>
                            </#if>
                            <div style="margin: 15px;display: flex;align-items: center;">
                                <span style="font-size: 17px;color: red;font-weight: bold" id="lossAssessmentSum" sumMoney="${(claimCaseObject.lossAssessmentSum?string('0.00'))!''}">维修估损总金额：${(claimCaseObject.lossAssessmentSum?string('0.00'))!''} 元</span>
                            </div>
                            <div style="margin: 15px;display: flex;align-items: center;">
                                <span style="font-size: 17px;color: red;font-weight: bold" id="nuclearLossSum" sumMoney="${(claimCaseObject.nuclearLossSum?string('0.00'))!''}">核损合计：${(claimCaseObject.nuclearLossSum?string('0.00'))!''} 元</span>
                            </div>
                        </div>

                        <!--理算部分-->
                        <#if claimCaseObject.status?starts_with("BAX3") || claimCaseObject.status == "BAX40" || claimCaseObject.status == "BAX99">
                            <div>
                                <div class="row">
                                    <div style="margin: 15px;display: flex;align-items: center;">
                                        <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">责任比例</div>
                                        <div class="col-sm-4 clear-padding-left">
                                            <select type="text" class="form-control" id="accidentLiability" name="accidentLiability" disabled >
                                                <option value="全责" <#if claimCaseObject.accidentLiability == "全责">selected</#if> data-dutyRate="100" >全责</option>
                                                <option value="主责" <#if claimCaseObject.accidentLiability == "主责">selected</#if> data-dutyRate="70" >主责</option>
                                                <option value="同责" <#if claimCaseObject.accidentLiability == "同责">selected</#if> data-dutyRate="50" >同责</option>
                                                <option value="次责" <#if claimCaseObject.accidentLiability == "次责">selected</#if> data-dutyRate="30" >次责</option>
                                                <option value="无责" <#if claimCaseObject.accidentLiability == "无责">selected</#if> data-dutyRate="0" >无责</option>
                                                <option value="责任待确认" <#if claimCaseObject.accidentLiability == "责任待确认">selected</#if> data-dutyRate="" >责任待确认</option>
                                            </select>
                                        </div>
                                        <div class="col-sm-4 clear-padding-left">
                                            <input validName="billCollection" placeholder="赔付比例" title="赔付比例" id="dutyRate" type="text" class="form-control" value="${claimCaseObject.dutyRate!claimCaseObject.accidentProportion}" data-valid="isRate isEmpty" <#if verifyReadonly??>readonly</#if>/>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div style="margin: 15px;display: flex;align-items: center;">
                                        <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">保单号</div>
                                        <div class="col-sm-9 clear-padding-left">
                                            <input validName="billCollection" id="policyNo" type="text" class="form-control" value="${claimCaseObject.policyNo!''}" title="${claimCaseObject.policyNo!''}" data-valid="isEmpty" <#if verifyReadonly??>readonly</#if>/>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div style="margin: 15px;display: flex;align-items: center;">
                                        <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">扣减费用</div>
                                        <div class="col-sm-9 clear-padding-left">
                                            <input validName="billCollection" id="deductFee" type="text" class="form-control" value="${claimCaseObject.deductFee!''}" title="${claimCaseObject.deductFee!''}" data-valid="isNumberAndDecimalPoint isEmpty" <#if verifyReadonly??>readonly</#if>/>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div style="margin: 15px;display: flex;align-items: center;">
                                        <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">理算金额</div>
                                        <div class="col-sm-9 clear-padding-left">
                                            <input validName="billCollection" id="verifyAmout" type="text" class="form-control" value="${claimCaseObject.verifyAmout!''}" title="${claimCaseObject.verifyAmout!''}" data-valid="isNumberAndDecimalPoint isEmpty" <#if verifyReadonly??>readonly</#if>/>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div style="margin: 15px;display: flex;align-items: center;">
                                        <div class="col-sm-3 clear-padding" style="font-size: 17px;font-weight: bold;">理算描述</div>
                                        <div class="col-sm-9 clear-padding-left">
                                            <input validName="billCollection" id="verifyDetail" type="text" class="form-control" value="${claimCaseObject.verifyDetail!''}" title="${claimCaseObject.verifyDetail!''}" data-valid="none" <#if verifyReadonly??>readonly</#if>/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </#if>
                    </div>
                </div>
                <#if claimCaseObject.status?starts_with("BAX3") || claimCaseObject.status == "BAX40" || claimCaseObject.status == "BAX99" >
                <div class="row" name="claimCaseObjectPayment">
                    <div class="row" style="margin-left: 5px">
                        <div class="col-sm-3 clear-padding">
                            <img class="marginLeft15" src="${ctx}/images/u48.svg" align="left" style="margin-top: 3px;"/>
                            <span style="font-size: 16px;font-weight: bold;margin-left: 7px">划款信息 </span>
                        </div>
                        <#if !verifyReadonly?? && !isShow??>
                        <div class="col-sm-1 clear-padding">
                            <div class="icon-plus" id="addPaymentRow" onclick="addPaymentRow()" ></div>
                        </div>
                        </#if>
                    </div>
                    <div id="paymentCollectArea" name="paymentCollectArea" class="row" style="height:300px;overflow-y: scroll;overflow-x: hidden;">
                    </div>
                </div>
                </#if>
            </div>
        </form>
    </div>
</div>

</body>
</html>
