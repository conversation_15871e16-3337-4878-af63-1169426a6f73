<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        const loader =  new Loaders({style:"rectangle"});
        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        $(document).ready(function () {

            // 业务标签 select2初始化
            $("#type-category").select2({
                placeholder: "请选择",
                width: null
            });
            // 保司标签 select2初始化
            $("#insCode").select2({
                placeholder: "请选择",
                width: null
            });

            $("body").on("change", "select[name='type-category']", function() {
                var value = $(this).val();
                $("input[name='type']").val(value.split("-")[0]);
                $("input[name='category']").val(value.split("-")[1]);
            });


        });

        
        function statusSwitch(status) {
            $("#status").val(status);
            $("#searchForm").submit();
        }

        function startTask(id) {
            window.location.href = "${ctx}/claimCaseObjectController/startTask?comeFrom=${claimCaseObjectVo.comeFrom}&id="+id;
        }
        function showObject(id) {
            window.location.href = "${ctx}/claimCaseObjectController/showObject?id="+id;
        }
        function showCase(claimCaseId) {
            window.open("${ctx}/claimCaseController/caseDetail?caseId="+claimCaseId);
        }

        function showProcessingDescription(processingDescription, e) {
            layer.tips(processingDescription,$(e), {shadeClose: true, time: 0, area: 'auto'});
        }

        function closeTips() {
            layer.closeAll();
        }

        //分配人员
        function bindPerson(id){
            layer.open({
                title: "人员任务分配",
                type: 2,
                area: ['900px','500px'],
                offset: 'auto',
                fix: false, //不固定
                maxmin: true,
                scrollbar: false,
                content: '${ctx}/claimCaseDistributionController/getBindPersonLayer4BS?id='+id,
                success: function (layero, index) {
                    // layer.iframeAuto(index);
                }
            });
        }

    </script>
    <style>

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
            border-right: 0;
            overflow: hidden;
            vertical-align: bottom;
            padding: 0px;
            display: inline-block;
        }
        .form-active > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
        }
        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 70px 6px 30px;
            font-size: 17px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
            border-right: 1px solid #e7ecf1;
        }
        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }
        td > a {
            display: inline-block;
            margin: 3px;
        }

        .li-blue {
            background: #0b94ea ;
            color: #fff;
        }

        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }
    </style>
</head>
<body>

<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>饿了么</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">估损预处理</span></li>
                </ul>
            </div>
            <div class="portlet-body">

                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal" action="${ctx}/claimCaseObjectController/objectPretreatmentList"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="claimCaseNo" id="claimCaseNo"
                                               value="${claimCaseObjectVo.claimCaseNo}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人姓名：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatName" id="treatName"
                                               value="${claimCaseObjectVo.treatName}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人身份证：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatIdNum" id="treatIdNum"
                                               value="${claimCaseObjectVo.treatIdNum}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">估损单类型：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                       <select class="form-control" name="type-category" id="type-category" >
                                           <option value=" ">请选择</option>
                                           <#if typeCategoryMap??>
                                               <#list typeCategoryMap.keySet() as key>
                                                   <option value="${key}" <#if key == claimCaseObjectVo.type + "-" + claimCaseObjectVo.category> selected </#if>>${typeCategoryMap.get(key)}</option>
                                               </#list>
                                           </#if>
                                       </select>
                                       <input type="hidden" name="type" value="${claimCaseObjectVo.type}">
                                       <input type="hidden" name="category" value="${claimCaseObjectVo.category}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">保司标识：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <select class="form-control" name="insCode" id="insCode" >
                                            <option value=" ">请选择</option>
                                            <option value="DD" <#if claimCaseObjectVo.insCode == "DD"> selected </#if>>DD</option>
                                            <option value="GY" <#if claimCaseObjectVo.insCode == "GY"> selected </#if>>GY</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group pull-right" style="margin-bottom: 10px;">
                                    <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;margin-right: 20px;">查询</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-active">
                        <div>
                            <input type="hidden" name="status" id="status" value="${claimCaseObjectVo.status}">
                            <input type="hidden" name="comeFrom" id="comeFrom" value="${claimCaseObjectVo.comeFrom}">
                            <ul style="padding: 0px;margin: 0px;display: flex;">
                                <li class="li-default <#if claimCaseObjectVo.status == 0>li-blue</#if>" onclick="statusSwitch(0)">估损预处理</li>
                            </ul>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th width="10%">报案号</th>
                        <th width="8%">出险人姓名</th>
                        <th width="10%">报案时间</th>
                        <th width="10%">出险时间</th>
                        <th width="10%">预处理时间</th>
                        <th width="6%">承保公司</th>
                        <th width="10%">标签</th>
                        <th width="8%">估损金额</th>
                        <th width="8%">赔付对象类型</th>
                        <th width="10%">创建时间</th>
                        <th width="20%">功能</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as vo>
                        <tr>
                            <td title="">${vo.claimCaseNo}</td>
                            <td title="">${vo.treatName}</td>
                            <td title="${(vo.startDate?string["yyyy-MM-dd HH:mm:ss"])!'-'}">${(vo.startDate?string["yyyy-MM-dd HH:mm:ss"])!'-'}</td>
                            <td title="${(vo.treatDate?string["yyyy-MM-dd HH:mm:ss"])!'-'}">${(vo.treatDate?string["yyyy-MM-dd HH:mm:ss"])!'-'}</td>
                            <td style="cursor: pointer;" <#if vo.processingTime??>onmouseover="showProcessingDescription('${vo.processingDescription}', this)" onmouseout="closeTips()"</#if>>
                               ${(vo.processingTime?string['yyyy-MM-dd HH:mm:ss'])!''}
                            </td>
                            <td>
                                <#if vo.insCode == 'RB' || vo.insCode == 'HMRB'>
                                    <span style="display:inline-block;background-color: #dbaf00;color: #fff;padding: 2px 6px;border-radius: 2px;">人保</span>
                                <#elseif vo.insCode == 'GY' || (vo.province == '辽宁省' && vo.treatDate?datetime gte "2023-09-29 00:00:00"?datetime) >
                                    <span style="display:inline-block;background-color: #067ee5;color: #fff;padding: 2px 6px;border-radius: 2px;">国元</span>
                                <#elseif vo.insCode == 'DD'>
                                    <span style="display:inline-block;background-color: #26c281;color: #fff;padding: 2px 6px;border-radius: 2px;">大地</span>
                                <#elseif vo.insCode == 'HX'>
                                    <span style="display:inline-block;background-color: #ee5f5b;color: #fff;padding: 2px 6px;border-radius: 2px;">海峡</span>
                                <#elseif vo.insCode == 'JZTB'>
                                    <span style="display:inline-block;background-color: #f78c45;color: #fff;padding: 2px 6px;border-radius: 2px;">太保</span>
                                </#if>
                            </td>
                            <td class="labelGroup">
                                <#if vo.label??>
                                    <#list vo.label.split(",") as key>
                                        <#if (key?trim)!="">
                                            <span class="${key} span-type" style="color: ${labelShowMap.get(key).fontColor};background-color: ${labelShowMap.get(key).color}"><#if labelShowMap.get(key)??>${labelShowMap.get(key).msg}<#else>${key}</#if></span>
                                        </#if>
                                    </#list>
                                </#if>
                            </td>
                            <td title="${(vo.estimatedApprovedMoney?string(',##0.00'))!''}">${(vo.estimatedApprovedMoney?string(',##0.00'))!''}</td>
                            <td>
                                <#switch vo.type + "-" + vo.category>
                                    <#case "1-1">
                                        骑手人伤
                                        <#break >
                                    <#case "2-1">
                                        三者人伤
                                        <#break >
                                    <#case "2-2">
                                        三者物损
                                        <#break >
                                    <#case "2-3">
                                        三者车损
                                        <#break >
                                </#switch>
                            </td>
                            <td>${vo.createTime?string('yyyy-MM-dd HH:mm:ss')}</td>
                            <td>
                                <a href="#" onclick="showObject('${vo.id}')">查看</a>
                                <a href="#" onclick="showCase('${vo.claimCaseId}')">查看案件</a>
                                <@shiro.hasPermission name="CLAIM_CASE_OBECT_DISTRIBUTION_AUDITER">
                                    <a onclick="bindPerson('${vo.claimCaseId}')">分配人员</a>
                                </@shiro.hasPermission>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>