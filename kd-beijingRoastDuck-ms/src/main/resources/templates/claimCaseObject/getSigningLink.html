<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        window.onload = function () {
            // 获取后端传递过来的 error 信息
            const errorMessage = "${error}";
            if (errorMessage) {
                alert(errorMessage);
            }
        };

        // 旧浏览器兼容方法
        function copyURL(text) {
            // 创建临时 input 元素
            const tempInput = document.createElement('input');
            tempInput.value = text;
            document.body.appendChild(tempInput);

            // 选中内容并执行复制
            tempInput.select();
            tempInput.setSelectionRange(0, 99999); // 兼容移动端
            const successful = document.execCommand('copy');

            if (successful) {
                alert('链接已复制到剪贴板');
                console.log('URL 复制成功');
            } else {
                console.error('复制失败，请手动复制');
            }
            // 移除临时元素
            document.body.removeChild(tempInput);
        }

    </script>
    <style>

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
            border-right: 0;
            overflow: hidden;
            vertical-align: bottom;
            padding: 0px;
            display: inline-block;
        }
        .form-active > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
        }
        td > a {
            display: inline-block;
            margin: 3px;
        }
        /* 新增的样式来处理长链接换行 */
        td {
            table-layout: fixed;
            overflow: hidden;
            word-wrap: break-word;
        }
        td[title^="http"] {
            max-width: 15ch;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    </style>
</head>
<body>

<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-body">
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>

                        <th width="15%">签字模板名称</th>
                        <th width="40%">签署长链接</th>
                        <th width="25%">签署短链接</th>
                        <th width="20%">功能</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#if jsonList?exists>
                    <#list jsonList as item>
                        <tr>
                            <td width="15%" title="${item.fileName!'-'}">${item.fileName!'-'}</td>
                            <td width="40%" title="${item.url!'-'}">${item.url!'-'}</td>
                            <td width="25%" title="${item.shortUrl!'-'}">${item.shortUrl!'-'}</td>
                            <td width="20%">
                                <a href="#" onclick="copyURL('${item.url}')">点击复制长链接</a>
                                <a href="#" onclick="copyURL('${item.shortUrl}')">点击复制短链接</a>
                            </td>
                        </tr>
                    </#list>
                    </#if>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
</body>
</html>