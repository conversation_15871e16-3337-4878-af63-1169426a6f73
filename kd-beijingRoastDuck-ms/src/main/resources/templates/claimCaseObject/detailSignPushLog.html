<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">

        function rePushSign(id) {
            parent.window.location.href = "${ctx}/signTemplateController/signDetail4log?pushLogId="+id+"&isCanPush=true";
        }
        function signDetail(id) {
            parent.window.location.href = "${ctx}/signTemplateController/signDetail4log?pushLogId="+id;
        }

        //查看文件信息
        function seeFileUrl(id,type) {
            console.log(id,type);
            if(id){
                let formData = new FormData();
                formData.append("signCenterId",id)
                formData.append("type",type)
                $.ajax({
                    url: "${ctx}/api/signTemplateApi/signGetFileUrl",
                    type: 'POST',
                    data: formData,
                    async: false,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            window.open(result.url, "_blank");
                        } else {
                            layer.msg(result.msg, {icon: 2, time: 1500, offset: 'auto'}, function (index) {
                                layer.close(index);
                            });
                        }
                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                    }
                });
            }else {
                layer.msg("查看签字文件数据异常请联系管理员！！！！", {icon: 2, time: 1500, offset: 'auto'}, function (index) {
                    layer.close(index);
                });
            }
        }

    </script>
    <style>

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
            border-right: 0;
            overflow: hidden;
            vertical-align: bottom;
            padding: 0px;
            display: inline-block;
        }
        .form-active > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
        }
        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 70px 6px 30px;
            font-size: 17px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
            border-right: 1px solid #e7ecf1;
        }
        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }
        td > a {
            display: inline-block;
            margin: 3px;
        }

        .li-blue {
            background: #0b94ea ;
            color: #fff;
        }

        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }
        .td-overflow {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 200px;
        }
    </style>
</head>
<body>

<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-body">
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                        <thead>
                        <tr>
                            <th width="15%">案件号</th>
                            <th width="20%">签字模板名称</th>
                            <th width="15%">推送状态</th>
                            <th width="20%">相关信息</th>
                            <th width="10%">推送时间</th>
                            <th width="20%">功能</th>
                        </tr>
                        </thead>
                        <tbody>
                        <#if signTemplatePushLogList?exists>
                        <#list signTemplatePushLogList as vo>
                            <tr>
                                <td width="15%" title="${vo.claimCaseNo!'-'}">${vo.claimCaseNo!'-'}</td>
                                <td width="20%" title="${vo.signTemplateName!'-'}">${vo.signTemplateName!'-'}</td>
                                <td width="15%">
                                    <#switch vo.status>
                                        <#case -1>
                                            已关闭
                                            <#break >
                                        <#case 0>
                                            推送失败
                                            <#break >
                                        <#case 1>
                                            推送成功
                                            <#break >
                                        <#case 2>
                                            生成成功
                                            <#break >
                                        <#case 3>
                                            生成失败
                                            <#break >
                                        <#case 4>
                                            发起签署成功
                                            <#break >
                                        <#case 5>
                                            发起签署失败
                                            <#break >
                                        <#case 6>
                                            签署完成
                                            <#break >
                                        <#case 7>
                                            签署已撤销
                                            <#break >
                                        <#case 8>
                                            签署已过期
                                            <#break >
                                        <#case 9>
                                            签署已拒签
                                            <#break >
                                    </#switch>
                                </td>
                                <td width="20%" style="overflow-x: hidden" class="td-overflow" title='${vo.errorMsg!"-"}'>
                                    ${vo.errorMsg!'-'}
                                </td>

                                <td width="10%">${vo.createTime?string('yyyy-MM-dd HH:mm:ss')}</td>
                                <td width="20%">
                                    <#if vo.status=0 || vo.status=3 || vo.status=5>
                                        <a href="#" onclick="rePushSign('${vo.id}')">重新发起</a>
                                    </#if>
                                    <a href="#" onclick="signDetail('${vo.id}')">查看详情</a>
                                    <#if vo.callBackJson?exists && vo.callBackJson?contains('fillUnique')>
                                        <a href="#" onclick="seeFileUrl(`${(vo.callBackJson?eval).fillUnique}`,'cktchsj')">查看填充后数据</a>
                                    </#if>
                                    <#if vo.callBackJson?exists && vo.callBackJson?contains('signSuccessUnique')>
                                        <a href="#" onclick="seeFileUrl(`${(vo.callBackJson?eval).signSuccessUnique}`,'ckqzwchsj')">查看签字完成后数据</a>
                                    </#if>
                                </td>
                            </tr>
                        </#list>
                        </tbody>
                    </#if>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
</body>
</html>