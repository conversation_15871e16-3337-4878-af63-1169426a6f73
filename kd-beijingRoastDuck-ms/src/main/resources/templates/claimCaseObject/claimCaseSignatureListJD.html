<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        const loader = new Loaders({style: "rectangle"});

        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        $(document).ready(function () {

            // 业务标签 select2初始化
            $("#type-category").select2({
                placeholder: "请选择",
                width: null
            });
            // 保司标签 select2初始化
            $("#insCodeSelect").select2({
                placeholder: "请选择",
                width: null
            });
            $("#managerSelect").select2({
                placeholder: "请选择",
                width: null
            });


            $("body").on("change", "select[name='type-category']", function () {
                var value = $(this).val();
                $("input[name='type']").val(value.split("-")[0]);
                $("input[name='category']").val(value.split("-")[1]);
            });


        });


        function statusSwitch(status) {
            $("#status").val(status);
            $("#searchForm").submit();
        }

        function getTask(id) {
            $.ajax({
                url: "${ctx}/claimCaseObjectController/getTask?id=" + id,
                type: 'GET',
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layer.msg(result.msg, {
                            icon: 1,
                            time: 1500 //1秒关闭（如果不配置，默认是3秒）
                        }, function (index) {
                            //statusSwitch(0);
                            window.location.href = "${ctx}/claimCaseObjectController/claimCaseSignatureListJD?status=0&insCode=JZTB";
                        });
                    } else  {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 1500 //1秒关闭（如果不配置，默认是3秒）
                        });
                    }
                },
                error: function (data) {
                    console.log(data);
                }
            });
        }

        function completeTask(id) {
            $.ajax({
                url: "${ctx}/claimCaseObjectController/completeTask?id=" + id,
                type: 'GET',
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layer.msg(result.msg, {
                            icon: 1,
                            time: 1500 //1秒关闭（如果不配置，默认是3秒）
                        }, function (index) {
                            window.location.href = "${ctx}/claimCaseObjectController/claimCaseSignatureListJD?status=1&insCode=JZTB";
                        });
                    } else  {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 1500 //1秒关闭（如果不配置，默认是3秒）
                        });
                    }
                },
                error: function (data) {
                    console.log(data);
                }
            });
        }

        function showObject(id) {
            window.location.href = "${ctx}/claimCaseObjectController/showObject?id=" + id;
        }
        function showCase(claimCaseId) {
            window.open("${ctx}/claimCaseController/caseDetail?caseId="+claimCaseId);
        }


    </script>
    <style>

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
            border-right: 0;
            overflow: hidden;
            vertical-align: bottom;
            padding: 0px;
            display: inline-block;
        }

        .form-active > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
        }

        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 70px 6px 30px;
            font-size: 17px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
            border-right: 1px solid #e7ecf1;
        }

        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }

        td > a {
            display: inline-block;
            margin: 3px;
        }

        .li-blue {
            background: #0b94ea;
            color: #fff;
        }
    </style>
</head>
<body>

<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>新职业平台</span> <i class="fa fa-circle"></i></li>
                    <li>
                        <span class="active">
                        <#if claimCaseObjectVo.insCode?has_content || claimCaseObjectVo.insCode != '' >
                            ${claimCaseObjectVo.insCode}签章任务
                        <#else>
                            全案签章任务
                        </#if>
                        </span>
                    </li>
                </ul>
            </div>
            <div class="portlet-body">

                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal" action="${ctx}/claimCaseObjectController/claimCaseSignatureListJD"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="claimCaseNo" id="claimCaseNo"
                                               value="${claimCaseObjectVo.claimCaseNo}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人姓名：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatName" id="treatName"
                                               value="${claimCaseObjectVo.treatName}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人身份证：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatIdNum" id="treatIdNum"
                                               value="${claimCaseObjectVo.treatIdNum}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">估损单类型：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <select class="form-control" name="type-category" id="type-category">
                                            <option value=" ">请选择</option>
                                            <#if typeCategoryMap??>
                                                <#list typeCategoryMap.keySet() as key>
                                                    <option value="${key}" <#if key == claimCaseObjectVo.type + "-" + claimCaseObjectVo.category> selected </#if>>${typeCategoryMap.get(key)}</option>
                                                </#list>
                                            </#if>
                                        </select>
                                        <input type="hidden" name="type" value="${claimCaseObjectVo.type}">
                                        <input type="hidden" name="category" value="${claimCaseObjectVo.category}">
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" name="insCode" id="insCode" value="${claimCaseObjectVo.insCode}">

                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="control-label col-sm-3" style="padding-right: 0">处理人员：</label>
                                        <div class="col-sm-8" style="padding-left: 8px;">
                                            <select class="form-control" name="managerSelect"
                                                    id="managerSelect" onchange="javascript:$('#auditer').val($(this).val());">
                                                <option value=" " selected>请选择</option>
                                                <#if managerSelectMap?exists>
                                                    <#list managerSelectMap.keySet() as key>
                                                        <option value="${key}" <#if key == claimCaseObjectVo.auditer>selected</#if>>
                                                            ${managerSelectMap.get(key)}
                                                        </option>
                                                    </#list>
                                                </#if>
                                            </select>
                                            <input type="hidden" name="auditer" id="auditer" value="${claimCaseObjectVo.auditer}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group pull-right" style="margin-bottom: 10px;">
                                    <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;margin-right: 20px;">查询</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-active">
                        <div>
                            <input type="hidden" name="status" id="status" value="${claimCaseObjectVo.status}">
                            <ul style="padding: 0px;margin: 0px;display: flex;">
                                <li class="li-default <#if claimCaseObjectVo.status == 0>li-blue</#if>" onclick="statusSwitch(0)">全部签章任务</li>
                                <li class="li-default <#if claimCaseObjectVo.status == 1>li-blue</#if>" onclick="statusSwitch(1)">我的签章任务</li>
                            </ul>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th width="10%">报案号</th>
                        <th width="10%">出险人姓名</th>
                        <th width="10%">出险人身份证</th>
                        <th width="10%">承保公司</th>
                        <th width="10%">赔付对象类型</th>
                        <th width="10%">定损/维修总金额</th>
                        <th width="15%">处理岗人员</th>
                        <th width="10%">创建时间</th>
                        <th width="15%">功能</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as vo>
                        <tr>
                            <td title="${vo.claimCaseNo}">${vo.claimCaseNo}</td>
                            <#if vo.insCode == 'JZTB'>
                                <td title="${vo.name}">${vo.name}</td>
                            <#else>
                                <td title="${vo.treatName}">${vo.treatName}</td>
                            </#if>
                            <td title="${vo.treatIdNum}">${vo.treatIdNum}</td>
                            <td>
                                <#if vo.insCode == 'RB' || vo.insCode == 'HMRB'>
                                    <span style="display:inline-block;background-color: #dbaf00;color: #fff;padding: 2px 6px;border-radius: 2px;">人保</span>
                                <#elseif vo.insCode == 'GY' || (vo.province == '辽宁省' && vo.treatDate?datetime gte "2023-09-29 00:00:00"?datetime) >
                                    <span style="display:inline-block;background-color: #067ee5;color: #fff;padding: 2px 6px;border-radius: 2px;">国元</span>
                                <#elseif vo.insCode == 'DD'>
                                    <span style="display:inline-block;background-color: #26c281;color: #fff;padding: 2px 6px;border-radius: 2px;">大地</span>
                                <#elseif vo.insCode == 'HX'>
                                    <span style="display:inline-block;background-color: #ee5f5b;color: #fff;padding: 2px 6px;border-radius: 2px;">海峡</span>
                                <#elseif vo.insCode == 'JZTB'>
                                    <span style="display:inline-block;background-color: #f78c45;color: #fff;padding: 2px 6px;border-radius: 2px;">太保</span>
                                </#if>
                            <td>
                                <#switch vo.type + "-" + vo.category>
                                    <#case "1-1">
                                        骑手人伤
                                        <#break >
                                    <#case "2-1">
                                        三者人伤
                                        <#break >
                                    <#case "2-2">
                                        三者物损
                                        <#break >
                                    <#case "2-3">
                                        三者车损
                                        <#break >
                                </#switch>
                            </td>
                            <td title="${(vo.lossAssessmentSum?string(',##0.00'))!''}">
                                ${(vo.lossAssessmentSum?string(',##0.00'))!''}
                            </td>
                            <td title=" <#if vo.auditer??>
                                    ${managerMap.get(vo.auditer)}
                                <#else >
                                    --
                                </#if>">
                                <#if vo.auditer??>
                                    ${managerMap.get(vo.auditer)}
                                <#else >
                                    --
                                </#if>

                            </td>
                            <td>${vo.createTime?string('yyyy-MM-dd HH:mm:ss')}</td>
                            <td>
                                <#if vo.isCaseClosed != 1>
                                    <#if claimCaseObjectVo.status == 0>
                                        <a href="#" onclick="getTask('${vo.id}')">领取任务</a>
                                    </#if>
                                    <#if claimCaseObjectVo.status == 1>
                                        <a href="#" onclick="completeTask('${vo.id}')">完成</a>
                                    </#if>
                                </#if>
                                <a href="#" onclick="showObject('${vo.id}')">查看</a>
                                <a href="#" onclick="showCase('${vo.claimCaseId}')">查看案件</a>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>