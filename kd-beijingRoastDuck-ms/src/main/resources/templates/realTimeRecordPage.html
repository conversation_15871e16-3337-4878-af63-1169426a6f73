<!DOCTYPE html>
<!--
Template Name: Metronic - Responsive Admin Dashboard Template build with Twitter Bootstrap 3.3.5
Version: 4.5.2
Author: KeenThemes
Website: http://www.keenthemes.com/
Contact: <EMAIL>
Follow: www.twitter.com/keenthemes
Like: www.facebook.com/keenthemes
Purchase: http://themeforest.net/item/metronic-responsive-admin-dashboard-template/4021469?ref=keenthemes
License: You must have a valid license purchased only from themeforest(the above link) in order to legally use the theme for your project.
-->
<!--[if IE 8]> <html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
<!--<![endif]-->
<!-- BEGIN HEAD -->

<head>
	<meta charset="utf-8" />
	<title>实时数据展示</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta content="width=device-width, initial-scale=1" name="viewport" />
	<meta content="" name="description" />
	<meta content="" name="author" />

	<#include "/common/cssResource.html">
	<!-- BEGIN PAGE LEVEL STYLES -->
	<link href="${ctx}/metronic/pages/css/login.min.css" rel="stylesheet" type="text/css" />
	<!-- END PAGE LEVEL STYLES -->

	<#include "/common/jsResource.html">

	<!--[if lt IE 10]>
	<meta http-equiv="Refresh" content="0; url=${ctx}/browserNotMatch" />
	<![endif]-->

	<style type="text/css">
		.td-red{
			background-color: #e53e49;
			color: white;
		}
		.td-purple{
			background-color: #8E44AD;
			color: white;
		}
		.td-blue{
			background-color: #3598dc;
			color: white;
		}
		.td-green{
			background-color: #32c5d2;
			color: white;
		}
		tr>th,tr>td {
			text-align: center;
		}
	</style>
	<script type="text/javascript">
		$(document).ready(function() {
			var isLoaded = false;

			function refreshRecord() {
				var isOnline = $("input[name='isOnline']:checked").val();
				$.ajax({
					url: "${ctx}/api/refreshDistribute",
					type: 'POST',
					data : '{"isOnline": '+isOnline+'}',
					async: true,
					cache: false,
					contentType: false,
					processData: false,
					beforeSend: function () {
						isLoaded = false;
					},
					success: function (data) {
						console.log(data);
						var result = eval("(" + data + ")");
						var str = "";
						var recordList = result.recordList;
						console.log(recordList);
						for (var k in recordList) {
							if (recordList[k].takeUpTime < 720) {
								str += "<tr>" +
										"<td>" + recordList[k].claimOrderNo + "</td>" +
										"<td>" + recordList[k].id + "</td>" +
										"<td>" + recordList[k].sendDateStr + "</td>" +
										"<td class='td-green'>" + recordList[k].takeUpTime + "</td>" +
										"<td>" + recordList[k].num + "</td>" +
										"<td>" + recordList[k].receiveDateStr + "</td>" +
										"</tr>";
							} else if (recordList[k].takeUpTime < 960) {
								str += "<tr>" +
										"<td>" + recordList[k].claimOrderNo + "</td>" +
										"<td>" + recordList[k].id + "</td>" +
										"<td>" + recordList[k].sendDateStr + "</td>" +
										"<td class='td-blue'>" + recordList[k].takeUpTime + "</td>" +
										"<td>" + recordList[k].num + "</td>" +
										"<td>" + recordList[k].receiveDateStr + "</td>" +
										"</tr>";
							} else if (recordList[k].takeUpTime < 1200) {
								str += "<tr>" +
										"<td>" + recordList[k].claimOrderNo + "</td>" +
										"<td>" + recordList[k].id + "</td>" +
										"<td>" + recordList[k].sendDateStr + "</td>" +
										"<td class='td-purple'>" + recordList[k].takeUpTime + "</td>" +
										"<td>" + recordList[k].num + "</td>" +
										"<td>" + recordList[k].receiveDateStr + "</td>" +
										"</tr>";
							} else {
								str += "<tr>" +
										"<td>" + recordList[k].claimOrderNo + "</td>" +
										"<td>" + recordList[k].id + "</td>" +
										"<td>" + recordList[k].sendDateStr + "</td>" +
										"<td class='td-red'>" + recordList[k].takeUpTime + "</td>" +
										"<td>" + recordList[k].num + "</td>" +
										"<td>" + recordList[k].receiveDateStr + "</td>" +
										"</tr>";
							}
						}
						$("#tbody").html(str);

						if (isOnline == "0") {
							var passInfo = " <div class=\"col-sm-2\" style=\"background-color: #FF7256; padding-top: 5px;  padding-left: 15px; padding-bottom: 15px; text-align: center;\">\n" +
									"                                    <h4><strong>超时案件数量</strong></h4>\n" +
									"                                    <div>超过24小时案件数："+ result.pass24Hour +"</div>\n" +
									"                                </div>\n" +
									" <div class=\"col-sm-2\" style=\"background-color: #FF3030; margin-left: 15px; padding-top: 5px;  padding-left: 15px; padding-bottom: 15px; text-align: center;\">\n" +
									"                                    <h4><strong>严重超时案件数量</strong></h4>\n" +
									"                                    <div>超过48小时案件数："+ result.pass48Hour +"</div>\n" +
									"                                </div>";
							$("#passInfo").html(passInfo);
						} else {
							var passInfo = " <div class=\"col-sm-2\" style=\"background-color: #FF7256; padding-top: 5px;  padding-left: 15px; padding-bottom: 15px; text-align: center;\">\n" +
									"                                    <h4><strong>超时案件数量</strong></h4>\n" +
									"                                    <div>超过8小时案件数："+ result.pass24Hour +"</div>\n" +
									"                                </div>\n" +
									" <div class=\"col-sm-2\" style=\"background-color: #FF3030; margin-left: 15px; padding-top: 5px;  padding-left: 15px; padding-bottom: 15px; text-align: center;\">\n" +
									"                                    <h4><strong>严重超时案件数量</strong></h4>\n" +
									"                                    <div>超过16小时案件数："+ result.pass48Hour +"</div>\n" +
									"                                </div>";
							$("#passInfo").html(passInfo);
						}
					},
					complete: function () {
						isLoaded = true;
					}
				});
			}

			// start 线上件/线下件切换
			$("input[name='isOnline']").change(function () {
			    if (isLoaded) {
                    refreshRecord({"init": 0});
                }
			});
			// end

			$("#isOnline1").trigger("click");

			// start 初始化
			refreshRecord({"init": 1});
			setInterval(function () {
				isLoaded && refreshRecord({"init": 0});
			}, 30000);
			// end 初始化
		});
	</script>

</head>
<!-- END HEAD -->

<body class="">
<div class="menu-toggler sidebar-toggler"></div>
<!-- END SIDEBAR TOGGLER BUTTON -->
<div class="content">
	<!-- BEGIN LOGIN FORM -->
	<div class="row">
		<div class="col-sm-offset-1 col-sm-10">
			<!-- BEGIN EXAMPLE TABLE PORTLET-->
			<div class="portlet light portlet-fit bordered">
				<div class="portlet-body">
					<div class="table-toolbar">
						<div class="row">
							<div id="passInfo" class="form-group col-sm-12">

							</div>
						</div>
					</div>
				</div>
                <div class="btn-group" data-toggle="buttons">
                    <label class="btn btn-default">
                        <input type="radio" class="toggle" id="isOnline1" name="isOnline" value="1"> 线上件 </label>
                    <label class="btn btn-default">
                        <input type="radio" class="toggle" id="isOnline0" name="isOnline" value="0"> 线下件 </label>
                </div>
				<table class="table table-striped table-bordered table-hover table-header-fixed"
					   id="table" style="min-height: 70%;">
					<thead>
					<tr>
						<th class="col-sm-2">案件号</th>
						<th class="col-sm-2">ID</th>
						<th class="col-sm-3">分发时间</th>
						<th class="col-sm-1">耗时（minute）</th>
						<th class="col-sm-2">失败返回次数</th>
						<th>上次返回时间</th>
					</tr>
					</thead>
					<tbody id="tbody">

					</tbody>
				</table>
			</div>
		</div>
		<!-- END LOGIN FORM -->
	</div>
</div>
</body>
</html>