<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>

    <script type="text/javascript">
        const loader = new Loaders({ style: "rectangle" });
        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        var scrollTop;

        $(document).ready(function () {

            // 业务标签 select2初始化
            $("#applyType").select2({
                placeholder: "请选择",
                width: null
            });

            // 业务标签 select2初始化
            var labelList = [];
            <#if labelShowMap?? && (labelShowMap.keySet() ? size > 0) >
                <#list labelShowMap.keySet() as key >
                    labelList.push({id: '${key}', text: '${labelShowMap.get(key).msg}'})
                </#list >
            </#if >
            $("#label").select2({
                placeholder: "请选择",
                width: null,
                data: labelList
            });
            var nowLabel = [];
            <#if claimCaseVo.label?exists>
                <#list claimCaseVo.label?split(",") as code>
                    nowLabel.push("${code}");
                </#list>
                $("#label").val(nowLabel).trigger('change');
            </#if >
        });

        //查看详情
        function caseDetail(claimCaseId) {
            window.location.href = "${ctx}/claimCaseController/insuranceCaseDetail4BS?caseId=" + claimCaseId;
        }

        function statusSwitch(status) {
            $("#status").val(status);
            $("#tagChoose").val(1);
            $("#searchForm").submit();
        }

        //编辑图片按钮
        function caseListEidtAttach() {
            var openWindowWidth = $(document).width() * 0.8 + "px";
            scrollTop = calculationScrollTop();
            let url = "${ctx}/claimCaseController/caseListEditAttach";
            layer.open({
                type: 2,
                title: '影像编辑',
                area: [openWindowWidth, '800px'],
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                content: url,
                shadeClose: true,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }


        // 计算滚动高度
        function calculationScrollTop() {
            var ifm = parent.document.getElementById("contentFrame");
            scrollTop = $(window.parent).scrollTop() + 150;
            if (!ifm) {
                scrollTop = 300;
            }
            return scrollTop + "px";
        }


        //查询按钮
        function querySearch() {
            let formData = $('#searchForm');
            let insCode = '${claimCaseVo.insCode}';
            let fieldNamesToCheck = ['insuranceCaseNo', 'applyName', 'applyMobile', 'applyType',
                'treatName', 'treatIdNum', 'applyType', 'treatDateStart', 'treatDateEnd', 'startDateStart', 'startDateEnd', 'label'
            ];
            //判断页面输入框是否为空
            let isAllQueryEmpty = checkFieldsIsEmpty(formData, fieldNamesToCheck);
            //全案案件查询claimCaseVo.insCode为空，不做查询条件校验
            //保司案件查询做查询条件校验
            if (isAllQueryEmpty && insCode) {
                layer.msg('请输入查询条件', {
                    icon: 2,
                    time: 2000,
                    offset: calculationScrollTop()
                });
            } else {
                formData.submit();
            }
        }

        function checkFieldsIsEmpty(formSelector, fieldNames) {
            // 将表单中所有字段的值存储在一个对象中
            let formValues = $(formSelector).serializeArray();
            // 将字段名称数组转换为对象，方便后续检查
            let fieldsToCheck = fieldNames.reduce(function (obj, name) {
                obj[name] = false; // 初始化为false，表示尚未检查
                return obj;
            }, {});

            let allEmpty = true;
            // 遍历所有表单字段
            $.each(formValues, function (index, field) {
                // 如果字段在需要检查的字段列表中，并且值为空，则标记为true
                if (fieldsToCheck.hasOwnProperty(field.name)) {
                    if (!field.value.trim()) {
                        fieldsToCheck[field.name] = true; // 标记为空
                    } else {
                        allEmpty = false;
                    }
                }
            });
            return allEmpty;
        }

    </script>
    <style>
        .select2-dropdown {
            z-index: 19891099 !important;
        }

        /*loader需要的遮罩层css start */
        #screen {
            height: 100%;
            width: 100%;
            z-index: 198910141;
            position: fixed;
            background: rgba(0, 0, 0, 0.3);
            top: 0;
        }

        #screen #screenLoading {
            margin: 0 auto;
            top: 40%;
            transform: translateY(-50%);
            background: greenyellow;
        }

        /*loader需要的遮罩层css end*/

        .clear-padding {
            padding: 0px !important;
        }

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
            border-right: 0;
            overflow: hidden;
            vertical-align: bottom;
            padding: 0px;
            display: inline-block;
        }

        .form-active > div > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
            display: flex;
        }

        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 70px 6px 30px;
            font-size: 17px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
            border-right: 1px solid #e7ecf1;
        }

        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }

        .li-blue {
            background: #0b94ea;
            color: #fff;
        }

        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin: 3px;
            padding: 2px;
        }

        td>a {
            display: inline-block;
            margin: 3px;
        }

        .td-overflow {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 100px;
        }

        .labelGroup span {
            margin-left: 5px;
            margin-right: 5px;
            background-color: #1676FF;
            color: white;
            border-radius: 2px;
        }

        .btn-bule {
            color: #FFFFFF;
            background-color: #1676FF;
            border-color: #1676FF;
            margin-left: 3%;
        }
    </style>
</head>

<body>
    <div class="row">
        <div class="col-sm-12">
            <div class="portlet light portlet-fit bordered">
                <div class="portlet-title">
                    <ul class="page-breadcrumb breadcrumb">
                        <li><span>新职业平台</span> <i class="fa fa-circle"></i></li>
                        <li><span class="active">
                                <#if claimCaseVo.insCode?has_content || claimCaseVo.insCode !=''>
                                    ${claimCaseVo.insCode}案件查询
                                    <#else>
                                        全案案件查询
                                </#if>
                            </span></li>

                    </ul>
                </div>
                <div class="portlet-body">

                    <!-- BEGIN FORM-->
                    <form id="searchForm" class="form-horizontal"
                          action="${ctx}/claimCaseController/insuranceCaseList4BS" method="post">
                        <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                        <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                        <div class="form-body">
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="control-label col-sm-3" style="padding-right: 0">案件号：</label>
                                        <div class="col-sm-8" style="padding-left: 0;">
                                            <input type="text" class="form-control" name="insuranceCaseNo"
                                                   id="insuranceCaseNo" value="${claimCaseVo.insuranceCaseNo}"
                                                   placeholder="请输入"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="control-label col-sm-3" style="padding-right: 0">出险人姓名：</label>
                                        <div class="col-sm-8" style="padding-left: 0;">
                                            <input type="text" class="form-control" name="treatName" id="treatName"
                                                   value="${claimCaseVo.treatName}" placeholder="请输入"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="control-label col-sm-3" style="padding-right: 0">出险人身份证：</label>
                                        <div class="col-sm-8" style="padding-left: 0;">
                                            <input type="text" class="form-control" name="treatIdNum" id="treatIdNum"
                                                   value="${claimCaseVo.treatIdNum}" placeholder="请输入"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <input type="hidden" name="insCode" id="insCode" value="${claimCaseVo.insCode}">
                        </div>
                        <div class="form-actions">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="btn-group pull-right" style="margin-bottom: 10px;margin-right: 30px;">
                                        <button id="query" type="button" class="btn green" style="margin-bottom: 10px;"
                                                onclick="querySearch()">查询</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-active">
                            <div>
                                <input type="hidden" name="status" id="status" value="${claimCaseVo.status}">
                                <input type="hidden" name="tagChoose" id="tagChoose" value="">
                                <ul>
                                    <li class="li-default <#if claimCaseVo.status == 9>li-blue</#if>" onclick="statusSwitch(9)">未决
                                    </li>
                                    <li class="li-default <#if claimCaseVo.status == 8>li-blue</#if>" onclick="statusSwitch(8)">已决
                                    </li>
                                    <li class="li-default <#if claimCaseVo.status == 7>li-blue</#if>" onclick="statusSwitch(7)">关闭
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </form>
                    <table class="table table-striped table-bordered table-hover table-header-fixed">
                        <thead>
                        <tr>
                            <th width="10%">案件号</th>
                            <th width="10%">出险人姓名</th>
                            <th width="10%">出险人身份证</th>
                            <th width="12%">出险类型</th>
                            <th width="10%">标签</th>
                            <@shiro.hasPermission name="SHOW_INS_LIST_APPRAISAL_AMOUNT">
                                <th width="7%">估损金额</th>
                            </@shiro.hasPermission>
                            <th width="7%">赔款金额</th>
                            <th width="13%" class="td-overflow">案件状态</th>
                            <th width="13%">功能</th>
                        </tr>
                        </thead>
                        <tbody>
                        <#list page.list as vo>
                            <tr>
                                <td title="">${vo.insuranceCaseNo}</td>
                                <td title="">${vo.treatName}</td>
                                <td title="">${vo.treatIdNum}</td>
                                <td>
                                    <#if vo.applyType??>
                                        <#list vo.applyType?split(",") as name>
                                            <span class="span-type">${name}</span>
                                        </#list>
                                    </#if>
                                </td>
                                <td title="" class="labelGroup">
                                    <#if vo.label??>
                                        <#list vo.label.split(",") as key>
                                            <#if (key?trim)!="">
                                                <span class="${key} span-type"
                                                      style="color: ${labelShowMap.get(key).fontColor};background-color: ${labelShowMap.get(key).color}">
                                                <#if labelShowMap.get(key)??>${labelShowMap.get(key).msg}<#else>${key}
                                                </#if>
                                            </span>
                                            </#if>
                                        </#list>
                                    </#if>
                                </td>
                                <!-- 估损金额 -->
                                <#--展示估损金额-->
                                <@shiro.hasPermission name="SHOW_INS_LIST_APPRAISAL_AMOUNT">
                                    <td>
                                        ${vo.appraisalAmount!'--'}
                                    </td>
                                </@shiro.hasPermission>
                                <td>
                                    <#--保司案件流转表展示页面不暂时，页面显示详情中的流转表不显示-->
                                    <@shiro.hasPermission name="CLAIM_CASE_DETAIL_PROCESS_IMAGE_INS">
                                        --
                                    </@shiro.hasPermission>
                                    <@shiro.lacksPermission name="CLAIM_CASE_DETAIL_PROCESS_IMAGE_INS">
                                        ${vo.appraisalAmount!'--'}
                                    </@shiro.lacksPermission>
                                </td>
                                <td>
                                    <#if claimCaseVo.status == 8>
                                        ${vo.payAmount!'--'}
                                    <#else>
                                        --
                                    </#if>
                                </td>
                                <#if closeMap.get(vo.id)??>
                                    <td class="td-overflow"
                                        title="${closeMap.get(vo.id)}（${claimCaseStatusEumMap.get(vo.status).msg}）">
                                        ${closeMap.get(vo.id)}（${claimCaseStatusEumMap.get(vo.status).msg}）
                                    </td>
                                <#else>
                                    <td class="td-overflow" title="${claimCaseStatusEumMap.get(vo.status).msg}">
                                        ${claimCaseStatusEumMap.get(vo.status).msg}
                                    </td>
                                </#if>
                                <#--功能-->
                                <td>
                                    <a href="#" onclick="caseDetail('${vo.id}')">查看详情</a>
                                </td>
                            </tr>
                        </#list>
                        </tbody>
                    </table>
                    <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                        <div class="modal-dialog">
                            <img src="${ctx}/images/load.gif">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- END FORM-->
    </div>
    <@sc.pagination page=page />
</body>

</html>