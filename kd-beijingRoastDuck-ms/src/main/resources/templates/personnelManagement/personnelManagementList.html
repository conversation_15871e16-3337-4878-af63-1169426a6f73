<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        const loader =  new Loaders({style:"rectangle"});
        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        $(document).ready(function () {

        });


        //查看详情
        function viewDetail(policyPersonId) {
            console.log(policyPersonId);
            window.location.href="${ctx}/personnelManagementController/personnelManagementDetail?policyPersonId="+policyPersonId;

        }
    </script>
    <style>


        /*loader需要的遮罩层css start */
        #screen {
            height: 100%;
            width: 100%;
            z-index: 198910141;
            position: fixed;
            background: rgba(0,0,0,0.3);
            top:0;
        }

        #screen #screenLoading{
            margin: 0 auto;
            top: 40%;
            transform: translateY(-50%);
            background: greenyellow;
        }
        /*loader需要的遮罩层css end*/

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
        }
        .form-active > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
        }
        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 22px;
            font-size: 17px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
        }
        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }
        .li-default:nth-of-type(2) {
            border-left: 1px solid #e7ecf1;
            border-right: 1px solid #e7ecf1;
        }
        .li-default:nth-of-type(3) {
            border-right: 1px solid #e7ecf1;
        }
        .li-blue {
            background: #0b94ea ;
            color: #fff;
        }
        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }
        td > a {
            display: inline-block;
            margin: 3px;
        }
    </style>
</head>
<body>
<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>新职业平台</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">人员查询</span></li>
                </ul>
            </div>
            <div class="portlet-body">

                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal" action="${ctx}/personnelManagementController/baseUserInfoListV2?tagChoose=1"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">

                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">被保人姓名：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="name" id="name"
                                               value="${baseUserReq.name}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">身份证号码：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="idNumber" id="idNumber"
                                               value="${baseUserReq.idNumber}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">凭证号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="customerPolicyNo" id="customerPolicyNo"
                                               value="${baseUserReq.customerPolicyNo}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>

<#--                            <div class="col-sm-4">-->
<#--                                <div class="form-group">-->
<#--                                    <label class="control-label col-sm-3" style="padding-right: 0">承包商名称：</label>-->
<#--                                    <div class="col-sm-8" style="padding-left: 0;">-->
<#--&lt;#&ndash;                                        <input type="text" class="form-control" name="primaryAgentName" id="primaryAgentName"&ndash;&gt;-->
<#--&lt;#&ndash;                                               value="${baseUserReq.primaryAgentName}"&ndash;&gt;-->
<#--&lt;#&ndash;                                               placeholder="请输入"/>&ndash;&gt;-->

<#--                                        <select class="form-control select2" name="primaryAgentId" id="primaryAgentId">-->
<#--                                            <option value="">-请选择-</option>-->
<#--                                            <#if totalAgentMap?? && (totalAgentMap.keySet()?size>0) >-->
<#--                                                <#list totalAgentMap.keySet() as key>-->
<#--                                                    <option value="${key}" <#if baseUserReq.primaryAgentId == key>selected </#if>>${totalAgentMap.get(key)}</option>-->
<#--                                                </#list>-->
<#--                                            </#if>-->
<#--                                        </select>-->

<#--                                    </div>-->
<#--                                </div>-->
<#--                            </div>-->

                        </div>

                        <div class="row">

<#--                            <div class="col-sm-4">-->
<#--                                <div class="form-group">-->
<#--                                    <label class="control-label col-sm-3" style="padding-right: 0">承包商证件号：</label>-->
<#--                                    <div class="col-sm-8" style="padding-left: 0;">-->
<#--                                        <input type="text" class="form-control" name="primaryAgentCertNo" id="primaryAgentCertNo"-->
<#--                                               value="${baseUserReq.primaryAgentCertNo}"-->
<#--                                               placeholder="请输入"/>-->
<#--                                    </div>-->
<#--                                </div>-->
<#--                            </div>-->

<#--                            <div class="col-sm-4">-->
<#--                                <div class="form-group">-->
<#--                                    <label class="control-label col-sm-3" style="padding-right: 0">子承包商名称：</label>-->
<#--                                    <div class="col-sm-8" style="padding-left: 0;">-->
<#--&lt;#&ndash;                                        <input type="text" class="form-control" name="subsidiaryAgentName" id="subsidiaryAgentName"&ndash;&gt;-->
<#--&lt;#&ndash;                                               value="${baseUserReq.subsidiaryAgentName}"&ndash;&gt;-->
<#--&lt;#&ndash;                                               placeholder="请输入"/>&ndash;&gt;-->
<#--                                        <select class="form-control select2" name="subsidiaryAgentId" id="subsidiaryAgentId">-->
<#--                                            <option value="">-请选择-</option>-->
<#--                                            <#if subAgentMap?? && (subAgentMap.keySet()?size>0) >-->
<#--                                                <#list subAgentMap.keySet() as key>-->
<#--                                                    <option value="${key}" <#if baseUserReq.subsidiaryAgentId == key>selected </#if>>${subAgentMap.get(key)}</option>-->
<#--                                                </#list>-->
<#--                                            </#if>-->
<#--                                        </select>-->
<#--                                    </div>-->
<#--                                </div>-->
<#--                            </div>-->

<#--                            <div class="col-sm-4">-->
<#--                                <div class="form-group">-->
<#--                                    <label class="control-label col-sm-3" style="padding-right: 0">子承包商证件号：</label>-->
<#--                                    <div class="col-sm-8" style="padding-left: 0;">-->
<#--                                        <input type="text" class="form-control" name="subsidiaryAgentCertNo" id="subsidiaryAgentCertNo"-->
<#--                                               value="${baseUserReq.subsidiaryAgentCertNo}"-->
<#--                                               placeholder="请输入"/>-->
<#--                                    </div>-->
<#--                                </div>-->
<#--                            </div>-->

                        </div>

                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group pull-right" style="margin-bottom: 10px;">
                                    <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;">查询</button>
                                </div>
                            </div>
                        </div>
                    </div>

                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th width="9%">被保人姓名</th>
                        <th width="9%">身份证号</th>
                        <th width="9%">承包商名称</th>
                        <th width="9%">承包商证件号</th>
                        <th width="9%">子承包商名称</th>
                        <th width="9%">子承包商证件号</th>
                        <th width="9%">省市</th>
                        <th width="9%">功能</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#if page.list?exists && (page.list?size gt 0)>
                        <#list page.list as vo>
                            <tr>
                                <td title="">${vo.name}</td>
                                <td title="">${vo.idNumber}</td>
                                <td title="">${vo.primaryAgentName}</td>
                                <td title="">${vo.primaryAgentCertNo}</td>
                                <td title="">${vo.subsidiaryAgentName}</td>
                                <td title="">${vo.subsidiaryAgentCertNo}</td>
                                <td title="">${vo.provinceAndCity}</td>
                                <#--功能-->
                                <td>
                                    <a href="#" onclick="viewDetail('${vo.id}')">查看详情</a>
                                </td>
                            </tr>
                        </#list>
                    <#else >
                        <tr>
                            <td align="center" colspan="8">暂无数据</td>
                        </tr>
                    </#if>
                    </tbody>
                </table>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>