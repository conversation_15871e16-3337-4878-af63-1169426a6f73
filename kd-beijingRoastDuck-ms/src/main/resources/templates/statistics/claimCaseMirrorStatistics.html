<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/metronic/global/scripts/app.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/layout.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/demo.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/jquery-multi-select/js/jquery.multi-select.js"
            type="text/javascript"></script>


    <script type="text/javascript">

        $(document).ready(function () {

            let dateStr = document.getElementById("queryDate").value;
            $("table").on("click", "td:not(:first-of-type)", function(){
                var auditor = $(this).attr("data-auditer");
                var statisticalType = $(this).attr("data-statistical-type");
                if (!statisticalType) {
                    layer.msg("缺少必要参数！",{icon: 2,time: 1500});
                    return;
                }
                let formElement = document.createElement("form");
                formElement.action = "${ctx}/claimCaseStatistics/claimCaseMirrorStatisticsList?statisticalType="+statisticalType+"&dateStr="+dateStr+"&auditor="+auditor;
                formElement.method = "post";
                formElement.style.display = "none";
                // formElement.appendChild(createInput("statisticalType", statisticalType));
                document.body.appendChild(formElement);
                formElement.submit();
                formElement.remove();
            });

            laydate.render({
                elem: '#queryDate',
                type: 'date',
                max: -1,
                btns: ['confirm'],
                done: function(value, date){
                    window.location.href = "${ctx}/claimCaseStatistics/claimCaseMirrorStatistics?dateStr="+value;
                }
            });
        });

    </script>
    <style>
        td:hover {
            background-color: #eef1f5;
        }
    </style>
</head>
<body>
<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>饿了么</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">预处理统计台</span></li>
                </ul>
            </div>
            <div class="form-body">
                <div class="col-sm-4" style="float: right">
                    <div class="form-group">
                        <label class="control-label col-sm-3" style="padding-right: 0;padding-top: 5px">统计时间 :</label>
                        <div class="col-sm-8" style="padding-left: 0;float: right" >
                                <input type="text"  class="form-control" data-valid="none" name="queryDate" id="queryDate"
                                       value="${dateStr}"
                                />
                        </div>
                    </div>
                </div>
            </div>
            <div class="portlet-body">
                <table class="table table-striped table-bordered table-header-fixed">
                    <thead>
                    <tr>
                        <th>账号</th>
                        <th>待处理</th>
                        <th>缺材料待补充</th>
                        <th>已补材待审核</th>
                        <th>当日关闭数</th>
                        <th>当日接收数</th>
                        <th>当日完成数</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#if statisticsMap?? && (statisticsMap.keySet()?size > 0) >
                        <#list statisticsMap.keySet() as key>
                            <#assign countMap = statisticsMap.get(key) >
                            <tr>
                                <td title="${countMap.get("realName")}">${countMap.get("realName")}</td>
                                <td title="${countMap.get("dclCase")}" data-auditer="${key}" data-statistical-type="1" >${countMap.get("dclCase")}</td>
                                <td title="${countMap.get("qcldbc")}" data-auditer="${key}" data-statistical-type="2" >${countMap.get("qcldbc")}</td>
                                <td title="${countMap.get("ybcdsh")}" data-auditer="${key}" data-statistical-type="3" >${countMap.get("ybcdsh")}</td>
                                <td title="${countMap.get("todayClose")}" data-auditer="${key}" data-statistical-type="4" >${countMap.get("todayClose")}</td>
                                <td title="${countMap.get("todayReceiveCountTotal")}" data-auditer="${key}" data-statistical-type="5" >${countMap.get("todayReceiveCountTotal")}</td>
                                <td title="${countMap.get("todayFinishCountTotal")}" data-auditer="${key}" data-statistical-type="6" >${countMap.get("todayFinishCountTotal")}</td>
                            </tr>
                        </#list>

                    <#else>
                    <tr>
                        <td colspan="7" style="text-align: center">统计数据还未生成...</td>
                    </tr>
                    </#if>
                    </tbody>
                </table>
<#--                                        <tr>-->
<#--                                            <td title="">合计</td>-->
<#--                                            <td title="${totalMap.get("processCountTotal")}" data-statistical-type="1" >${totalMap.get("processCountTotal")}</td>-->
<#--                                            <td title="${totalMap.get("finishCountTotal")}" data-statistical-type="2" >${totalMap.get("finishCountTotal")}</td>-->
<#--                                            <td title="${totalMap.get("closeCountTotal")}" data-statistical-type="3" >${totalMap.get("closeCountTotal")}</td>-->
<#--                                            <td title="${totalMap.get("todayReceiveCountTotal")}" data-statistical-type="4" >${totalMap.get("todayReceiveCountTotal")}</td>-->
<#--                                            <td title="${totalMap.get("todayFinishCountTotal")}" data-statistical-type="5" >${totalMap.get("todayFinishCountTotal")}</td>-->
<#--                                        </tr>-->
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
</body>
</html>