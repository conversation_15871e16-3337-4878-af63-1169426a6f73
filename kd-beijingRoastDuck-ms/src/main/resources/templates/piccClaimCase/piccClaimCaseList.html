<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>

    <script src="${ctx}/js/xlsx/xlsx.full.min.js?ts=2"></script>

    <script type="text/javascript">

        function iframeH() {
            var ifm = parent.document.getElementById("contentFrame");
            if (ifm) {
                var ifmH = $(ifm).height()
                var h = $(document.body).height() + 120;
                if (ifmH < h) {
                    parent.document.getElementById("contentFrame").height = h;
                }
            }
        }

        const loader =  new Loaders({style:"rectangle"});
        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        $(document).ready(function () {

            $("#status").select2({
                placeholder: "请选择",
                width: null,
                allowClear: true
            });

            $("#insCode").select2({
                placeholder: "请选择",
                width: null,
                allowClear: true
            });

            $("#caseStatus").select2({
                placeholder: "请选择",
                width: null,
                allowClear: true
            });

            var postForm = function (url) {
                // 创建表单
                var formElement = document.createElement("form");
                formElement.action = url;
                formElement.method = "post";
                // 打开新标签
                formElement.target = '_blank';
                formElement.style.display = "none";
                formElement.appendChild(createInput("status", $.trim($("#status").val())));
                formElement.appendChild(createInput("claimCaseNo", $.trim($("#claimCaseNo").val())));
                formElement.appendChild(createInput("applyName", $.trim($("#applyName").val())));
                formElement.appendChild(createInput("applyMobile", $.trim($("#applyMobile").val())));
                formElement.appendChild(createInput("treatIdNum", $.trim($("#treatIdNum").val())));
                formElement.appendChild(createInput("applyType", $.trim($("#applyType").val())));
                formElement.appendChild(createInput("treatDateStart", $.trim($("#treatDateStart").val())));
                formElement.appendChild(createInput("treatDateEnd", $.trim($("#treatDateEnd").val())));
                formElement.appendChild(createInput("startDateStart", $.trim($("#startDateStart").val())));
                formElement.appendChild(createInput("startDateEnd", $.trim($("#startDateEnd").val())));
                formElement.appendChild(createInput("label", $.trim($("#label").val())));
                formElement.appendChild(createInput("insCode", $.trim($("#insCode").val())));
                formElement.appendChild(createInput("direction", 1));      // 方向（1：饿了么；2：保司）

                document.body.appendChild(formElement);
                formElement.submit();
                formElement.remove();
                return;
            }
        });

        var createInput = function (name, value) {
            var inputElement = document.createElement("input");
            inputElement.type = "hidden";
            inputElement.name = name;
            if (value != null) {
                inputElement.value = value;
            }
            return inputElement;
        }


        //查看详情
        function caseDetail(claimCaseId) {
            window.location.href="${ctx}/claimCaseController/caseDetail4BS?caseId="+claimCaseId;
        }

        // 导出上载模板
        function exportPiccUploadTemplate() {
            window.location.href="${ctx}/claimCaseController/exportPiccUploadTemplate";
        }

        function sheetSwitch(status) {
            layer.msg("数据查询中，请稍候", {icon: 16, time: 10000, shade: [0.1, '#000000']});
            $("#sheetStatus").val(status);
            $("#searchForm").submit();
        }


        // 计算滚动高度
        function calculationScrollTop() {
            var ifm = parent.document.getElementById("contentFrame");
            var scrollTop = $(window.parent).scrollTop() + 150;
            if (!ifm) {
                scrollTop = 300;
            }
            return scrollTop + "px";
        }


        // 上载推送控制台
        function uploadPushCenter() {
            $("#caseStatus").val("").trigger('change');
            if ($("#uploadBtn").val() == undefined || $("#uploadBtn").val() == "") {
                layer.msg("请选择文件", {icon: 2, time: 1500, shade: [0.1, '#000']});
                return;
            }
            layer.open({
                title: "上载推送",
                type: 1,
                area: ['600px', '300px'],
                fixed: false,
                offset: calculationScrollTop(),
                closeBtn: 1,
                content: $('#caseStatusContainer'),
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        var layerTop = top.layer;

        function piccClaimDataPush(claimCaseNo, caseStatus) {
            if (!claimCaseNo || !caseStatus) {
                layer.msg("缺少参数", {icon: 2, time: 1500, shade: [0.1, '#000']});
                return;
            }
            var dataJson = {};
            dataJson["caseStatus"] = caseStatus;
            var dataList = [];
            dataList.push(claimCaseNo);
            dataJson["dataList"] = dataList;

            $.ajax({
                url: "${ctx}/claimCaseBatchController/addCasePushBatchAndDetail",
                type: 'POST',
                data: JSON.stringify(dataJson),
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layer.msg(result.msg, {icon: 1, time: 2000, offset: calculationScrollTop()}, function (index) {
                            layer.close(index);
                            window.location.reload();
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 2000, offset: calculationScrollTop()}, function (index) {
                            layer.close(index);
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    layer.msg(result.msg, {icon: 2, time: 2000, offset: calculationScrollTop()}, function () {
                        layer.close(index);
                    });
                }
            });
        }
        
        function uploadDataPush() {
            var dataJson = {};

            var caseStatus = $("#caseStatus").val();
            if (!caseStatus) {
                layer.msg("请选择推送状态", {icon: 2, time: 2000, offset: calculationScrollTop(), shade: [0.1, '#000']});
                return;
            }

            var dataList = [];
            var file = document.getElementById("uploadBtn").files[0];   // 获取选择的文件
            if (file) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    var data = e.target.result;
                    var workbook = XLSX.read(data, {type: 'binary'}); // 读取Excel文件
                    var firstSheetName = workbook.SheetNames[0]; // 获取第一个工作表的名称
                    var worksheet = workbook.Sheets[firstSheetName]; // 获取工作表数据
                    var jsonData = XLSX.utils.sheet_to_json(worksheet, {header: 1}); // 将工作表转换为JSON对象数组

                    var title = jsonData[0][0];
                    if (title != "案件号") {
                        layer.msg("模板错误", {icon: 2, time: 2000, offset: calculationScrollTop(), shade: [0.1, '#000']});
                        return;
                    }
                    for (var i = 1; i < jsonData.length; i++) {
                        var row = jsonData[i];
                        var value = row[0];
                        if (value && value.trim()) {
                            dataList.push(value.trim());
                        }
                    }

                    if (dataList.length == 0) {
                        layer.msg("上载案件不能为空", {icon: 2, time: 2000, offset: calculationScrollTop(), shade: [0.1, '#000']});
                    }
                    dataJson["caseStatus"] = caseStatus;
                    dataJson["dataList"] = dataList;

                    layer.msg("数据推送中，请稍候", {icon: 16, time: 10000, shade: [0.1, '#000'], offset: calculationScrollTop()});
                    $.ajax({
                        url: "${ctx}/claimCaseBatchController/addCasePushBatchAndDetail",
                        type: 'POST',
                        data: JSON.stringify(dataJson),
                        async: false,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            layer.closeAll("loading");      // 关闭加载层
                            if (result.ret == "0") {
                                layer.msg(result.msg, {icon: 1, time: 2000, offset: calculationScrollTop()}, function (index) {
                                    layer.close(index);
                                    window.location.reload();
                                });
                            } else {
                                layer.msg(result.msg, {icon: 2, time: 2000, offset: calculationScrollTop()}, function (index) {
                                    layer.close(index);
                                });
                            }
                        },
                        error: function (data) {
                            var result = eval("(" + data + ")");
                            layer.msg(result.msg, {icon: 2, time: 2000, offset: calculationScrollTop()}, function () {
                                layer.close(index);
                            });
                        }
                    });
                };
                reader.readAsBinaryString(file); // 以二进制字符串形式读取文件内容
            } else {
                layer.msg("上载文件不存在", {icon: 2, time: 2000, offset: calculationScrollTop(), shade: [0.1, '#000']});
            }

        }

    </script>
    <style>

        .select2-dropdown {
            z-index: 19891099 !important;
        }

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
            border-right: 0;
            overflow: hidden;
            vertical-align: bottom;
            padding: 0px;
            display: inline-block;
        }
        .form-active > div > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
            display: flex;
        }
        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 70px 6px 30px;
            font-size: 17px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
            border-right: 1px solid #e7ecf1;
        }
        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }
        .li-blue {
            background: #0b94ea ;
            color: #fff;
        }
        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }
        td > a {
            display: inline-block;
            margin: 3px;
        }

        .td-overflow {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 100px;
        }
        .labelGroup span{
            margin-left: 5px;
            margin-right: 5px;
            background-color: #1676FF;
            color: white;
            border-radius: 2px;
        }

        .btn-bule {
            color: #FFFFFF;
            background-color: #1676FF;
            border-color: #1676FF;
            margin-left: 3%;
        }

        input[type="radio"] {
            position: relative !important;
            margin: 0px !important;
        }

        .row-other-class {
            margin: 20px;
            display: flex;
            align-items: center;
        }
    </style>
</head>
<body>

<#--上载推送-->
<div id="caseStatusContainer" style="display: none; width:100%; height:100%; padding: 10px 3%; background-color: white;">
    <div class="row row-other-class ratio-div" >
        <div class="col-sm-3 text-right">推送状态：</div>
        <div class="col-sm-8">
            <select class="form-control" id="caseStatus" name="caseStatus">
                <option value="">请选择</option>
                <option value="1">抄单查询</option>
                <option value="2">发起报案</option>
                <option value="3">估损同步</option>
                <option value="4">保司核损</option>
                <option value="5">保司核赔</option>
                <option value="6">影像同步</option>
                <option value="7">案件注销</option>
            </select>
        </div>
    </div>
    <div class="row text-center" style="margin: 20px;display: flex;justify-content: space-evenly;">
        <button class="btn btn-bule margin-right-10"
                onclick="uploadDataPush()">推送
        </button>
        <button class="btn btn-warning"
                onclick="javascript:layer.closeAll();">取消
        </button>
    </div>
</div>

<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>RB案件推送</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">RB案件管理</span></li>
                </ul>
            </div>
            <div class="portlet-body">

                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal" action="${ctx}/claimCaseController/piccClaimCaseList"
                      method="post" enctype="multipart/form-data">
                    <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="claimCaseNo" id="claimCaseNo"
                                               value="${piccClaimCaseVo.claimCaseNo}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案人姓名：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="applyName" id="applyName"
                                               value="${piccClaimCaseVo.applyName}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案人手机号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="applyMobile" id="applyMobile"
                                               value="${piccClaimCaseVo.applyMobile}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人姓名：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatName" id="treatName"
                                               value="${piccClaimCaseVo.treatName}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人身份证：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatIdNum" id="treatIdNum"
                                               value="${piccClaimCaseVo.treatIdNum}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险时间：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <div class="input-group date-picker input-daterange"
                                             data-date-format="yyyy-mm-dd" >
                                            <input type="text" class="form-control" name="treatDateStart" id="treatDateStart" autocomplete="off"
                                                   value="${piccClaimCaseVo.treatDateStart}">
                                            <span class="input-group-addon"><font style="vertical-align: inherit;"><font
                                                            style="vertical-align: inherit;"> 至 </font></font></span>
                                            <input type="text" class="form-control" name="treatDateEnd" id="treatDateEnd" autocomplete="off"
                                                   value="${piccClaimCaseVo.treatDateEnd}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案时间：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <div class="input-group date-picker input-daterange"
                                             data-date-format="yyyy-mm-dd" >
                                            <input type="text" class="form-control" name="startDateStart" id="startDateStart" autocomplete="off"
                                                   value="${piccClaimCaseVo.startDateStart}">
                                            <span class="input-group-addon"><font style="vertical-align: inherit;"><font
                                                            style="vertical-align: inherit;"> 至 </font></font></span>
                                            <input type="text" class="form-control" name="startDateEnd" id="startDateEnd" autocomplete="off"
                                                   value="${piccClaimCaseVo.startDateEnd}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">案件状态：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <select class="form-control select2-multiple"  name="status"
                                                id="status">
                                            <option value="">请选择</option>
                                            <#list claimStatusEnumMap.keySet() as key>
                                                <option value="${key}" <#if piccClaimCaseVo.status == key>selected</#if>>${claimStatusEnumMap.get(key)}</option>
                                            </#list>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">保司标识：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <select class="form-control select2-multiple"  name="insCode"
                                                id="insCode">
                                            <option value="">请选择</option>
                                            <option value="RB" <#if piccClaimCaseVo.insCode == "RB">selected</#if>>人保</option>
                                            <option value="HMRB" <#if piccClaimCaseVo.insCode == "HMRB">selected</#if>>盒马人保</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12" >
                                <div class="btn-group pull-right" style="margin-bottom: 10px;margin-right: 30px;">
                                    <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;">查询</button>
                                </div>
                            </div>
                            <#if piccClaimCaseVo.status != 4>
                            <div class="col-sm-12" >
                                <div class="pull-right uploadFilebox" style="margin-bottom: 10px;margin-right: 30px;">
                                    <a href="#" class="margin-right-10" onclick="exportPiccUploadTemplate()">模板导出</a>
                                    <div class="fileUpload btn btn-primary margin-right-10">
                                        <input id="uploadBtn" type="file" name="file" class="upload" accept=".xlsx"/>
                                    </div>
                                    <button type="button" id="subBtn" class="btn btn-default"
                                            onClick="uploadPushCenter();">上载推送
                                    </button>
                                </div>
                            </div>
                            </#if>
                            <div class="col-sm-12">
                                <div class="mt-element-list" style="display: none;" id="errorDiv">
                                    <div class="mt-list-container list-news" style="box-shadow: 2px 2px 7px rgba(0, 0, 0, .4);" id="containerUl">
                                        <div class="pull-right" style="margin-right: 10px; margin-top: -5px;">
                                            <a class="btn btn-warning" onclick="javascript:$('#errorDiv').hide();">关闭</a>
                                        </div>
                                        <div class="list-item-content formstyle1 enterset_con_l"
                                             style="padding-top: 10px; font-size: 16px; padding-bottom: 20px;" id="errorInfo">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-active">
                        <div>
                            <input type="hidden" name="sheetStatus" id="sheetStatus" value="${piccClaimCaseVo.sheetStatus}">
                            <ul>
                                <li class="li-default <#if piccClaimCaseVo.sheetStatus == 1>li-blue</#if>" onclick="sheetSwitch(1)">未决案件
                                </li>
                                <li class="li-default <#if piccClaimCaseVo.sheetStatus == 2>li-blue</#if>" onclick="sheetSwitch(2)">关闭案件
                                </li>
                            </ul>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th width="10%">报案号</th>
                        <th width="10%">出险人姓名</th>
                        <th width="10%">出险人身份证</th>
                        <th width="10%">估损金额</th>
                        <th width="13%" class="td-overflow">案件状态</th>
                        <th width="10%" class="td-overflow">保司状态</th>
                        <th width="13%">功能</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as claimCase>
                        <tr>
                            <td title="${claimCase.claimCaseNo}">${claimCase.claimCaseNo}</td>
                            <td title="${claimCase.treatName}">${claimCase.treatName}</td>
                            <td title="${claimCase.treatIdNum}">${claimCase.treatIdNum}</td>
                            <td title="${claimCase.appraisalAmount}">${claimCase.appraisalAmount}</td>
                            <td class="td-overflow" title="${claimStatusEnumMap.get(claimCase.status)}">
                                ${claimStatusEnumMap.get(claimCase.status)}
                            </td>
                            <td class="td-overflow" title="${piccClaimStatusEnumMap.get(claimCase.remark)}">
                                ${piccClaimStatusEnumMap.get(claimCase.remark)}
                            </td>
                            <td>
                                <a href="#" onclick="caseDetail('${claimCase.id}')">查看详情</a>
                                 <#if piccClaimCaseVo.sheetStatus == 1>
                                     <a onclick="piccClaimDataPush('${claimCase.claimCaseNo}', 1)">抄单查询</a>
                                     <a onclick="piccClaimDataPush('${claimCase.claimCaseNo}', 2)">发起报案</a>
                                     <a onclick="piccClaimDataPush('${claimCase.claimCaseNo}', 3)">估损同步</a>
                                     <a onclick="piccClaimDataPush('${claimCase.claimCaseNo}', 4)">保司核损</a>
                                     <a onclick="piccClaimDataPush('${claimCase.claimCaseNo}', 5)">保司核赔</a>
                                     <a onclick="piccClaimDataPush('${claimCase.claimCaseNo}', 6)">影像同步</a>
                                 </#if>
                                <#if piccClaimCaseVo.sheetStatus == 2>
                                    <a onclick="piccClaimDataPush('${claimCase.claimCaseNo}', 7)">案件注销</a>
                                </#if>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>