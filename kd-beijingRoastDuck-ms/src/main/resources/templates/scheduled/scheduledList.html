<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>定时任务管理</title>
    <!-- Custom styles for this template -->
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <script type="text/javascript">
        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        $(function () {
            //禁用无效按钮
            $('tr').each(function () {
                var _this = $(this);
                var s1 = _this.find('.unEffective').size();
                if (s1 > 0) {
                    _this.find('.doTask').prop('disabled', true);
                }
            })
        })

        //新增定时任务
        function addTask() {
            var openWindowWidth = $(document).width() * 0.6 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 100 ? 100 : $(window).height() / 5 - 20) + "px";
            layer.open({
                title: "新增定时任务",
                type: 2,
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: '${ctx}/scheduledController/form?operationType=new',
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        //立即执行
        function doTask(emu, scheduledId) {
            $(emu).prop("disabled", true);
            var layerIndex = top.layer.confirm('确认 立即 执行该任务吗？', {
                title: "<span style='color:red'>提示</span>",
                offset: "200px",
                btn: ['确定', '取消'] //按钮
            }, function () {
                $.ajax({
                    url: "${ctx}/scheduledController/doTask",
                    type: 'POST',
                    data: scheduledId,
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        var msg = result.msg;
                        if (result.ret === "0") {
                            msg = "执行成功！";
                        }
                        var tr1 = $('#' + scheduledId);
                        var type = tr1.find('.type').text();
                        if (type === "单次任务") {
                            tr1.find('.isEffective').removeClass('isEffective').addClass('unEffective').html('无效');
                        } else {
                            $(emu).prop("disabled", false);
                        }
                        layer.msg(msg,
                            {
                                time: 2000
                            });
                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                        $(emu).prop("disabled", false);
                        alert(result.msg);
                    }
                });
                top.layer.close(layerIndex);
            }, function () {
                top.layer.close(layerIndex);
                $(emu).prop("disabled", false);
            });
        }

        //切换状态
        function changeStatus(emu, scheduledId) {
            $(emu).prop("disabled", true);
            var tr1 = $('#' + scheduledId);
            var status = tr1.find('.status').html();
            var statusC = (status === "有效" ? "-1" : "1");
            var formData = new FormData();
            formData.append("id", scheduledId);
            formData.append("status", statusC);
            $.ajax({
                url: "${ctx}/scheduledController/changeStatus",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret === "0") {
                        var s2 = tr1.find('.isEffective').size();
                        if (s2 > 0) {
                            tr1.find('.isEffective').removeClass('isEffective').addClass('unEffective').html('无效');
                            tr1.find('.doTask').prop('disabled', true);
                        } else {
                            tr1.find('.unEffective').removeClass('unEffective').addClass('isEffective').html('有效');
                            tr1.find('.doTask').prop("disabled", false);
                        }
                    }
                    layer.msg(result.msg,
                        {
                            time: 2000
                        });
                    $(emu).prop("disabled", false);
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    $(emu).prop("disabled", false);
                    alert(result.msg);
                }
            });
        }

        //编辑定时任务
        function doEdit(scheduledId) {
            var openWindowWidth = $(document).width() * 0.6 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 100 ? 100 : $(window).height() / 5 - 20) + "px";
            layer.open({
                title: "编辑定时任务",
                type: 2,
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: '${ctx}/scheduledController/form?operationType=edit&scheduledId=' + scheduledId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        //查看日志
        function showLog(scheduledId) {
            var openWindowWidth = $(document).width() * 0.7 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 100 ? 100 : $(window).height() / 5 - 20) + "px";
            top.layer.open({
                title: "查看任务日志",
                type: 2,
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: '${ctx}/scheduledController/log?scheduledId=' + scheduledId,
                success: function (layero, index) {
                    top.layer.iframeAuto(index);
                }
            });
        }

    </script>
    <style type="text/css">
        .td-button {
            display: flex;
            flex-flow: wrap;
            justify-content: space-around;
        }

        .isEffective {
            color: green;
        }

        .unEffective {
            color: red;
        }

        .th-overflow {
            max-width: 5px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    </style>
</head>
<body>
<!-- BEGIN PAGE BASE CONTENT -->
<div class="container-fluid">
    <div class="row">
        <!-- BEGIN EXAMPLE TABLE PORTLET-->
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li>新职业平台 <i class="fa fa-circle"></i></li>
                    <li><span class="active">定时任务管理</span></li>
                </ul>
            </div>

            <div class="portlet-body">
                <form id="searchForm" class="form-horizontal" action="${ctx}/scheduledController/scheduledList"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="${page.pageNum}"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="col-sm-4">
                            <div class="row">
                                <div class="form-group">
                                    <label for="taskName" class="control-label col-sm-3"
                                           style="padding-right: 0px">任务名称：</label>
                                    <div class="col-sm-9" style="padding-left: 0px">
                                        <input type="text" class="form-control" id="taskName" name="taskName"
                                               value="${scheduled.taskName}" placeholder="请输入任务名称查询"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-actions">
                        <div class="col-sm-12">
                            <div class="row" style="margin-bottom: 10px;">
                                <button type="button" class="btn btn-success pull-right" onclick="addTask()">
                                    新增
                                </button>
                                <button type="submit" class="btn btn-info pull-right"
                                        style="margin-right:20px;">
                                    查询
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
                <table class="table table-bordered table-striped table-hover">
                    <thead>
                    <tr>
                        <th style="width:12%;">任务名称</th>
                        <th style="width:8%;">任务类型</th>
                        <th style="width:16%;">执行时间</th>
                        <th style="width:8%;">请求类型</th>
                        <th style="width:24%;">url地址 / 队列名称</th>
                        <th style="width:4%;">状态</th>
                        <th style="width:12%;">创建时间</th>
                        <th style="width:16%;">操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as scheduled>
                        <tr id="${scheduled.id}">
                            <td class="th-overflow" title="${scheduled.taskName}">${scheduled.taskName}</td>
                            <#if scheduled.type == 1 >
                                <td class="type">循环任务</td>
                            <#elseif scheduled.type == 2>
                                <td class="type">单次任务</td>
                            <#else>
                                <td class="type">未知任务</td>
                            </#if>
                            <td class="th-overflow"
                                    <#list cronTranslation.keySet() as key>
                                        <#if key == scheduled.id>
                                            title="${cronTranslation[key]}"
                                        </#if>
                                    </#list>
                            >
                                ${scheduled.cron}
                            </td>
                            <td>
                                <#if scheduled.reqType == 1>
                                    外网url请求
                                <#elseif scheduled.reqType == 2>
                                    内网url请求
                                <#elseif scheduled.reqType == 3>
                                    mq队列请求
                                <#else>
                                    未知请求
                                </#if>
                            </td>
                            <#if scheduled.reqType == 3>
                                <td class="th-overflow" title="${scheduled.queueName}">${scheduled.queueName}</td>
                            <#else>
                                <td class="th-overflow" title="${scheduled.url}">${scheduled.url}</td>
                            </#if>
                            <#if scheduled.status == 1>
                                <td class="status isEffective">有效</td>
                            <#elseif scheduled.status == -1>
                                <td class="status unEffective">无效</td>
                            <#else>
                                <td>未知</td>
                            </#if>

                            <td>${scheduled.createTime?string('yyyy-MM-dd HH:mm:ss')}</td>
                            <td>
                                <div class="col-sm-12 td-button">
                                    <button type="button" class="btn btn-xs btn-danger doTask"
                                            onclick="doTask(this,'${scheduled.id}');" style="margin-top:4px;">
                                        立即执行
                                    </button>
                                    <button type="button" class="btn btn-xs btn-warning"
                                            onclick="changeStatus(this,'${scheduled.id}')" style="margin-top:4px;">
                                        切换状态
                                    </button>
                                </div>
                                <div class="col-sm-12 td-button">
                                    <button type="button" class="btn btn-xs "
                                            onclick="doEdit('${scheduled.id}')" style="margin-top:4px;">
                                        编辑任务
                                    </button>
                                    <button type="button" class="btn btn-xs btn-default"
                                            onclick="showLog('${scheduled.id}')" style="margin-top:4px;">
                                        查看日志
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<!-- END PAGE BASE CONTENT -->
<@sc.pagination page=page />
</body>
</html>